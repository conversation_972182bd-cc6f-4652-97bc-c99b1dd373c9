#!/usr/bin/env python3
"""
AnimateDiff视频生成集成
作为Wanx 2.1的稳定替代方案
"""

import os
import sys
import time
import torch
from pathlib import Path
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class AnimateDiffGenerator:
    """AnimateDiff视频生成器"""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.pipeline = None
        self.model_loaded = False
        
    def load_model(self):
        """加载AnimateDiff模型"""
        if self.model_loaded:
            return True
            
        try:
            logger.info("加载AnimateDiff模型...")
            
            from diffusers import AnimateDiffPipeline, DDIMScheduler, MotionAdapter
            from diffusers.utils import export_to_video
            
            # 加载motion adapter
            adapter = MotionAdapter.from_pretrained(
                "guoyww/animatediff-motion-adapter-v1-5-2", 
                torch_dtype=torch.float16
            )
            
            # 加载pipeline
            model_id = "runwayml/stable-diffusion-v1-5"
            self.pipeline = AnimateDiffPipeline.from_pretrained(
                model_id,
                motion_adapter=adapter,
                torch_dtype=torch.float16,
                safety_checker=None,
                requires_safety_checker=False
            )
            
            # 优化设置
            self.pipeline.scheduler = DDIMScheduler.from_config(
                self.pipeline.scheduler.config
            )
            self.pipeline = self.pipeline.to(self.device)
            
            # 内存优化
            if self.device == "cuda":
                self.pipeline.enable_model_cpu_offload()
                self.pipeline.enable_vae_slicing()
                
            self.model_loaded = True
            logger.info("AnimateDiff模型加载成功")
            return True
            
        except Exception as e:
            logger.error(f"AnimateDiff模型加载失败: {e}")
            return False
    
    def generate_video(
        self,
        prompt,
        output_path,
        width=512,
        height=512,
        num_frames=16,
        guidance_scale=7.5,
        num_inference_steps=25,
        seed=None
    ):
        """生成视频"""
        try:
            if not self.load_model():
                return False, "模型加载失败", None
            
            logger.info(f"开始AnimateDiff视频生成: {prompt}")
            
            # 设置随机种子
            if seed is not None:
                torch.manual_seed(seed)
            
            # 生成视频
            start_time = time.time()
            
            video_frames = self.pipeline(
                prompt=prompt,
                num_frames=num_frames,
                guidance_scale=guidance_scale,
                num_inference_steps=num_inference_steps,
                height=height,
                width=width,
                generator=torch.Generator(device=self.device).manual_seed(seed) if seed else None
            ).frames[0]
            
            # 导出视频
            from diffusers.utils import export_to_video
            export_to_video(video_frames, str(output_path), fps=8)
            
            elapsed_time = time.time() - start_time
            
            if output_path.exists() and output_path.stat().st_size > 0:
                logger.info(f"AnimateDiff视频生成成功: {output_path} (耗时: {elapsed_time:.1f}秒)")
                return True, "生成成功", output_path
            else:
                logger.error("视频文件生成失败")
                return False, "视频文件生成失败", None
                
        except Exception as e:
            logger.error(f"AnimateDiff视频生成异常: {e}")
            return False, f"生成异常: {e}", None

def generate_animatediff_video(
    prompt,
    output_path,
    width=512,
    height=512,
    duration=2,
    fps=8,
    guidance_scale=7.5,
    num_inference_steps=25,
    seed=None
):
    """AnimateDiff视频生成接口"""
    try:
        # 计算帧数
        num_frames = min(duration * fps, 24)  # 限制最大帧数
        
        # 创建生成器
        generator = AnimateDiffGenerator()
        
        # 生成视频
        success, message, result_path = generator.generate_video(
            prompt=prompt,
            output_path=output_path,
            width=width,
            height=height,
            num_frames=num_frames,
            guidance_scale=guidance_scale,
            num_inference_steps=num_inference_steps,
            seed=seed
        )
        
        return success, message, result_path
        
    except Exception as e:
        logger.error(f"AnimateDiff接口异常: {e}")
        return False, f"接口异常: {e}", None

# Stable Video Diffusion集成
class SVDGenerator:
    """Stable Video Diffusion生成器"""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.pipeline = None
        self.model_loaded = False
    
    def load_model(self):
        """加载SVD模型"""
        if self.model_loaded:
            return True
            
        try:
            logger.info("加载Stable Video Diffusion模型...")
            
            from diffusers import StableVideoDiffusionPipeline
            from diffusers.utils import load_image, export_to_video
            
            self.pipeline = StableVideoDiffusionPipeline.from_pretrained(
                "stabilityai/stable-video-diffusion-img2vid-xt",
                torch_dtype=torch.float16,
                variant="fp16"
            )
            self.pipeline = self.pipeline.to(self.device)
            
            # 内存优化
            if self.device == "cuda":
                self.pipeline.enable_model_cpu_offload()
                
            self.model_loaded = True
            logger.info("SVD模型加载成功")
            return True
            
        except Exception as e:
            logger.error(f"SVD模型加载失败: {e}")
            return False
    
    def generate_video_from_image(
        self,
        image_path,
        output_path,
        num_frames=25,
        decode_chunk_size=8,
        seed=None
    ):
        """从图片生成视频"""
        try:
            if not self.load_model():
                return False, "模型加载失败", None
            
            from diffusers.utils import load_image, export_to_video
            from PIL import Image
            
            # 加载图片
            if isinstance(image_path, str):
                image = load_image(image_path)
            else:
                image = image_path
            
            # 调整图片尺寸
            image = image.resize((1024, 576))
            
            logger.info("开始SVD视频生成...")
            
            # 设置随机种子
            if seed is not None:
                torch.manual_seed(seed)
            
            # 生成视频
            start_time = time.time()
            
            frames = self.pipeline(
                image,
                decode_chunk_size=decode_chunk_size,
                num_frames=num_frames,
                generator=torch.Generator(device=self.device).manual_seed(seed) if seed else None
            ).frames[0]
            
            # 导出视频
            export_to_video(frames, str(output_path), fps=7)
            
            elapsed_time = time.time() - start_time
            
            if output_path.exists() and output_path.stat().st_size > 0:
                logger.info(f"SVD视频生成成功: {output_path} (耗时: {elapsed_time:.1f}秒)")
                return True, "生成成功", output_path
            else:
                logger.error("视频文件生成失败")
                return False, "视频文件生成失败", None
                
        except Exception as e:
            logger.error(f"SVD视频生成异常: {e}")
            return False, f"生成异常: {e}", None

if __name__ == "__main__":
    # 测试AnimateDiff
    output_path = Path("test_animatediff.mp4")
    
    print("测试AnimateDiff视频生成...")
    success, message, result_path = generate_animatediff_video(
        prompt="a cat running in a garden",
        output_path=output_path,
        width=512,
        height=512,
        duration=2,
        guidance_scale=7.5,
        num_inference_steps=20
    )
    
    if success:
        print(f"✅ AnimateDiff生成成功: {result_path}")
    else:
        print(f"❌ AnimateDiff生成失败: {message}")
