<template>
  <div class="model-training-container">
    <div class="header">
      <h1>🎓 本地模型训练中心</h1>
      <p>创建专业的智能体，如教师、医生、律师等</p>
    </div>

    <el-tabs v-model="activeTab" class="training-tabs">
      <!-- 专业智能体创建 -->
      <el-tab-pane label="创建专业智能体" name="professional">
        <div class="professional-agent-section">
          <el-card class="form-card">
            <template #header>
              <div class="card-header">
                <span>选择专业类型</span>
              </div>
            </template>
            
            <el-form :model="professionalForm" label-width="120px">
              <el-form-item label="专业类型">
                <el-select v-model="professionalForm.profession" placeholder="选择专业类型" @change="onProfessionChange">
                  <el-option
                    v-for="prof in availableProfessions"
                    :key="prof.id"
                    :label="prof.name"
                    :value="prof.id">
                    <span style="float: left">{{ prof.name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 13px">{{ prof.description }}</span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="自定义名称">
                <el-input v-model="professionalForm.custom_name" placeholder="例如：张老师、李医生" />
              </el-form-item>

              <el-form-item label="专业化领域">
                <el-select v-model="professionalForm.specialization" placeholder="选择专业化领域" clearable>
                  <el-option
                    v-for="spec in currentSpecializations"
                    :key="spec"
                    :label="spec"
                    :value="spec" />
                </el-select>
              </el-form-item>

              <el-form-item label="额外训练数据">
                <div class="training-data-section">
                  <div v-for="(item, index) in professionalForm.additional_training_data" :key="index" class="training-item">
                    <el-input
                      v-model="item.input"
                      placeholder="用户输入示例"
                      style="margin-bottom: 8px" />
                    <el-input
                      v-model="item.output"
                      type="textarea"
                      placeholder="期望的智能体回复"
                      :rows="2"
                      style="margin-bottom: 8px" />
                    <el-button @click="removeTrainingItem(index)" type="danger" size="small">删除</el-button>
                  </div>
                  <el-button @click="addTrainingItem" type="primary" size="small">添加训练示例</el-button>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="createProfessionalAgent" :loading="creating">
                  创建专业智能体
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 自定义微调 -->
      <el-tab-pane label="自定义微调" name="finetune">
        <div class="finetune-section">
          <el-card class="form-card">
            <template #header>
              <div class="card-header">
                <span>模型微调</span>
              </div>
            </template>

            <el-form :model="finetuneForm" label-width="120px">
              <el-form-item label="基础模型">
                <el-select v-model="finetuneForm.base_model" placeholder="选择基础模型">
                  <el-option
                    v-for="model in availableModels"
                    :key="model.name"
                    :label="model.name"
                    :value="model.name" />
                </el-select>
              </el-form-item>

              <el-form-item label="新模型名称">
                <el-input v-model="finetuneForm.model_name" placeholder="输入新模型名称" />
              </el-form-item>

              <el-form-item label="训练对话数据">
                <div class="conversation-data-section">
                  <div v-for="(conv, index) in finetuneForm.conversation_data" :key="index" class="conversation-item">
                    <div class="conversation-header">对话 {{ index + 1 }}</div>
                    <el-input
                      v-model="conv.input"
                      placeholder="用户输入"
                      style="margin-bottom: 8px" />
                    <el-input
                      v-model="conv.output"
                      type="textarea"
                      placeholder="智能体回复"
                      :rows="3"
                      style="margin-bottom: 8px" />
                    <el-button @click="removeConversation(index)" type="danger" size="small">删除对话</el-button>
                  </div>
                  <el-button @click="addConversation" type="primary" size="small">添加对话</el-button>
                </div>
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="startFineTuning" :loading="finetuning">
                  开始微调
                </el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 模型管理 -->
      <el-tab-pane label="模型管理" name="management">
        <div class="model-management-section">
          <el-card class="models-card">
            <template #header>
              <div class="card-header">
                <span>已训练的模型</span>
                <el-button @click="refreshModels" type="primary" size="small">刷新列表</el-button>
              </div>
            </template>

            <el-table :data="availableModels" style="width: 100%">
              <el-table-column prop="name" label="模型名称" />
              <el-table-column prop="size" label="大小" :formatter="formatSize" />
              <el-table-column prop="modified_at" label="修改时间" :formatter="formatDate" />
              <el-table-column label="操作" width="200">
                <template #default="scope">
                  <el-button @click="testModel(scope.row)" type="primary" size="small">测试</el-button>
                  <el-button @click="deleteModel(scope.row)" type="danger" size="small">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>

      <!-- 训练模板 -->
      <el-tab-pane label="训练模板" name="templates">
        <div class="templates-section">
          <el-row :gutter="20">
            <el-col :span="8" v-for="(template, key) in trainingTemplates" :key="key">
              <el-card class="template-card">
                <template #header>
                  <div class="card-header">
                    <span>{{ template.name }}</span>
                  </div>
                </template>
                <div class="template-examples">
                  <div v-for="(example, index) in template.examples" :key="index" class="example-item">
                    <div class="example-input">👤 {{ example.input }}</div>
                    <div class="example-output">🤖 {{ example.output }}</div>
                  </div>
                </div>
                <el-button @click="useTemplate(key)" type="primary" size="small" style="margin-top: 10px">
                  使用此模板
                </el-button>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 测试模型对话框 -->
    <el-dialog v-model="testDialogVisible" title="测试模型" width="600px">
      <el-form :model="testForm" label-width="100px">
        <el-form-item label="测试提示">
          <el-input v-model="testForm.test_prompt" type="textarea" :rows="3" placeholder="输入测试提示词" />
        </el-form-item>
        <el-form-item label="模型回复">
          <div class="test-response" v-if="testResponse">
            {{ testResponse }}
          </div>
          <div v-else class="no-response">暂无回复</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="testDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="runTest" :loading="testing">开始测试</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import axios from 'axios'

export default {
  name: 'ModelTraining',
  setup() {
    const activeTab = ref('professional')
    const creating = ref(false)
    const finetuning = ref(false)
    const testing = ref(false)
    const testDialogVisible = ref(false)
    const testResponse = ref('')

    // 专业智能体表单
    const professionalForm = reactive({
      profession: '',
      custom_name: '',
      specialization: '',
      additional_training_data: []
    })

    // 微调表单
    const finetuneForm = reactive({
      base_model: '',
      model_name: '',
      conversation_data: []
    })

    // 测试表单
    const testForm = reactive({
      model_name: '',
      test_prompt: ''
    })

    // 数据
    const availableProfessions = ref([])
    const currentSpecializations = ref([])
    const availableModels = ref([])
    const trainingTemplates = ref({})

    // 加载数据
    const loadProfessions = async () => {
      try {
        const response = await axios.get('/api/model-training/professions')
        if (response.data.success) {
          availableProfessions.value = response.data.professions
        }
      } catch (error) {
        console.error('加载专业类型失败:', error)
        ElMessage.error('加载专业类型失败')
      }
    }

    const loadModels = async () => {
      try {
        const response = await axios.get('/api/model-training/models')
        if (response.data.success) {
          availableModels.value = response.data.models
        }
      } catch (error) {
        console.error('加载模型列表失败:', error)
        ElMessage.error('加载模型列表失败')
      }
    }

    const loadTemplates = async () => {
      try {
        const response = await axios.get('/api/model-training/training-templates')
        if (response.data.success) {
          trainingTemplates.value = response.data.templates
        }
      } catch (error) {
        console.error('加载训练模板失败:', error)
        ElMessage.error('加载训练模板失败')
      }
    }

    // 事件处理
    const onProfessionChange = () => {
      const profession = availableProfessions.value.find(p => p.id === professionalForm.profession)
      if (profession) {
        currentSpecializations.value = profession.specializations || []
        professionalForm.specialization = ''
      }
    }

    const addTrainingItem = () => {
      professionalForm.additional_training_data.push({
        input: '',
        output: ''
      })
    }

    const removeTrainingItem = (index) => {
      professionalForm.additional_training_data.splice(index, 1)
    }

    const addConversation = () => {
      finetuneForm.conversation_data.push({
        input: '',
        output: ''
      })
    }

    const removeConversation = (index) => {
      finetuneForm.conversation_data.splice(index, 1)
    }

    const createProfessionalAgent = async () => {
      if (!professionalForm.profession) {
        ElMessage.warning('请选择专业类型')
        return
      }

      creating.value = true
      try {
        const response = await axios.post('/api/model-training/create-professional-agent', professionalForm)
        if (response.data.success) {
          ElMessage.success('专业智能体创建成功！')
          // 重置表单
          Object.assign(professionalForm, {
            profession: '',
            custom_name: '',
            specialization: '',
            additional_training_data: []
          })
          // 刷新模型列表
          await loadModels()
        } else {
          ElMessage.error(response.data.error || '创建失败')
        }
      } catch (error) {
        console.error('创建专业智能体失败:', error)
        ElMessage.error('创建失败，请检查网络连接')
      } finally {
        creating.value = false
      }
    }

    const startFineTuning = async () => {
      if (!finetuneForm.base_model || !finetuneForm.model_name) {
        ElMessage.warning('请填写基础模型和新模型名称')
        return
      }

      if (finetuneForm.conversation_data.length < 5) {
        ElMessage.warning('训练数据至少需要5个对话示例')
        return
      }

      finetuning.value = true
      try {
        const response = await axios.post('/api/model-training/fine-tune', finetuneForm)
        if (response.data.success) {
          ElMessage.success('模型微调完成！')
          // 重置表单
          Object.assign(finetuneForm, {
            base_model: '',
            model_name: '',
            conversation_data: []
          })
          // 刷新模型列表
          await loadModels()
        } else {
          ElMessage.error(response.data.error || '微调失败')
        }
      } catch (error) {
        console.error('模型微调失败:', error)
        ElMessage.error('微调失败，请检查网络连接')
      } finally {
        finetuning.value = false
      }
    }

    const testModel = (model) => {
      testForm.model_name = model.name
      testForm.test_prompt = ''
      testResponse.value = ''
      testDialogVisible.value = true
    }

    const runTest = async () => {
      if (!testForm.test_prompt) {
        ElMessage.warning('请输入测试提示词')
        return
      }

      testing.value = true
      try {
        const response = await axios.post('/api/model-training/test-model', testForm)
        if (response.data.success) {
          testResponse.value = response.data.response
        } else {
          ElMessage.error(response.data.error || '测试失败')
        }
      } catch (error) {
        console.error('测试模型失败:', error)
        ElMessage.error('测试失败，请检查网络连接')
      } finally {
        testing.value = false
      }
    }

    const deleteModel = async (model) => {
      try {
        await ElMessageBox.confirm('确定要删除这个模型吗？', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const response = await axios.delete(`/api/model-training/models/${model.name}`)
        if (response.data.success) {
          ElMessage.success('模型删除成功')
          await loadModels()
        } else {
          ElMessage.error(response.data.error || '删除失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除模型失败:', error)
          ElMessage.error('删除失败')
        }
      }
    }

    const refreshModels = () => {
      loadModels()
    }

    const useTemplate = (templateKey) => {
      const template = trainingTemplates.value[templateKey]
      if (template && template.examples) {
        professionalForm.additional_training_data = template.examples.map(example => ({
          input: example.input,
          output: example.output
        }))
        activeTab.value = 'professional'
        ElMessage.success('模板已应用到专业智能体创建')
      }
    }

    // 格式化函数
    const formatSize = (row, column, cellValue) => {
      if (!cellValue) return '-'
      const bytes = cellValue
      const sizes = ['B', 'KB', 'MB', 'GB']
      if (bytes === 0) return '0 B'
      const i = Math.floor(Math.log(bytes) / Math.log(1024))
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
    }

    const formatDate = (row, column, cellValue) => {
      if (!cellValue) return '-'
      return new Date(cellValue).toLocaleString()
    }

    // 初始化
    onMounted(() => {
      loadProfessions()
      loadModels()
      loadTemplates()
    })

    return {
      activeTab,
      creating,
      finetuning,
      testing,
      testDialogVisible,
      testResponse,
      professionalForm,
      finetuneForm,
      testForm,
      availableProfessions,
      currentSpecializations,
      availableModels,
      trainingTemplates,
      onProfessionChange,
      addTrainingItem,
      removeTrainingItem,
      addConversation,
      removeConversation,
      createProfessionalAgent,
      startFineTuning,
      testModel,
      runTest,
      deleteModel,
      refreshModels,
      useTemplate,
      formatSize,
      formatDate
    }
  }
}
</script>

<style scoped>
.model-training-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #409EFF;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 16px;
}

.training-tabs {
  margin-top: 20px;
}

.form-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.training-data-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.training-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
}

.conversation-data-section {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
}

.conversation-item {
  margin-bottom: 15px;
  padding: 10px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: white;
}

.conversation-header {
  font-weight: bold;
  margin-bottom: 8px;
  color: #409EFF;
}

.models-card {
  margin-bottom: 20px;
}

.template-card {
  height: 100%;
  margin-bottom: 20px;
}

.template-examples {
  max-height: 300px;
  overflow-y: auto;
}

.example-item {
  margin-bottom: 15px;
  padding: 10px;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.example-input {
  margin-bottom: 5px;
  color: #606266;
  font-size: 14px;
}

.example-output {
  color: #409EFF;
  font-size: 14px;
}

.test-response {
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #f5f7fa;
  min-height: 100px;
  white-space: pre-wrap;
}

.no-response {
  padding: 10px;
  text-align: center;
  color: #909399;
  font-style: italic;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .model-training-container {
    padding: 10px;
  }

  .template-card {
    margin-bottom: 15px;
  }
}
</style>
