{"metadata": {"total_size": 57153966336}, "weight_map": {"blocks.0.cross_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.cross_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.cross_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.cross_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.cross_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.cross_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.cross_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.cross_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.cross_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.cross_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.ffn.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.ffn.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.ffn.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.ffn.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.modulation": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.norm3.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.norm3.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.self_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.self_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.self_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.self_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.self_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.self_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.self_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.self_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.self_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.0.self_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.cross_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.cross_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.cross_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.cross_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.cross_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.cross_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.cross_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.cross_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.cross_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.cross_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.ffn.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.ffn.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.ffn.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.ffn.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.modulation": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.norm3.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.norm3.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.self_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.self_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.self_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.self_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.self_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.self_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.self_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.self_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.self_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.1.self_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.10.cross_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.cross_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.cross_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.cross_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.cross_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.cross_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.cross_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.cross_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.cross_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.cross_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.ffn.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.ffn.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.ffn.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.ffn.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.modulation": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.norm3.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.norm3.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.self_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.self_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.self_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.self_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.self_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.self_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.self_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.self_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.self_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.10.self_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.cross_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.cross_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.cross_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.cross_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.cross_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.cross_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.cross_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.cross_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.cross_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.cross_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.ffn.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.ffn.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.ffn.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.ffn.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.modulation": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.norm3.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.norm3.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.self_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.self_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.self_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.self_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.self_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.self_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.self_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.self_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.self_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.11.self_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.cross_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.cross_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.cross_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.cross_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.cross_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.cross_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.cross_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.cross_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.cross_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.cross_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.ffn.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.ffn.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.ffn.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.ffn.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.modulation": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.norm3.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.norm3.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.self_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.self_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.self_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.self_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.self_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.self_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.self_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.self_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.self_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.12.self_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.cross_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.cross_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.cross_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.cross_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.cross_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.cross_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.cross_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.cross_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.cross_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.cross_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.ffn.0.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.ffn.0.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.ffn.2.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.ffn.2.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.13.modulation": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.norm3.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.norm3.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.self_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.self_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.self_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.self_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.self_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.self_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.self_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.self_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.self_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.13.self_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.14.cross_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.cross_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.cross_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.cross_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.cross_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.cross_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.cross_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.cross_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.cross_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.cross_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.ffn.0.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.ffn.0.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.ffn.2.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.ffn.2.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.modulation": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.norm3.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.norm3.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.self_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.self_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.self_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.self_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.self_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.self_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.self_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.self_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.self_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.14.self_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.cross_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.cross_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.cross_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.cross_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.cross_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.cross_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.cross_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.cross_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.cross_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.cross_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.ffn.0.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.ffn.0.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.ffn.2.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.ffn.2.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.modulation": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.norm3.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.norm3.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.self_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.self_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.self_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.self_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.self_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.self_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.self_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.self_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.self_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.15.self_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.cross_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.cross_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.cross_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.cross_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.cross_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.cross_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.cross_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.cross_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.cross_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.cross_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.ffn.0.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.ffn.0.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.ffn.2.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.ffn.2.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.modulation": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.norm3.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.norm3.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.self_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.self_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.self_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.self_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.self_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.self_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.self_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.self_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.self_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.16.self_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.cross_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.cross_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.cross_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.cross_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.cross_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.cross_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.cross_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.cross_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.cross_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.cross_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.ffn.0.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.ffn.0.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.ffn.2.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.ffn.2.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.modulation": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.norm3.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.norm3.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.self_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.self_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.self_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.self_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.self_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.self_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.self_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.self_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.self_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.17.self_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.cross_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.cross_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.cross_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.cross_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.cross_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.cross_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.cross_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.cross_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.cross_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.cross_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.ffn.0.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.ffn.0.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.ffn.2.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.ffn.2.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.modulation": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.norm3.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.norm3.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.self_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.self_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.self_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.self_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.self_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.self_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.self_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.self_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.self_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.18.self_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.cross_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.cross_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.cross_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.cross_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.cross_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.cross_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.cross_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.cross_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.cross_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.cross_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.ffn.0.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.ffn.0.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.ffn.2.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.ffn.2.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.modulation": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.norm3.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.norm3.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.self_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.self_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.self_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.self_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.self_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.self_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.self_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.self_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.self_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.19.self_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.2.cross_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.cross_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.cross_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.cross_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.cross_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.cross_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.cross_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.cross_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.cross_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.cross_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.ffn.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.ffn.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.ffn.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.ffn.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.modulation": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.norm3.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.norm3.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.self_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.self_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.self_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.self_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.self_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.self_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.self_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.self_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.self_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.2.self_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.20.cross_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.cross_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.cross_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.cross_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.cross_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.cross_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.cross_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.cross_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.cross_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.cross_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.ffn.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.ffn.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.ffn.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.ffn.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.20.modulation": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.norm3.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.norm3.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.self_attn.k.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.self_attn.k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.self_attn.norm_k.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.self_attn.norm_q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.self_attn.o.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.self_attn.o.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.self_attn.q.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.self_attn.q.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.self_attn.v.bias": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.20.self_attn.v.weight": "diffusion_pytorch_model-00003-of-00006.safetensors", "blocks.21.cross_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.cross_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.cross_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.cross_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.cross_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.cross_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.cross_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.cross_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.cross_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.cross_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.ffn.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.ffn.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.ffn.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.ffn.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.modulation": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.norm3.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.norm3.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.self_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.self_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.self_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.self_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.self_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.self_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.self_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.self_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.self_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.21.self_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.cross_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.cross_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.cross_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.cross_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.cross_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.cross_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.cross_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.cross_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.cross_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.cross_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.ffn.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.ffn.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.ffn.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.ffn.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.modulation": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.norm3.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.norm3.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.self_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.self_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.self_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.self_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.self_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.self_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.self_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.self_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.self_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.22.self_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.cross_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.cross_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.cross_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.cross_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.cross_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.cross_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.cross_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.cross_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.cross_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.cross_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.ffn.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.ffn.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.ffn.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.ffn.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.modulation": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.norm3.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.norm3.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.self_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.self_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.self_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.self_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.self_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.self_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.self_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.self_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.self_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.23.self_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.cross_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.cross_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.cross_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.cross_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.cross_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.cross_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.cross_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.cross_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.cross_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.cross_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.ffn.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.ffn.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.ffn.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.ffn.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.modulation": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.norm3.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.norm3.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.self_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.self_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.self_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.self_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.self_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.self_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.self_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.self_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.self_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.24.self_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.cross_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.cross_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.cross_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.cross_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.cross_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.cross_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.cross_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.cross_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.cross_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.cross_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.ffn.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.ffn.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.ffn.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.ffn.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.modulation": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.norm3.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.norm3.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.self_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.self_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.self_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.self_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.self_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.self_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.self_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.self_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.self_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.25.self_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.cross_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.cross_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.cross_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.cross_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.cross_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.cross_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.cross_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.cross_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.cross_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.cross_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.ffn.0.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.ffn.0.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.ffn.2.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.ffn.2.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.modulation": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.norm3.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.norm3.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.self_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.self_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.self_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.self_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.self_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.self_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.self_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.self_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.self_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.26.self_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.cross_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.cross_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.cross_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.cross_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.cross_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.cross_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.cross_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.cross_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.cross_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.cross_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.ffn.0.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.ffn.0.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.ffn.2.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.ffn.2.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.27.modulation": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.norm3.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.norm3.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.self_attn.k.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.self_attn.k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.self_attn.norm_k.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.self_attn.norm_q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.self_attn.o.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.self_attn.o.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.self_attn.q.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.self_attn.q.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.self_attn.v.bias": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.27.self_attn.v.weight": "diffusion_pytorch_model-00004-of-00006.safetensors", "blocks.28.cross_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.cross_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.cross_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.cross_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.cross_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.cross_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.cross_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.cross_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.cross_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.cross_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.ffn.0.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.ffn.0.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.ffn.2.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.ffn.2.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.modulation": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.norm3.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.norm3.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.self_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.self_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.self_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.self_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.self_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.self_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.self_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.self_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.self_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.28.self_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.cross_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.cross_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.cross_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.cross_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.cross_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.cross_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.cross_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.cross_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.cross_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.cross_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.ffn.0.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.ffn.0.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.ffn.2.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.ffn.2.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.modulation": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.norm3.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.norm3.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.self_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.self_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.self_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.self_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.self_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.self_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.self_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.self_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.self_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.29.self_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.3.cross_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.cross_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.cross_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.cross_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.cross_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.cross_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.cross_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.cross_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.cross_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.cross_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.ffn.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.ffn.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.ffn.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.ffn.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.modulation": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.norm3.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.norm3.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.self_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.self_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.self_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.self_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.self_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.self_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.self_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.self_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.self_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.3.self_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.30.cross_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.cross_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.cross_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.cross_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.cross_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.cross_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.cross_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.cross_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.cross_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.cross_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.ffn.0.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.ffn.0.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.ffn.2.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.ffn.2.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.modulation": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.norm3.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.norm3.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.self_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.self_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.self_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.self_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.self_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.self_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.self_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.self_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.self_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.30.self_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.cross_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.cross_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.cross_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.cross_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.cross_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.cross_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.cross_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.cross_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.cross_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.cross_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.ffn.0.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.ffn.0.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.ffn.2.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.ffn.2.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.modulation": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.norm3.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.norm3.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.self_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.self_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.self_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.self_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.self_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.self_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.self_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.self_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.self_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.31.self_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.cross_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.cross_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.cross_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.cross_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.cross_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.cross_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.cross_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.cross_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.cross_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.cross_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.ffn.0.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.ffn.0.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.ffn.2.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.ffn.2.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.modulation": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.norm3.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.norm3.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.self_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.self_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.self_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.self_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.self_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.self_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.self_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.self_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.self_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.32.self_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.cross_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.cross_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.cross_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.cross_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.cross_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.cross_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.cross_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.cross_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.cross_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.cross_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.ffn.0.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.ffn.0.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.ffn.2.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.ffn.2.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.modulation": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.norm3.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.norm3.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.self_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.self_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.self_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.self_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.self_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.self_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.self_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.self_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.self_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.33.self_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.cross_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.cross_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.cross_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.cross_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.cross_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.cross_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.cross_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.cross_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.cross_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.cross_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.ffn.0.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.ffn.0.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.ffn.2.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.ffn.2.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.34.modulation": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.norm3.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.norm3.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.self_attn.k.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.self_attn.k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.self_attn.norm_k.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.self_attn.norm_q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.self_attn.o.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.self_attn.o.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.self_attn.q.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.self_attn.q.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.self_attn.v.bias": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.34.self_attn.v.weight": "diffusion_pytorch_model-00005-of-00006.safetensors", "blocks.35.cross_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.cross_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.cross_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.cross_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.cross_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.cross_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.cross_attn.q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.cross_attn.q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.cross_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.cross_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.ffn.0.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.ffn.0.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.ffn.2.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.ffn.2.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.modulation": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.norm3.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.norm3.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.self_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.self_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.self_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.self_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.self_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.self_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.self_attn.q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.self_attn.q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.self_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.35.self_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.cross_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.cross_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.cross_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.cross_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.cross_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.cross_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.cross_attn.q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.cross_attn.q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.cross_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.cross_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.ffn.0.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.ffn.0.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.ffn.2.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.ffn.2.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.modulation": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.norm3.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.norm3.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.self_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.self_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.self_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.self_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.self_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.self_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.self_attn.q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.self_attn.q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.self_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.36.self_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.cross_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.cross_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.cross_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.cross_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.cross_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.cross_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.cross_attn.q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.cross_attn.q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.cross_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.cross_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.ffn.0.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.ffn.0.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.ffn.2.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.ffn.2.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.modulation": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.norm3.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.norm3.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.self_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.self_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.self_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.self_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.self_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.self_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.self_attn.q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.self_attn.q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.self_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.37.self_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.cross_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.cross_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.cross_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.cross_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.cross_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.cross_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.cross_attn.q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.cross_attn.q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.cross_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.cross_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.ffn.0.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.ffn.0.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.ffn.2.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.ffn.2.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.modulation": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.norm3.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.norm3.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.self_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.self_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.self_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.self_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.self_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.self_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.self_attn.q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.self_attn.q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.self_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.38.self_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.cross_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.cross_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.cross_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.cross_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.cross_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.cross_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.cross_attn.q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.cross_attn.q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.cross_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.cross_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.ffn.0.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.ffn.0.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.ffn.2.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.ffn.2.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.modulation": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.norm3.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.norm3.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.self_attn.k.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.self_attn.k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.self_attn.norm_k.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.self_attn.norm_q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.self_attn.o.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.self_attn.o.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.self_attn.q.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.self_attn.q.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.self_attn.v.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.39.self_attn.v.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "blocks.4.cross_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.cross_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.cross_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.cross_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.cross_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.cross_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.cross_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.cross_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.cross_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.cross_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.ffn.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.ffn.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.ffn.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.ffn.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.modulation": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.norm3.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.norm3.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.self_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.self_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.self_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.self_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.self_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.self_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.self_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.self_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.self_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.4.self_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.cross_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.cross_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.cross_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.cross_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.cross_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.cross_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.cross_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.cross_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.cross_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.cross_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.ffn.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.ffn.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.ffn.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.ffn.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.modulation": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.norm3.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.norm3.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.self_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.self_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.self_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.self_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.self_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.self_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.self_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.self_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.self_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.5.self_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.cross_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.cross_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.cross_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.cross_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.cross_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.cross_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.cross_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.cross_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.cross_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.cross_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.ffn.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.ffn.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.ffn.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.ffn.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.6.modulation": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.norm3.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.norm3.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.self_attn.k.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.self_attn.k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.self_attn.norm_k.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.self_attn.norm_q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.self_attn.o.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.self_attn.o.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.self_attn.q.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.self_attn.q.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.self_attn.v.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.6.self_attn.v.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "blocks.7.cross_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.cross_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.cross_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.cross_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.cross_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.cross_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.cross_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.cross_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.cross_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.cross_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.ffn.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.ffn.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.ffn.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.ffn.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.modulation": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.norm3.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.norm3.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.self_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.self_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.self_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.self_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.self_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.self_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.self_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.self_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.self_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.7.self_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.cross_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.cross_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.cross_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.cross_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.cross_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.cross_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.cross_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.cross_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.cross_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.cross_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.ffn.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.ffn.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.ffn.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.ffn.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.modulation": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.norm3.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.norm3.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.self_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.self_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.self_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.self_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.self_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.self_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.self_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.self_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.self_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.8.self_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.cross_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.cross_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.cross_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.cross_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.cross_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.cross_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.cross_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.cross_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.cross_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.cross_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.ffn.0.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.ffn.0.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.ffn.2.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.ffn.2.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.modulation": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.norm3.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.norm3.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.self_attn.k.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.self_attn.k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.self_attn.norm_k.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.self_attn.norm_q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.self_attn.o.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.self_attn.o.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.self_attn.q.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.self_attn.q.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.self_attn.v.bias": "diffusion_pytorch_model-00002-of-00006.safetensors", "blocks.9.self_attn.v.weight": "diffusion_pytorch_model-00002-of-00006.safetensors", "head.head.bias": "diffusion_pytorch_model-00006-of-00006.safetensors", "head.head.weight": "diffusion_pytorch_model-00006-of-00006.safetensors", "head.modulation": "diffusion_pytorch_model-00006-of-00006.safetensors", "patch_embedding.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "patch_embedding.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "text_embedding.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "text_embedding.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "text_embedding.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "text_embedding.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_embedding.0.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_embedding.0.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_embedding.2.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_embedding.2.weight": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_projection.1.bias": "diffusion_pytorch_model-00001-of-00006.safetensors", "time_projection.1.weight": "diffusion_pytorch_model-00001-of-00006.safetensors"}}