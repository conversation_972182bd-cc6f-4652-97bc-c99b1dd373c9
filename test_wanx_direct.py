#!/usr/bin/env python3
"""
直接使用Wanx 2.1 Python API测试
"""

import os
import sys
import time
import warnings
from pathlib import Path

warnings.filterwarnings('ignore')

# 添加Wanx路径
wanx_path = Path("backend/storage/models/video_generation/wan/Wan2.1")
sys.path.insert(0, str(wanx_path))

def test_wanx_direct():
    """直接使用Wanx Python API测试"""
    print("=== 直接测试Wanx 2.1 Python API ===")
    
    try:
        # 导入Wanx模块
        import wan
        from wan.configs import WAN_CONFIGS
        print("✅ Wanx模块导入成功")
        
        # 检查CUDA
        import torch
        if not torch.cuda.is_available():
            print("❌ CUDA不可用")
            return False
        
        print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
        print(f"✅ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # 设置模型路径
        model_dir = wanx_path / "Wan2.1-T2V-1.3B"
        if not model_dir.exists():
            print(f"❌ 模型目录不存在: {model_dir}")
            return False
        
        print(f"✅ 模型目录存在: {model_dir}")
        
        # 创建配置
        config = WAN_CONFIGS['t2v-1.3B']
        print("✅ 配置创建成功")
        
        # 创建Wanx T2V实例
        print("🔄 创建Wanx T2V实例...")
        wan_t2v = wan.WanT2V(
            config=config,
            checkpoint_dir=str(model_dir),
            t5_cpu=True
        )
        print("✅ Wanx T2V实例创建成功")
        
        # 生成视频
        prompt = "cat"
        print(f"🎬 开始生成视频: {prompt}")
        
        start_time = time.time()
        
        # 使用最小参数
        video = wan_t2v.generate(
            input_prompt=prompt,
            size=(480, 832),  # 小尺寸
            frame_num=5,  # 最少帧数 (4n+1格式)
            shift=8.0,
            sampling_steps=5,  # 最少步数
            guide_scale=6.0,
            n_prompt="",
            seed=42,
            offload_model=True
        )
        
        end_time = time.time()
        
        print(f"✅ 视频生成完成！")
        print(f"⏱️  生成时间: {end_time - start_time:.2f}秒")
        print(f"📊 视频形状: {video.shape if hasattr(video, 'shape') else 'Unknown'}")
        
        # 保存视频
        output_path = "test_wanx_direct.mp4"
        if hasattr(video, 'shape'):
            # 保存视频文件
            import imageio
            if len(video.shape) == 4:  # (batch, frames, height, width) or (frames, height, width, channels)
                # 将tensor移到CPU并转换为numpy
                video_cpu = video.cpu().numpy()

                # 如果是(batch, frames, height, width)格式，取第一个batch
                if video_cpu.shape[0] == 3:  # 可能是RGB通道
                    # 重新排列为(frames, height, width, channels)
                    video_cpu = video_cpu.transpose(1, 2, 3, 0)

                # 确保值在0-255范围内
                if video_cpu.max() <= 1.0:
                    video_cpu = (video_cpu * 255).astype('uint8')
                else:
                    video_cpu = video_cpu.astype('uint8')

                imageio.mimsave(output_path, video_cpu, fps=8)
                print(f"✅ 视频已保存: {output_path}")
            else:
                print(f"⚠️  视频格式异常: {video.shape}")
        
        return True
        
    except Exception as e:
        print(f"❌ Wanx直接测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_minimal_config():
    """测试最小配置"""
    print("\n=== 测试最小配置 ===")
    
    try:
        import torch
        print(f"PyTorch版本: {torch.__version__}")
        print(f"CUDA版本: {torch.version.cuda}")
        print(f"CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"GPU: {torch.cuda.get_device_name(0)}")
            print(f"GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
            
            # 清理GPU缓存
            torch.cuda.empty_cache()
            print("✅ GPU缓存已清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 最小配置测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始Wanx 2.1直接测试")
    
    # 测试最小配置
    minimal_ok = test_minimal_config()
    
    if minimal_ok:
        print("\n🎯 进行直接API测试...")
        direct_ok = test_wanx_direct()
        
        if direct_ok:
            print("\n🎉 Wanx 2.1直接测试成功！")
        else:
            print("\n❌ Wanx 2.1直接测试失败")
    else:
        print("\n❌ 基础配置测试失败")
    
    print("\n=== 测试完成 ===")
