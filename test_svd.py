#!/usr/bin/env python3
"""
测试Stable Video Diffusion - 更适合8GB显存的视频生成模型
"""

import torch
import os
import time
from pathlib import Path
import numpy as np
from PIL import Image

def test_svd():
    """测试Stable Video Diffusion"""
    print("=== 测试Stable Video Diffusion ===")
    
    try:
        # 检查CUDA
        if not torch.cuda.is_available():
            print("❌ CUDA不可用")
            return False
        
        print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
        print(f"✅ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # 清理GPU缓存
        torch.cuda.empty_cache()
        print("✅ GPU缓存已清理")
        
        # 导入SVD
        from diffusers import StableVideoDiffusionPipeline
        from diffusers.utils import load_image, export_to_video
        print("✅ SVD导入成功")
        
        print("🔄 加载SVD模型...")
        
        # 加载SVD pipeline
        pipe = StableVideoDiffusionPipeline.from_pretrained(
            "stabilityai/stable-video-diffusion-img2vid-xt", 
            torch_dtype=torch.float16, 
            variant="fp16"
        )
        
        # 启用内存优化
        pipe.enable_model_cpu_offload()
        pipe.enable_vae_slicing()
        
        print("✅ SVD模型加载成功")
        
        # 创建测试图片
        test_image = create_test_image()
        
        print("🎬 开始生成视频...")
        
        start_time = time.time()
        
        # 生成视频
        generator = torch.manual_seed(42)
        frames = pipe(
            test_image, 
            decode_chunk_size=2,  # 减少内存使用
            generator=generator,
            num_frames=14,  # 较少帧数
        ).frames[0]
        
        end_time = time.time()
        
        # 保存视频
        output_path = "svd_test.mp4"
        export_to_video(frames, output_path, fps=7)
        
        print(f"✅ SVD视频生成成功: {output_path}")
        print(f"⏱️  生成时间: {end_time - start_time:.2f}秒")
        print(f"📊 帧数: {len(frames)}")
        
        # 检查文件大小
        file_size = os.path.getsize(output_path)
        print(f"📊 文件大小: {file_size / 1024:.1f} KB")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请安装: pip install diffusers[torch] transformers accelerate")
        return False
    except Exception as e:
        print(f"❌ SVD测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_test_image():
    """创建测试图片"""
    # 创建一个简单的测试图片
    image = Image.new('RGB', (512, 512), color='lightblue')
    
    # 可以添加一些简单的内容
    from PIL import ImageDraw, ImageFont
    draw = ImageDraw.Draw(image)
    
    # 画一个简单的场景
    draw.ellipse([200, 200, 300, 300], fill='orange')  # 太阳
    draw.rectangle([100, 400, 400, 500], fill='green')  # 草地
    
    return image

def test_simple_video_generation():
    """简单的视频生成测试"""
    print("\n=== 简单视频生成测试 ===")
    
    try:
        import cv2
        import numpy as np
        
        print("🔄 生成简单动画视频...")
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter('simple_animation.mp4', fourcc, 10.0, (512, 512))
        
        # 生成30帧简单动画
        for i in range(30):
            # 创建帧
            frame = np.zeros((512, 512, 3), dtype=np.uint8)
            frame[:] = (50, 100, 150)  # 背景色
            
            # 添加移动的圆
            center_x = int(256 + 100 * np.sin(i * 0.2))
            center_y = int(256 + 50 * np.cos(i * 0.2))
            cv2.circle(frame, (center_x, center_y), 30, (255, 255, 0), -1)
            
            # 添加文字
            cv2.putText(frame, f'Frame {i+1}', (10, 30), 
                       cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
            
            out.write(frame)
        
        out.release()
        
        print("✅ 简单动画生成成功: simple_animation.mp4")
        print("📊 30帧, 10 FPS, 3秒时长")
        
        return True
        
    except Exception as e:
        print(f"❌ 简单动画生成失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始视频生成测试")
    
    print("选择测试方案:")
    print("1. Stable Video Diffusion (需要下载模型)")
    print("2. 简单动画生成 (本地生成)")
    
    # 默认使用简单动画
    mode = "2"
    
    if mode == "1":
        print("\n🎯 测试Stable Video Diffusion...")
        success = test_svd()
    else:
        print("\n⚡ 生成简单动画...")
        success = test_simple_video_generation()
    
    if success:
        print("\n🎉 视频生成成功！")
    else:
        print("\n❌ 视频生成失败")
    
    print("\n=== 测试完成 ===")
