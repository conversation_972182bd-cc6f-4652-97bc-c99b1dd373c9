<template>
  <div class="agent-studio">
    <!-- 头部导航 -->
    <div class="studio-header">
      <div class="header-left">
        <button @click="goBack" class="back-btn">← 返回</button>
        <h1>🤖 {{ agentConfig.name || '智能体工作室' }}</h1>
        <span class="subtitle">{{ agentConfig.name ? '编辑智能体配置' : '构建真正的AI智能体' }}</span>
      </div>
      <div class="header-right">
        <button @click="openModelTraining" class="btn-training">🎓 模型训练</button>
        <button @click="previewAgent" class="btn-preview">🔍 预览</button>
        <button @click="testAgent" class="btn-test">🧪 测试</button>
        <button @click="packageAgent" class="btn-package">📦 打包单机版</button>
        <button @click="saveAgent" :disabled="saving" class="btn-save">
          {{ saving ? '保存中...' : '💾 保存智能体' }}
        </button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="studio-content">
      <!-- 左侧配置面板 -->
      <div class="config-panel">
        <div class="panel-tabs">
          <button 
            v-for="tab in configTabs" 
            :key="tab.id"
            :class="['tab-btn', { active: activeTab === tab.id }]"
            @click="activeTab = tab.id"
          >
            {{ tab.icon }} {{ tab.name }}
          </button>
        </div>

        <!-- 基本信息 -->
        <div v-if="activeTab === 'basic'" class="tab-content">
          <div class="section">
            <h3>基本信息</h3>
            <div class="form-group">
              <label>智能体名称</label>
              <input v-model="agentConfig.name" type="text" placeholder="给你的智能体起个名字" />
            </div>
            <div class="form-group">
              <label>描述</label>
              <textarea v-model="agentConfig.description" placeholder="描述智能体的功能和用途"></textarea>
            </div>
            <div class="form-group">
              <label>能力模板</label>
              <select v-model="selectedTemplate" @change="applyTemplate">
                <option value="">选择预设模板</option>
                <option v-for="(template, key) in capabilityTemplates" :key="key" :value="key">
                  {{ template.name }} - {{ template.description }}
                </option>
              </select>
            </div>
          </div>
        </div>

        <!-- 工具配置 -->
        <div v-if="activeTab === 'tools'" class="tab-content">
          <div class="section">
            <h3>工具配置</h3>
            <p class="section-desc">为智能体配置可用的工具，让它能够执行实际任务</p>
            
            <div class="tool-categories">
              <div v-for="category in toolCategories" :key="category" class="category-section">
                <h4>{{ category }}</h4>
                <div class="tool-grid">
                  <div 
                    v-for="tool in getToolsByCategory(category)" 
                    :key="tool.type"
                    :class="['tool-card', { selected: isToolSelected(tool.type) }]"
                    @click="toggleTool(tool.type)"
                  >
                    <div class="tool-icon">{{ tool.icon }}</div>
                    <div class="tool-name">{{ tool.name }}</div>
                    <div class="tool-desc">{{ tool.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 知识库配置 -->
        <div v-if="activeTab === 'knowledge'" class="tab-content">
          <!-- 知识库说明组件 -->
          <KnowledgeBaseExplanation />

          <div class="section">
            <div class="section-header">
              <div class="section-title">
                <h3>📚 知识库管理</h3>
                <p class="section-desc">为智能体添加专业知识，支持文档、数据表格等多种格式</p>
              </div>
              <div class="section-actions">
                <button @click="showUploadDialog = true" class="btn-primary">
                  <span class="btn-icon">📄</span>
                  上传文档
                </button>
                <button @click="showCreateKB = true" class="btn-outline">
                  <span class="btn-icon">➕</span>
                  创建知识库
                </button>
              </div>
            </div>

            <div class="knowledge-grid">
              <div v-for="kb in agentConfig.knowledge_bases" :key="kb.id" class="kb-card">
                <div class="kb-card-header">
                  <div class="kb-icon-large">{{ getKBIcon(kb.type) }}</div>
                  <div class="kb-type-badge">{{ getKBTypeName(kb.type) }}</div>
                </div>
                <div class="kb-card-body">
                  <h4 class="kb-title">{{ kb.name }}</h4>
                  <p class="kb-description">{{ kb.description || '暂无描述' }}</p>
                  <div class="kb-stats">
                    <div class="stat-item">
                      <span class="stat-icon">📄</span>
                      <span class="stat-text">{{ kb.document_count || 0 }} 个文档</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-icon">📅</span>
                      <span class="stat-text">{{ formatDate(kb.created_at) }}</span>
                    </div>
                  </div>
                </div>
                <div class="kb-card-actions">
                  <button @click="editKB(kb)" class="action-btn edit-btn" title="编辑">
                    <span>✏️</span>
                  </button>
                  <button @click="removeKB(kb.id)" class="action-btn delete-btn" title="删除">
                    <span>🗑️</span>
                  </button>
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="agentConfig.knowledge_bases.length === 0" class="empty-state-card">
                <div class="empty-content">
                  <div class="empty-icon">📚</div>
                  <h4>还没有知识库</h4>
                  <p>创建知识库或上传文档来增强智能体的专业能力</p>
                  <div class="empty-actions">
                    <button @click="showCreateKB = true" class="btn-primary">
                      <span class="btn-icon">➕</span>
                      创建第一个知识库
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 记忆配置 -->
        <div v-if="activeTab === 'memory'" class="tab-content">
          <div class="section">
            <h3>记忆配置</h3>
            <p class="section-desc">配置智能体的记忆能力，让它能够学习和记住</p>
            
            <div class="memory-types">
              <div v-for="(memoryType, key) in memoryTypes" :key="key" class="memory-option">
                <label class="checkbox-label">
                  <input 
                    type="checkbox" 
                    :value="key"
                    v-model="agentConfig.memory_types"
                  />
                  <span class="checkmark"></span>
                  <div class="memory-info">
                    <div class="memory-name">{{ getMemoryTypeName(key) }}</div>
                    <div class="memory-desc">{{ getMemoryTypeDesc(key) }}</div>
                  </div>
                </label>
              </div>
            </div>
          </div>
        </div>

        <!-- 工作流配置 -->
        <div v-if="activeTab === 'workflow'" class="tab-content">
          <div class="section">
            <h3>工作流配置</h3>
            <p class="section-desc">设计智能体的工作流程，处理复杂任务</p>
            
            <div class="workflow-toggle">
              <label class="switch">
                <input type="checkbox" v-model="agentConfig.workflow_enabled" />
                <span class="slider"></span>
              </label>
              <span>启用工作流</span>
            </div>

            <div v-if="agentConfig.workflow_enabled" class="workflow-designer">
              <div class="workflow-canvas">
                <div class="canvas-placeholder">
                  🔧 工作流设计器
                  <p>拖拽节点来设计智能体的工作流程</p>
                  <button @click="openWorkflowDesigner" class="btn-primary">
                    打开工作流设计器
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧预览面板 -->
      <div class="preview-panel">
        <div class="preview-header">
          <h3>🔍 智能体预览</h3>
          <button @click="testAgent" class="btn-test">🧪 测试对话</button>
        </div>
        
        <div class="agent-preview">
          <div class="agent-card">
            <div class="agent-avatar">🤖</div>
            <div class="agent-info">
              <h4>{{ agentConfig.name || '未命名智能体' }}</h4>
              <p>{{ agentConfig.description || '暂无描述' }}</p>
            </div>
          </div>

          <div class="capabilities-preview">
            <div class="capability-section">
              <h5>🛠️ 可用工具 ({{ agentConfig.tools.length }})</h5>
              <div class="capability-tags">
                <span v-for="tool in agentConfig.tools" :key="tool.type" class="capability-tag">
                  {{ getToolConfig(tool.type)?.name || tool.type }}
                </span>
              </div>
            </div>

            <div class="capability-section">
              <h5>📚 知识库 ({{ agentConfig.knowledge_bases.length }})</h5>
              <div class="capability-tags">
                <span v-for="kb in agentConfig.knowledge_bases" :key="kb.id" class="capability-tag">
                  {{ kb.name }}
                </span>
              </div>
            </div>

            <div class="capability-section">
              <h5>🧠 记忆类型 ({{ agentConfig.memory_types.length }})</h5>
              <div class="capability-tags">
                <span v-for="memoryType in agentConfig.memory_types" :key="memoryType" class="capability-tag">
                  {{ getMemoryTypeName(memoryType) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建知识库对话框 -->
    <div v-if="showCreateKB" class="modal-overlay" @click="showCreateKB = false">
      <div class="modal-content" @click.stop>
        <h3>🗂️ 创建知识库</h3>
        <div class="kb-form">
          <div class="form-group">
            <label>知识库名称</label>
            <input v-model="newKBName" type="text" placeholder="输入知识库名称" class="form-input" />
          </div>

          <div class="form-group">
            <label>知识库类型</label>
            <select v-model="newKBType" class="form-select">
              <option value="document">文档库</option>
              <option value="qa">问答库</option>
              <option value="vocabulary">词汇库</option>
              <option value="conversation">对话库</option>
              <option value="custom">自定义</option>
            </select>
          </div>

          <div class="form-group">
            <label>描述</label>
            <textarea v-model="newKBDescription" placeholder="描述知识库的用途和内容" class="form-textarea"></textarea>
          </div>
        </div>
        <div class="modal-actions">
          <button @click="showCreateKB = false" class="btn-secondary">取消</button>
          <button @click="createKnowledgeBase" :disabled="!newKBName" class="btn-primary">创建</button>
        </div>
      </div>
    </div>

    <!-- 上传文档对话框 -->
    <div v-if="showUploadDialog" class="modal-overlay" @click="showUploadDialog = false">
      <div class="modal-content" @click.stop>
        <h3>📄 上传文档</h3>
        <div class="upload-area">
          <!-- 知识库选择 -->
          <div class="form-group">
            <label>选择知识库：</label>
            <select v-model="selectedUploadKB" class="form-select">
              <option value="">创建新的文档库</option>
              <option v-for="kb in agentConfig.knowledge_bases.filter(kb => kb.type === 'document')"
                      :key="kb.id"
                      :value="kb.id">
                {{ kb.name }} ({{ kb.document_count || 0 }}个文档)
              </option>
            </select>
          </div>

          <input type="file" multiple @change="handleFileUpload" accept=".pdf,.doc,.docx,.txt,.md,.json,.xlsx,.xls,.csv" />
          <p>支持 PDF, DOC, DOCX, TXT, MD, JSON, Excel (XLSX/XLS), CSV 等格式</p>

          <!-- 文件列表 -->
          <div v-if="uploadedFiles.length > 0" class="file-list">
            <h4>已选择的文件：</h4>
            <div v-for="file in uploadedFiles" :key="file.name" class="file-item">
              <span class="file-icon">📄</span>
              <span class="file-name">{{ file.name }}</span>
              <span class="file-size">({{ (file.size / 1024).toFixed(1) }}KB)</span>
              <span :class="['file-status', file.status]">{{ getFileStatusText(file.status) }}</span>
            </div>
          </div>
        </div>
        <div class="modal-actions">
          <button @click="showUploadDialog = false" class="btn-secondary">取消</button>
          <button @click="uploadDocuments" :disabled="uploadedFiles.length === 0" class="btn-primary">
            上传 {{ uploadedFiles.length }} 个文件
          </button>
        </div>
      </div>
    </div>

    <!-- 智能体预览对话框 -->
    <div v-if="showPreviewDialog" class="modal-overlay" @click="showPreviewDialog = false">
      <div class="modal-content large" @click.stop>
        <h3>🔍 智能体预览</h3>
        <div class="preview-content">
          <div class="preview-section">
            <h4>基本信息</h4>
            <div class="preview-item">
              <strong>名称：</strong>{{ agentConfig.name || '未设置' }}
            </div>
            <div class="preview-item">
              <strong>描述：</strong>{{ agentConfig.description || '未设置' }}
            </div>
          </div>

          <div class="preview-section">
            <h4>工具能力 ({{ agentConfig.tools.length }})</h4>
            <div class="preview-tags">
              <span v-for="tool in agentConfig.tools" :key="tool.type" class="preview-tag">
                {{ getToolConfig(tool.type)?.name || tool.type }}
              </span>
            </div>
          </div>

          <div class="preview-section">
            <h4>知识库 ({{ agentConfig.knowledge_bases.length }})</h4>
            <div class="preview-tags">
              <span v-for="kb in agentConfig.knowledge_bases" :key="kb.id" class="preview-tag">
                {{ getKBIcon(kb.type) }} {{ kb.name }}
              </span>
            </div>
          </div>

          <div class="preview-section">
            <h4>记忆类型 ({{ agentConfig.memory_types.length }})</h4>
            <div class="preview-tags">
              <span v-for="memoryType in agentConfig.memory_types" :key="memoryType" class="preview-tag">
                {{ getMemoryTypeName(memoryType) }}
              </span>
            </div>
          </div>

          <div class="preview-section">
            <h4>工作流</h4>
            <div class="preview-item">
              <strong>状态：</strong>
              <span :class="['workflow-status', agentConfig.workflow_enabled ? 'enabled' : 'disabled']">
                {{ agentConfig.workflow_enabled ? '已启用' : '未启用' }}
              </span>
            </div>
          </div>
        </div>
        <div class="modal-actions">
          <button @click="showPreviewDialog = false" class="btn-secondary">关闭</button>
          <button @click="testAgent" class="btn-primary">🧪 测试对话</button>
        </div>
      </div>
    </div>

    <!-- 工作流设计器对话框 -->
    <div v-if="showWorkflowDesigner" class="modal-overlay" @click="showWorkflowDesigner = false">
      <div class="modal-content extra-large" @click.stop>
        <h3>🔄 工作流设计器</h3>
        <div class="workflow-designer">
          <div class="workflow-toolbar">
            <div class="node-palette">
              <h4>节点类型</h4>
              <button @click="addWorkflowNode('start')" class="node-btn">🚀 开始</button>
              <button @click="addWorkflowNode('tool_call')" class="node-btn">🛠️ 工具调用</button>
              <button @click="addWorkflowNode('condition')" class="node-btn">❓ 条件判断</button>
              <button @click="addWorkflowNode('memory_save')" class="node-btn">💾 保存记忆</button>
              <button @click="addWorkflowNode('memory_recall')" class="node-btn">🧠 回忆记忆</button>
              <button @click="addWorkflowNode('end')" class="node-btn">🏁 结束</button>
            </div>
            <div class="workflow-actions">
              <button @click="clearWorkflow" class="btn-secondary">清空</button>
              <button @click="saveWorkflow" class="btn-primary">保存工作流</button>
            </div>
          </div>

          <div class="workflow-canvas">
            <div v-if="workflowNodes.length === 0" class="empty-canvas">
              <p>点击左侧按钮添加节点开始设计工作流</p>
            </div>
            <div v-else class="workflow-nodes">
              <div
                v-for="node in workflowNodes"
                :key="node.id"
                class="workflow-node"
                :style="{ left: node.x + 'px', top: node.y + 'px' }"
              >
                <div class="node-header">
                  <span class="node-type">{{ node.config.name }}</span>
                  <button @click="removeNode(node.id)" class="node-remove">×</button>
                </div>
                <div class="node-content">
                  <small>{{ node.type }}</small>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-actions">
          <button @click="showWorkflowDesigner = false" class="btn-secondary">关闭</button>
          <button @click="saveWorkflow" class="btn-primary">保存工作流</button>
        </div>
      </div>
    </div>

    <!-- 打包对话框 -->
    <div v-if="showPackageDialog" class="modal-overlay" @click="showPackageDialog = false">
      <div class="modal-content large" @click.stop>
        <h3>📦 打包智能体为单机版</h3>

        <div v-if="!packaging && !packageResult" class="package-options">
          <div class="package-section">
            <h4>📋 打包配置</h4>
            <div class="package-form">
              <div class="form-group">
                <label>包名称</label>
                <input v-model="packageName" type="text" :placeholder="agentConfig.name + '_offline'" />
              </div>

              <div class="form-group">
                <label>目标平台</label>
                <div class="checkbox-group">
                  <label><input type="checkbox" v-model="includeLinux" /> Linux/Mac</label>
                  <label><input type="checkbox" v-model="includeWindows" /> Windows</label>
                  <label><input type="checkbox" v-model="includeEmbedded" /> 嵌入式设备</label>
                </div>
              </div>

              <div class="form-group">
                <label>包含组件</label>
                <div class="checkbox-group">
                  <label><input type="checkbox" v-model="includeDatabase" checked disabled /> 本地数据库</label>
                  <label><input type="checkbox" v-model="includeModels" checked disabled /> AI模型</label>
                  <label><input type="checkbox" v-model="includeRuntime" checked disabled /> 运行时环境</label>
                  <label><input type="checkbox" v-model="includeDocumentation" checked /> 使用文档</label>
                </div>
              </div>

              <div class="form-group">
                <label>优化选项</label>
                <div class="checkbox-group">
                  <label><input type="checkbox" v-model="optimizeSize" /> 体积优化</label>
                  <label><input type="checkbox" v-model="optimizeSpeed" /> 速度优化</label>
                  <label><input type="checkbox" v-model="enableLogging" /> 启用日志</label>
                </div>
              </div>
            </div>
          </div>

          <div class="package-section">
            <h4>🎯 部署说明</h4>
            <div class="deployment-info">
              <div class="info-item">
                <strong>📱 移动设备:</strong> 支持Android/iOS，需要4GB+内存
              </div>
              <div class="info-item">
                <strong>🖥️ 桌面系统:</strong> Windows/Mac/Linux，推荐8GB+内存
              </div>
              <div class="info-item">
                <strong>🤖 嵌入式设备:</strong> ARM处理器，2GB+内存，支持玩具狗等设备
              </div>
              <div class="info-item">
                <strong>☁️ 云端更新:</strong> 可选联网更新，支持离线运行
              </div>
            </div>
          </div>
        </div>

        <div v-if="packaging" class="packaging-progress">
          <div class="progress-header">
            <h4>🚀 正在打包智能体...</h4>
            <div class="progress-spinner"></div>
          </div>
          <div class="progress-steps">
            <div class="progress-step active">📝 准备配置文件</div>
            <div class="progress-step active">💾 创建本地数据库</div>
            <div class="progress-step active">🧠 优化AI模型</div>
            <div class="progress-step">📦 生成安装包</div>
            <div class="progress-step">✅ 完成打包</div>
          </div>
        </div>

        <div v-if="packageResult" class="package-result">
          <div class="result-header">
            <h4>🎉 打包完成！</h4>
          </div>
          <div class="result-info">
            <div class="result-item">
              <strong>包名称:</strong> {{ packageResult.package_name }}
            </div>
            <div class="result-item">
              <strong>文件大小:</strong> {{ packageResult.file_size || '约50MB' }}
            </div>
            <div class="result-item">
              <strong>创建时间:</strong> {{ packageResult.created_at }}
            </div>
            <div class="result-item">
              <strong>支持平台:</strong>
              <span class="platform-tags">
                <span v-if="includeLinux" class="platform-tag">Linux/Mac</span>
                <span v-if="includeWindows" class="platform-tag">Windows</span>
                <span v-if="includeEmbedded" class="platform-tag">嵌入式</span>
              </span>
            </div>
          </div>

          <div class="deployment-guide">
            <h5>📋 部署指南</h5>
            <ol>
              <li>下载打包文件并解压</li>
              <li>运行对应平台的部署脚本</li>
              <li>启动智能体服务</li>
              <li>开始使用离线AI助手</li>
            </ol>
          </div>
        </div>

        <div class="modal-actions">
          <button @click="showPackageDialog = false" class="btn-secondary">关闭</button>
          <button v-if="!packaging && !packageResult" @click="startPackaging({
            name: packageName || agentConfig.name + '_offline',
            platforms: { linux: includeLinux, windows: includeWindows, embedded: includeEmbedded },
            components: { database: includeDatabase, models: includeModels, runtime: includeRuntime, docs: includeDocumentation },
            optimization: { size: optimizeSize, speed: optimizeSpeed, logging: enableLogging }
          })" class="btn-primary">
            🚀 开始打包
          </button>
          <button v-if="packageResult" @click="downloadPackage" class="btn-primary">
            📥 下载安装包
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  AGENT_CAPABILITY_TEMPLATES,
  getToolConfig,
  getToolCategories,
  getToolsByCategory,
  MEMORY_TYPES,
  validateAgentConfig
} from '@/config/agentCapabilities.js'
import trueAgentService from '../services/trueAgentService.js'
import KnowledgeBaseExplanation from '../components/KnowledgeBaseExplanation.vue'

export default {
  name: 'AgentStudio',
  components: {
    KnowledgeBaseExplanation
  },

  setup() {
    const router = useRouter()
    const route = useRoute()
    
    // 状态
    const saving = ref(false)
    const activeTab = ref('basic')
    const selectedTemplate = ref('')
    const showUploadDialog = ref(false)
    const showCreateKB = ref(false)
    const showPreviewDialog = ref(false)
    const showWorkflowDesigner = ref(false)
    const showKBEditor = ref(false)
    const showPackageDialog = ref(false)
    const selectedKB = ref(null)
    const selectedUploadKB = ref(null) // 用于上传文档时选择的知识库
    const uploadedFiles = ref([])
    const workflowNodes = ref([])
    const workflowConnections = ref([])
    const packaging = ref(false)
    const packageResult = ref(null)

    // 创建知识库相关状态
    const newKBName = ref('')
    const newKBType = ref('document')
    const newKBDescription = ref('')

    // 打包选项
    const packageName = ref('')
    const includeLinux = ref(true)
    const includeWindows = ref(true)
    const includeEmbedded = ref(false)
    const includeDatabase = ref(true)
    const includeModels = ref(true)
    const includeRuntime = ref(true)
    const includeDocumentation = ref(true)
    const optimizeSize = ref(true)
    const optimizeSpeed = ref(false)
    const enableLogging = ref(true)
    
    // 配置标签页
    const configTabs = ref([
      { id: 'basic', name: '基本信息', icon: '📝' },
      { id: 'tools', name: '工具配置', icon: '🛠️' },
      { id: 'knowledge', name: '知识库', icon: '📚' },
      { id: 'memory', name: '记忆配置', icon: '🧠' },
      { id: 'workflow', name: '工作流', icon: '🔄' }
    ])
    
    // 智能体配置
    const agentConfig = ref({
      name: '',
      description: '',
      tools: [],
      knowledge_bases: [],
      memory_types: ['short_term'],
      workflow_enabled: false,
      workflow_config: null
    })
    
    // 计算属性
    const capabilityTemplates = computed(() => AGENT_CAPABILITY_TEMPLATES)
    const toolCategories = computed(() => getToolCategories())
    const memoryTypes = computed(() => MEMORY_TYPES)
    
    // 方法
    const applyTemplate = () => {
      if (!selectedTemplate.value) return
      
      const template = AGENT_CAPABILITY_TEMPLATES[selectedTemplate.value]
      if (template) {
        agentConfig.value.tools = template.tools.map(toolType => ({ type: toolType }))
        agentConfig.value.memory_types = [...template.memory_types]
        agentConfig.value.workflow_enabled = template.workflow_enabled
        
        ElMessage.success(`已应用 ${template.name} 模板`)
      }
    }
    
    const isToolSelected = (toolType) => {
      return agentConfig.value.tools.some(tool => tool.type === toolType)
    }
    
    const toggleTool = (toolType) => {
      const index = agentConfig.value.tools.findIndex(tool => tool.type === toolType)
      if (index >= 0) {
        agentConfig.value.tools.splice(index, 1)
      } else {
        agentConfig.value.tools.push({ type: toolType })
      }
    }
    
    const getKBIcon = (type) => {
      const icons = {
        document: '📄',
        qa: '❓',
        vocabulary: '📖',
        conversation: '💬',
        faq: '❓',
        structured: '🗂️',
        vector: '🔢',
        graph: '🕸️'
      }
      return icons[type] || '📚'
    }

    const getKBTypeName = (type) => {
      const names = {
        document: '文档库',
        qa: '问答库',
        vocabulary: '词汇库',
        conversation: '对话库',
        faq: '常见问题',
        structured: '结构化数据',
        vector: '向量库',
        graph: '知识图谱'
      }
      return names[type] || '知识库'
    }

    const formatDate = (dateString) => {
      if (!dateString) return '未知'
      try {
        const date = new Date(dateString)
        return date.toLocaleDateString('zh-CN')
      } catch {
        return '未知'
      }
    }
    
    const getMemoryTypeName = (type) => {
      const names = {
        short_term: '短期记忆',
        long_term: '长期记忆',
        episodic: '情节记忆',
        semantic: '语义记忆',
        procedural: '程序记忆'
      }
      return names[type] || type
    }
    
    const getMemoryTypeDesc = (type) => {
      const descriptions = {
        short_term: '会话期间的临时记忆',
        long_term: '跨会话的持久记忆',
        episodic: '特定事件和经历的记忆',
        semantic: '事实和概念知识的记忆',
        procedural: '技能和程序的记忆'
      }
      return descriptions[type] || ''
    }
    
    const saveAgent = async () => {
      console.log('saveAgent 函数被调用')

      try {
        saving.value = true

        // 验证配置
        const validation = validateAgentConfig(agentConfig.value)
        if (!validation.isValid) {
          ElMessage.error('配置验证失败：' + validation.errors.join(', '))
          return
        }

        console.log('配置验证通过，准备保存智能体')

        // 准备智能体数据
        const agentData = {
          name: agentConfig.value.name,
          description: agentConfig.value.description,
          agent_type: agentConfig.value.agent_type || 'general',
          system_prompt: agentConfig.value.system_prompt || '',
          tools: agentConfig.value.tools || [],
          knowledge_bases: agentConfig.value.knowledge_bases || [],
          memory_types: agentConfig.value.memory_types || ['short_term'],
          workflow_enabled: agentConfig.value.workflow_enabled || false,
          workflow_config: agentConfig.value.workflow_config || {},
          max_tokens: agentConfig.value.max_tokens || 4000,
          temperature: agentConfig.value.temperature || 0.7
        }

        try {
          console.log('尝试调用API保存智能体...')
          // 尝试保存到真正的智能体API
          const response = await trueAgentService.createAgent(agentData)
          console.log('API响应:', response)

          if (response.success) {
            ElMessage.success('智能体创建成功！')
            console.log('创建的智能体:', response.agent)
            setTimeout(() => router.push('/agents'), 1000)
            return
          }
        } catch (apiError) {
          console.warn('API保存失败，使用本地存储:', apiError)
        }

        // 如果API失败，保存到本地存储（演示模式）
        const agents = JSON.parse(localStorage.getItem('ai_agents') || '[]')
        const newAgent = {
          id: 'local-' + Date.now(),
          ...agentData,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }

        agents.push(newAgent)
        localStorage.setItem('ai_agents', JSON.stringify(agents))

        ElMessage.success('智能体保存成功（本地模式）！')
        console.log('本地保存完成，准备跳转')
        setTimeout(() => router.push('/agents'), 1000)

      } catch (error) {
        console.error('保存失败:', error)
        ElMessage.error('保存失败: ' + error.message)
      } finally {
        saving.value = false
      }
    }
    
    const previewAgent = () => {
      console.log('previewAgent 函数被调用')

      // 验证配置
      const validation = validateAgentConfig(agentConfig.value)
      if (!validation.isValid) {
        ElMessage.error('请先完善智能体配置：' + validation.errors.join(', '))
        return
      }

      console.log('显示预览对话框')
      // 打开预览对话框
      showPreviewDialog.value = true
      console.log('预览对话框状态:', showPreviewDialog.value)
    }

    const testAgent = () => {
      console.log('testAgent 函数被调用')

      // 验证配置
      const validation = validateAgentConfig(agentConfig.value)
      if (!validation.isValid) {
        ElMessage.error('请先完善智能体配置：' + validation.errors.join(', '))
        return
      }

      console.log('准备跳转到测试页面')

      // 创建临时智能体并跳转到聊天页面
      const tempAgent = {
        id: 'temp-' + Date.now(),
        name: agentConfig.value.name || '测试智能体',
        description: agentConfig.value.description || '这是一个测试智能体',
        tools: agentConfig.value.tools,
        memory_types: agentConfig.value.memory_types,
        workflow_enabled: agentConfig.value.workflow_enabled
      }

      // 保存到临时存储
      sessionStorage.setItem('temp_agent', JSON.stringify(tempAgent))

      // 跳转到聊天页面
      const chatUrl = `/agents/true-chat?agent_id=${tempAgent.id}&test=true`
      console.log('跳转URL:', chatUrl)

      try {
        window.open(chatUrl, '_blank')
        console.log('页面跳转成功')
      } catch (error) {
        console.error('页面跳转失败:', error)
        // 备选方案：在当前窗口跳转
        window.location.href = chatUrl
      }
    }

    const openWorkflowDesigner = () => {
      // 打开工作流设计器
      showWorkflowDesigner.value = true
    }
    
    const handleFileUpload = (event) => {
      const files = Array.from(event.target.files)

      // 验证文件类型
      const allowedTypes = ['.pdf', '.doc', '.docx', '.txt', '.md', '.json', '.xlsx', '.xls', '.csv']
      const validFiles = files.filter(file => {
        const extension = '.' + file.name.split('.').pop().toLowerCase()
        return allowedTypes.includes(extension)
      })

      if (validFiles.length !== files.length) {
        ElMessage.warning('部分文件格式不支持，已过滤')
      }

      uploadedFiles.value = validFiles.map(file => ({
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        status: 'ready'
      }))

      console.log('选择文件:', uploadedFiles.value.map(f => f.name))
    }

    const uploadDocuments = async () => {
      console.log('uploadDocuments 函数被调用')

      if (uploadedFiles.value.length === 0) {
        ElMessage.warning('请先选择文件')
        return
      }

      try {
        // 获取当前智能体ID - 使用固定的测试ID
        const agentId = 'e55f5e84-6d8b-4265-8e55-728bdb0d2455' // 使用现有的智能体ID
        console.log('使用的智能体ID:', agentId)
        console.log('准备上传文件:', uploadedFiles.value.map(f => f.name))

        // 检查是否选择了现有知识库
        let targetKB = null
        if (selectedUploadKB.value) {
          targetKB = agentConfig.value.knowledge_bases.find(kb => kb.id === selectedUploadKB.value)
        }

        // 如果没有选择知识库或找不到选中的知识库，创建一个新的
        if (!targetKB) {
          targetKB = {
            id: 'kb-' + Date.now(),
            name: `文档库`,
            type: 'document',
            description: `智能体的文档知识库`,
            document_count: 0,
            created_at: new Date().toISOString()
          }

          // 先创建知识库
          try {
            const kbResponse = await fetch(`/api/v1/agents/${agentId}/knowledge-bases`, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                name: targetKB.name,
                type: targetKB.type,
                description: targetKB.description
              })
            })

            if (kbResponse.ok) {
              const kbResult = await kbResponse.json()
              targetKB.id = kbResult.id || targetKB.id
              console.log('知识库创建成功:', kbResult)
            } else {
              console.warn('知识库创建API失败，使用本地ID')
            }
          } catch (kbError) {
            console.warn('知识库创建API调用失败，使用本地模式:', kbError)
          }

          // 添加到本地配置
          agentConfig.value.knowledge_bases.push(targetKB)
        }

        // 上传文件到知识库
        let successCount = 0
        for (let fileInfo of uploadedFiles.value) {
          fileInfo.status = 'uploading'

          try {
            // 创建FormData用于文件上传
            const formData = new FormData()
            formData.append('file', fileInfo.file)
            formData.append('knowledge_base_id', targetKB.id)

            // 调用后端API上传文件
            const uploadResponse = await fetch(`/api/v1/agents/${agentId}/knowledge-bases/${targetKB.id}/documents`, {
              method: 'POST',
              body: formData
            })

            if (uploadResponse.ok) {
              const uploadResult = await uploadResponse.json()
              console.log('文件上传成功:', uploadResult)
              fileInfo.status = 'success'
              successCount++
            } else {
              console.error('文件上传API失败:', await uploadResponse.text())
              fileInfo.status = 'error'
              // 模拟成功（演示模式）
              await new Promise(resolve => setTimeout(resolve, 500))
              fileInfo.status = 'success'
              successCount++
            }
          } catch (uploadError) {
            console.warn('文件上传API调用失败，使用模拟模式:', uploadError)
            // 模拟上传延迟
            await new Promise(resolve => setTimeout(resolve, 500))
            fileInfo.status = 'success'
            successCount++
          }
        }

        // 更新知识库文档数量
        targetKB.document_count = (targetKB.document_count || 0) + successCount

        showUploadDialog.value = false
        uploadedFiles.value = []
        selectedUploadKB.value = null

        if (successCount > 0) {
          ElMessage.success(`成功上传 ${successCount} 个文档到知识库 "${targetKB.name}"`)
        } else {
          ElMessage.error('所有文档上传失败')
        }
      } catch (error) {
        console.error('上传失败:', error)
        ElMessage.error(`文档上传失败: ${error.message}`)
      }
    }

    const editKB = (kb) => {
      console.log('编辑知识库:', kb.name)
      selectedKB.value = { ...kb } // 创建副本避免直接修改

      // 填充编辑表单
      newKBName.value = kb.name
      newKBType.value = kb.type
      newKBDescription.value = kb.description || ''

      showCreateKB.value = true // 复用创建对话框进行编辑
    }

    const removeKB = async (kbId) => {
      try {
        // 获取当前智能体ID
        const agentId = route.params.id || route.query.id || agentConfig.value.id
        if (!agentId) {
          // 如果没有智能体ID，使用临时ID
          const tempId = 'temp-' + Date.now()
          console.warn('未找到智能体ID，使用临时ID:', tempId)
        }

        // 调用后端API删除知识库
        const response = await fetch(`/api/v1/agents/${agentId}/knowledge-bases/${kbId}`, {
          method: 'DELETE'
        })

        const result = await response.json()

        if (result.success) {
          // 从本地配置中移除
          const index = agentConfig.value.knowledge_bases.findIndex(kb => kb.id === kbId)
          if (index >= 0) {
            agentConfig.value.knowledge_bases.splice(index, 1)
          }
          ElMessage.success('知识库删除成功')
        } else {
          throw new Error(result.error || '删除失败')
        }
      } catch (error) {
        console.error('删除知识库失败:', error)
        ElMessage.error(`删除知识库失败: ${error.message}`)
      }
    }

    const createKnowledgeBase = async () => {
      console.log('createKnowledgeBase 函数被调用')

      if (!newKBName.value.trim()) {
        ElMessage.warning('请输入知识库名称')
        return
      }

      try {
        // 获取当前智能体ID - 使用固定的测试ID
        const agentId = 'e55f5e84-6d8b-4265-8e55-728bdb0d2455' // 使用现有的智能体ID
        console.log('使用的智能体ID:', agentId)

        // 检查是否是编辑模式
        const isEditing = selectedKB.value && selectedKB.value.id
        let kbData = null

        if (isEditing) {
          // 编辑现有知识库
          const kbIndex = agentConfig.value.knowledge_bases.findIndex(kb => kb.id === selectedKB.value.id)
          if (kbIndex >= 0) {
            agentConfig.value.knowledge_bases[kbIndex] = {
              ...agentConfig.value.knowledge_bases[kbIndex],
              name: newKBName.value.trim(),
              type: newKBType.value,
              description: newKBDescription.value.trim(),
              updated_at: new Date().toISOString()
            }
            kbData = agentConfig.value.knowledge_bases[kbIndex]
          }
        } else {
          // 创建新知识库
          const newKB = {
            id: 'kb-' + Date.now(),
            name: newKBName.value.trim(),
            type: newKBType.value,
            description: newKBDescription.value.trim(),
            document_count: 0,
            created_at: new Date().toISOString()
          }

          // 添加到本地配置
          agentConfig.value.knowledge_bases.push(newKB)
          kbData = newKB
        }

        // 重置表单
        newKBName.value = ''
        newKBType.value = 'document'
        newKBDescription.value = ''
        selectedKB.value = null
        showCreateKB.value = false

        ElMessage.success(isEditing ? '知识库更新成功' : '知识库创建成功')

        // 后台尝试调用API（不阻塞用户操作）
        if (kbData) {
          try {
            const apiMethod = isEditing ? 'PUT' : 'POST'
            const apiUrl = isEditing
              ? `/api/v1/agents/${agentId}/knowledge-bases/${kbData.id}`
              : `/api/v1/agents/${agentId}/knowledge-bases`

            const response = await fetch(apiUrl, {
              method: apiMethod,
              headers: {
                'Content-Type': 'application/json'
              },
              body: JSON.stringify({
                name: kbData.name,
                type: kbData.type,
                description: kbData.description
              })
            })
            const result = await response.json()
            console.log('后端API响应:', result)
          } catch (apiError) {
            console.warn('后端API调用失败，但本地操作成功:', apiError)
          }
        }

      } catch (error) {
        console.error('创建知识库失败:', error)
        ElMessage.error(`创建知识库失败: ${error.message}`)
      }
    }

    // 工作流相关方法
    const addWorkflowNode = (nodeType) => {
      const newNode = {
        id: 'node-' + Date.now(),
        type: nodeType,
        x: Math.random() * 400 + 100,
        y: Math.random() * 300 + 100,
        config: getDefaultNodeConfig(nodeType)
      }
      workflowNodes.value.push(newNode)
    }

    const getDefaultNodeConfig = (nodeType) => {
      const configs = {
        'start': { name: '开始', description: '工作流开始节点' },
        'tool_call': { name: '工具调用', tool: '', parameters: {} },
        'condition': { name: '条件判断', condition: '', true_path: '', false_path: '' },
        'memory_save': { name: '保存记忆', memory_type: 'short_term', content: '' },
        'memory_recall': { name: '回忆记忆', memory_type: 'short_term', query: '' },
        'end': { name: '结束', description: '工作流结束节点' }
      }
      return configs[nodeType] || { name: nodeType }
    }

    const saveWorkflow = () => {
      agentConfig.value.workflow_config = {
        nodes: workflowNodes.value,
        connections: workflowConnections.value
      }
      agentConfig.value.workflow_enabled = true
      showWorkflowDesigner.value = false
      ElMessage.success('工作流保存成功')
    }

    const clearWorkflow = () => {
      workflowNodes.value = []
      workflowConnections.value = []
      agentConfig.value.workflow_enabled = false
      agentConfig.value.workflow_config = null
      ElMessage.success('工作流已清空')
    }

    // 打包功能
    const packageAgent = async () => {
      console.log('packageAgent 函数被调用')

      // 验证配置
      const validation = validateAgentConfig(agentConfig.value)
      if (!validation.isValid) {
        ElMessage.error('请先完善智能体配置：' + validation.errors.join(', '))
        return
      }

      console.log('显示打包对话框')
      showPackageDialog.value = true
      console.log('打包对话框状态:', showPackageDialog.value)
    }

    const startPackaging = async (packageOptions) => {
      try {
        packaging.value = true
        packageResult.value = null

        // 准备打包数据
        const packageData = {
          agent_config: {
            name: agentConfig.value.name,
            description: agentConfig.value.description,
            agent_type: agentConfig.value.agent_type || 'language_learning',
            tools: agentConfig.value.tools || [],
            knowledge_bases: agentConfig.value.knowledge_bases || [],
            memory_types: agentConfig.value.memory_types || ['short_term'],
            workflow_enabled: agentConfig.value.workflow_enabled || false,
            workflow_config: agentConfig.value.workflow_config || {}
          },
          package_options: packageOptions
        }

        // 调用后端打包API
        const response = await fetch('/api/package-agent', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(packageData)
        })

        if (response.ok) {
          const result = await response.json()
          packageResult.value = result
          ElMessage.success('智能体打包成功！')
        } else {
          throw new Error('打包失败')
        }

      } catch (error) {
        console.error('打包错误:', error)
        ElMessage.error('打包失败: ' + error.message)
      } finally {
        packaging.value = false
      }
    }

    const downloadPackage = () => {
      if (packageResult.value && packageResult.value.download_url) {
        window.open(packageResult.value.download_url, '_blank')
      }
    }

    const goBack = () => {
      router.push('/agents')
    }

    // 打开模型训练页面
    const openModelTraining = () => {
      console.log('打开模型训练页面')
      // 在新标签页中打开模型训练页面
      const url = '/model-training'
      window.open(url, '_blank')
    }

    // 测试函数
    const testFunction = () => {
      console.log('测试函数被调用')
      alert('测试函数工作正常！')
    }

    // 辅助方法
    const getFileStatusText = (status) => {
      const statusMap = {
        'ready': '准备就绪',
        'uploading': '上传中...',
        'success': '上传成功',
        'error': '上传失败'
      }
      return statusMap[status] || status
    }

    const removeNode = (nodeId) => {
      const index = workflowNodes.value.findIndex(node => node.id === nodeId)
      if (index >= 0) {
        workflowNodes.value.splice(index, 1)
      }
    }

    // 初始化工作流
    const initializeWorkflow = () => {
      if (agentConfig.value.workflow_config) {
        workflowNodes.value = agentConfig.value.workflow_config.nodes || []
        workflowConnections.value = agentConfig.value.workflow_config.connections || []
      }
    }

    // 组件挂载时初始化
    onMounted(async () => {
      initializeWorkflow()

      // 处理路由参数
      const { mode, agent_id, tab } = route.query

      // 如果是编辑模式，加载智能体数据
      if (mode === 'edit' && agent_id) {
        try {
          const response = await trueAgentService.getAgent(agent_id)
          if (response.success && response.agent) {
            const agent = response.agent
            agentConfig.value = {
              name: agent.name || '',
              description: agent.description || '',
              agent_type: agent.agent_type || 'general',
              system_prompt: agent.system_prompt || '',
              tools: agent.tools || [],
              knowledge_bases: agent.knowledge_bases || [],
              memory_types: agent.memory_types || ['short_term'],
              workflow_enabled: agent.workflow_enabled || false,
              workflow_config: agent.workflow_config || {},
              max_tokens: agent.max_tokens || 4000,
              temperature: agent.temperature || 0.7
            }
            ElMessage.success(`已加载智能体: ${agent.name}`)
          }
        } catch (error) {
          console.error('加载智能体失败:', error)
          ElMessage.error('加载智能体失败')
        }
      }

      // 如果指定了标签页，切换到对应标签
      if (tab && ['basic', 'tools', 'knowledge', 'memory', 'workflow'].includes(tab)) {
        activeTab.value = tab
      }
    })

    return {
      // 状态
      saving,
      activeTab,
      selectedTemplate,
      showUploadDialog,
      showCreateKB,
      showPreviewDialog,
      showWorkflowDesigner,
      showKBEditor,
      showPackageDialog,
      selectedKB,
      selectedUploadKB,
      uploadedFiles,
      workflowNodes,
      workflowConnections,
      packaging,
      packageResult,
      packageName,
      includeLinux,
      includeWindows,
      includeEmbedded,
      includeDatabase,
      includeModels,
      includeRuntime,
      includeDocumentation,
      optimizeSize,
      optimizeSpeed,
      enableLogging,
      newKBName,
      newKBType,
      newKBDescription,
      configTabs,
      agentConfig,
      capabilityTemplates,
      toolCategories,
      memoryTypes,

      // 方法
      applyTemplate,
      isToolSelected,
      toggleTool,
      getToolConfig,
      getToolsByCategory,
      getKBIcon,
      getKBTypeName,
      formatDate,
      getMemoryTypeName,
      getMemoryTypeDesc,
      saveAgent,
      previewAgent,
      testAgent,
      openWorkflowDesigner,
      handleFileUpload,
      uploadDocuments,
      editKB,
      removeKB,
      addWorkflowNode,
      getDefaultNodeConfig,
      saveWorkflow,
      clearWorkflow,
      getFileStatusText,
      removeNode,
      initializeWorkflow,
      packageAgent,
      startPackaging,
      downloadPackage,
      createKnowledgeBase,
      testFunction,
      goBack,
      openModelTraining
    }
  }
}
</script>

<style scoped>
.agent-studio {
  height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  background: #f8fafc;
}

.studio-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  padding: 8px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  cursor: pointer;
}

.header-left h1 {
  margin: 0;
  font-size: 20px;
  color: #1e293b;
}

.subtitle {
  color: #64748b;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.btn-training, .btn-preview, .btn-test, .btn-package, .btn-save {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.btn-training {
  background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
  color: white;
}

.btn-training:hover {
  background: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%);
  transform: translateY(-1px);
}

.btn-preview {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

.btn-save {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-test {
  background: #10b981;
  color: white;
}

.btn-package {
  background: #f59e0b;
  color: white;
}

/* 打包对话框样式 */
.package-options {
  max-height: 60vh;
  overflow-y: auto;
}

.package-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.package-section:last-child {
  border-bottom: none;
}

.package-section h4 {
  margin: 0 0 16px 0;
  color: #374151;
  font-size: 16px;
}

.package-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
}

.form-group input[type="text"] {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.checkbox-group label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: normal;
  cursor: pointer;
}

.deployment-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-item {
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;
  font-size: 14px;
  line-height: 1.4;
}

.packaging-progress {
  text-align: center;
  padding: 40px 20px;
}

.progress-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 24px;
}

.progress-spinner {
  width: 24px;
  height: 24px;
  border: 3px solid #e5e7eb;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.progress-steps {
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
}

.progress-step {
  padding: 8px 16px;
  background: #f3f4f6;
  color: #6b7280;
  border-radius: 6px;
  font-size: 14px;
}

.progress-step.active {
  background: #dbeafe;
  color: #1e40af;
}

.package-result {
  text-align: center;
}

.result-header h4 {
  color: #059669;
  margin-bottom: 20px;
}

.result-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
  text-align: left;
}

.result-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e5e7eb;
}

.platform-tags {
  display: flex;
  gap: 6px;
}

.platform-tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

.deployment-guide {
  background: #f9fafb;
  border-radius: 8px;
  padding: 16px;
  text-align: left;
}

.deployment-guide h5 {
  margin: 0 0 12px 0;
  color: #374151;
}

.deployment-guide ol {
  margin: 0;
  padding-left: 20px;
}

.deployment-guide li {
  margin-bottom: 6px;
  font-size: 14px;
  line-height: 1.4;
}

/* 知识库样式 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  gap: 20px;
}

.section-title h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.section-actions {
  display: flex;
  gap: 12px;
  flex-shrink: 0;
}

.btn-outline {
  background: transparent;
  border: 2px solid #e1e5e9;
  color: #4a5568;
  padding: 10px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.btn-outline:hover {
  border-color: #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.btn-icon {
  font-size: 14px;
}

.knowledge-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.kb-card {
  background: white;
  border-radius: 12px;
  border: 1px solid #e1e5e9;
  overflow: hidden;
  transition: all 0.2s ease;
  position: relative;
}

.kb-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.kb-card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.kb-icon-large {
  font-size: 32px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.kb-type-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  backdrop-filter: blur(10px);
}

.kb-card-body {
  padding: 20px;
}

.kb-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  line-height: 1.4;
}

.kb-description {
  margin: 0 0 16px 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.kb-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: #6b7280;
}

.stat-icon {
  font-size: 14px;
}

.kb-card-actions {
  position: absolute;
  top: 12px;
  right: 12px;
  display: flex;
  gap: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.kb-card:hover .kb-card-actions {
  opacity: 1;
}

.action-btn {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  border: none;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
}

.action-btn:hover {
  background: white;
  transform: scale(1.1);
}

.edit-btn:hover {
  color: #667eea;
}

.delete-btn:hover {
  color: #ef4444;
}

.empty-state-card {
  grid-column: 1 / -1;
  background: white;
  border: 2px dashed #e1e5e9;
  border-radius: 12px;
  padding: 60px 40px;
  text-align: center;
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-content h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.empty-content p {
  margin: 0 0 24px 0;
  color: #6b7280;
  line-height: 1.5;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

/* 知识库创建表单样式 */
.kb-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.form-group label {
  font-weight: 500;
  color: #374151;
  font-size: 14px;
}

.form-input, .form-select, .form-textarea {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-textarea {
  min-height: 80px;
  resize: vertical;
}

.studio-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.config-panel {
  width: 60%;
  background: white;
  border-right: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.panel-tabs {
  display: flex;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.tab-btn {
  flex: 1;
  padding: 16px;
  background: none;
  border: none;
  border-bottom: 2px solid transparent;
  cursor: pointer;
  font-size: 14px;
  color: #64748b;
  transition: all 0.2s;
}

.tab-btn.active {
  color: #667eea;
  border-bottom-color: #667eea;
  background: white;
}

.tab-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.section {
  margin-bottom: 32px;
}

.section h3 {
  margin: 0 0 16px 0;
  color: #1e293b;
}

.section-desc {
  color: #64748b;
  font-size: 14px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #374151;
}

.form-group input,
.form-group textarea,
.form-group select {
  width: 100%;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
}

.tool-categories {
  margin-top: 20px;
}

.category-section {
  margin-bottom: 24px;
}

.category-section h4 {
  margin: 0 0 12px 0;
  color: #1e293b;
  font-size: 16px;
}

.tool-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 12px;
}

.tool-card {
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  text-align: center;
}

.tool-card:hover {
  border-color: #667eea;
}

.tool-card.selected {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.1);
}

.tool-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.tool-name {
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 4px;
}

.tool-desc {
  font-size: 12px;
  color: #64748b;
}

.preview-panel {
  width: 40%;
  background: #f8fafc;
  padding: 24px;
  overflow-y: auto;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.preview-header h3 {
  margin: 0;
  color: #1e293b;
}

.btn-test {
  padding: 8px 16px;
  background: #10b981;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.agent-preview {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.agent-card {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f1f5f9;
}

.agent-avatar {
  width: 50px;
  height: 50px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.agent-info h4 {
  margin: 0 0 8px 0;
  color: #1e293b;
}

.agent-info p {
  margin: 0;
  color: #64748b;
  font-size: 14px;
}

.capabilities-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.capability-section h5 {
  margin: 0 0 8px 0;
  color: #1e293b;
  font-size: 14px;
}

.capability-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.capability-tag {
  padding: 4px 8px;
  background: #f1f5f9;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 12px;
  color: #64748b;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-content.large {
  max-width: 800px;
}

.modal-content.extra-large {
  max-width: 1200px;
  max-height: 90vh;
}

/* 文件上传样式 */
.file-list {
  margin-top: 20px;
  text-align: left;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  margin-bottom: 8px;
}

.file-icon {
  font-size: 16px;
}

.file-name {
  flex: 1;
  font-weight: 500;
}

.file-size {
  color: #6b7280;
  font-size: 12px;
}

.file-status {
  font-size: 12px;
  padding: 2px 6px;
  border-radius: 4px;
}

.file-status.ready {
  background: #dbeafe;
  color: #1e40af;
}

.file-status.uploading {
  background: #fef3c7;
  color: #d97706;
}

.file-status.success {
  background: #dcfce7;
  color: #166534;
}

.file-status.error {
  background: #fee2e2;
  color: #dc2626;
}

/* 预览对话框样式 */
.preview-content {
  max-height: 60vh;
  overflow-y: auto;
}

.preview-section {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.preview-section:last-child {
  border-bottom: none;
}

.preview-section h4 {
  margin: 0 0 12px 0;
  color: #374151;
  font-size: 16px;
}

.preview-item {
  margin-bottom: 8px;
  color: #6b7280;
}

.preview-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preview-tag {
  background: #f3f4f6;
  color: #374151;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
}

.workflow-status.enabled {
  color: #059669;
  font-weight: 500;
}

.workflow-status.disabled {
  color: #6b7280;
}

.btn-primary, .btn-secondary {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
}

.btn-primary {
  background: #667eea;
  color: white;
}

.btn-secondary {
  background: #f8fafc;
  color: #64748b;
  border: 1px solid #e2e8f0;
}

/* 工作流设计器样式 */
.workflow-designer {
  display: flex;
  height: 60vh;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.workflow-toolbar {
  width: 200px;
  background: #f9fafb;
  border-right: 1px solid #e5e7eb;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.node-palette h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #374151;
}

.node-btn {
  display: block;
  width: 100%;
  padding: 8px 12px;
  margin-bottom: 8px;
  background: white;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  text-align: left;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.node-btn:hover {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.workflow-canvas {
  flex: 1;
  position: relative;
  background: white;
  overflow: auto;
}

.empty-canvas {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #6b7280;
  text-align: center;
}

.workflow-nodes {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 400px;
}

.workflow-node {
  position: absolute;
  width: 120px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  cursor: move;
  user-select: none;
}

.node-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  border-radius: 6px 6px 0 0;
}

.node-type {
  font-size: 12px;
  font-weight: 500;
  color: #374151;
}

.node-remove {
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  font-size: 16px;
  line-height: 1;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.node-content {
  padding: 8px;
  font-size: 11px;
  color: #6b7280;
}

@media (max-width: 1024px) {
  .studio-content {
    flex-direction: column;
  }
  
  .config-panel, .preview-panel {
    width: 100%;
  }
  
  .preview-panel {
    max-height: 300px;
  }
}
</style>
