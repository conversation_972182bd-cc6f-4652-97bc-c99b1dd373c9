#!/usr/bin/env python3
"""
测试CogVideoX-5B - 更适合8GB显存的AI视频生成模型
"""

import torch
import os
import time
import gc
from pathlib import Path

def check_system_requirements():
    """检查系统要求"""
    print("=== 检查CogVideoX-5B系统要求 ===")
    
    # 检查CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，CogVideoX需要GPU")
        return False
    
    gpu_name = torch.cuda.get_device_name(0)
    gpu_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
    
    print(f"✅ GPU: {gpu_name}")
    print(f"✅ GPU内存: {gpu_memory:.1f} GB")
    
    # CogVideoX-5B的要求
    if gpu_memory < 7:
        print("⚠️  GPU内存可能不足，CogVideoX-5B建议8GB+")
        return False
    else:
        print("✅ GPU内存满足要求")
    
    # 检查PyTorch版本
    print(f"✅ PyTorch版本: {torch.__version__}")
    
    return True

def test_cogvideox_import():
    """测试CogVideoX导入"""
    print("\n=== 测试CogVideoX导入 ===")
    
    try:
        # 尝试导入diffusers
        from diffusers import CogVideoXPipeline
        print("✅ CogVideoX导入成功")
        return True
    except ImportError as e:
        print(f"❌ CogVideoX导入失败: {e}")
        print("💡 需要安装: pip install diffusers[torch] transformers accelerate")
        return False
    except Exception as e:
        print(f"❌ 其他导入错误: {e}")
        return False

def test_cogvideox_model_loading():
    """测试CogVideoX模型加载"""
    print("\n=== 测试CogVideoX模型加载 ===")
    
    try:
        from diffusers import CogVideoXPipeline
        
        print("🔄 开始加载CogVideoX-5B模型...")
        print("⚠️  首次运行会下载约10GB模型文件，请耐心等待...")
        
        # 清理GPU缓存
        torch.cuda.empty_cache()
        gc.collect()
        
        # 加载模型（使用内存优化）
        pipe = CogVideoXPipeline.from_pretrained(
            "THUDM/CogVideoX-5b",
            torch_dtype=torch.float16,  # 使用半精度节省内存
            device_map="auto"  # 自动分配设备
        )
        
        # 启用内存优化
        pipe.enable_model_cpu_offload()
        pipe.enable_vae_slicing()
        pipe.enable_vae_tiling()
        
        print("✅ CogVideoX-5B模型加载成功")
        
        # 测试生成
        print("🎬 测试视频生成...")
        
        prompt = "A cat walking in a garden"
        
        start_time = time.time()
        
        # 生成视频（使用最小参数）
        video = pipe(
            prompt=prompt,
            num_frames=49,  # CogVideoX标准帧数
            num_inference_steps=20,  # 较少步数
            guidance_scale=6.0,
            generator=torch.Generator().manual_seed(42)
        ).frames[0]
        
        end_time = time.time()
        
        print(f"✅ 视频生成成功！")
        print(f"⏱️  生成时间: {end_time - start_time:.2f}秒")
        print(f"📊 视频形状: {len(video)} 帧")
        
        # 保存视频
        output_path = "cogvideox_test.mp4"
        save_video_frames(video, output_path)
        
        return True
        
    except Exception as e:
        print(f"❌ CogVideoX测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_video_frames(frames, output_path, fps=8):
    """保存视频帧"""
    try:
        import imageio
        
        print(f"🔄 保存视频到: {output_path}")
        
        # 转换帧格式
        video_frames = []
        for frame in frames:
            if hasattr(frame, 'numpy'):
                frame_np = frame.numpy()
            else:
                frame_np = frame
            
            # 确保格式正确
            if frame_np.max() <= 1.0:
                frame_np = (frame_np * 255).astype('uint8')
            
            video_frames.append(frame_np)
        
        # 保存视频
        imageio.mimsave(output_path, video_frames, fps=fps)
        
        file_size = os.path.getsize(output_path) / 1024  # KB
        duration = len(video_frames) / fps
        
        print(f"✅ 视频保存成功: {output_path}")
        print(f"📊 帧数: {len(video_frames)}")
        print(f"📊 时长: {duration:.1f}秒")
        print(f"📊 文件大小: {file_size:.1f} KB")
        
        return True
        
    except Exception as e:
        print(f"❌ 视频保存失败: {e}")
        return False

def test_cogvideox_memory_optimized():
    """内存优化版本的CogVideoX测试"""
    print("\n=== 内存优化CogVideoX测试 ===")
    
    try:
        from diffusers import CogVideoXPipeline
        import torch
        
        # 最大化内存优化
        torch.cuda.empty_cache()
        gc.collect()
        
        print("🔄 加载内存优化版本...")
        
        # 使用更激进的内存优化
        pipe = CogVideoXPipeline.from_pretrained(
            "THUDM/CogVideoX-5b",
            torch_dtype=torch.float16,
            low_cpu_mem_usage=True,
            device_map="auto"
        )
        
        # 启用所有内存优化
        pipe.enable_model_cpu_offload()
        pipe.enable_vae_slicing()
        pipe.enable_vae_tiling()
        pipe.enable_attention_slicing(1)
        
        print("✅ 内存优化版本加载成功")
        
        # 简单测试
        prompt = "cat"
        print(f"🎬 生成简单测试: {prompt}")
        
        start_time = time.time()
        
        video = pipe(
            prompt=prompt,
            num_frames=25,  # 更少帧数
            num_inference_steps=10,  # 更少步数
            guidance_scale=5.0,
            generator=torch.Generator().manual_seed(42)
        ).frames[0]
        
        end_time = time.time()
        
        print(f"✅ 内存优化测试成功！")
        print(f"⏱️  生成时间: {end_time - start_time:.2f}秒")
        
        # 保存
        save_video_frames(video, "cogvideox_optimized.mp4")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存优化测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始CogVideoX-5B测试")
    
    # 1. 检查系统要求
    if not check_system_requirements():
        print("❌ 系统要求不满足")
        return False
    
    # 2. 检查导入
    if not test_cogvideox_import():
        print("❌ 导入测试失败")
        return False
    
    # 3. 选择测试模式
    print("\n选择测试模式:")
    print("1. 完整测试 (49帧, 20步)")
    print("2. 内存优化测试 (25帧, 10步)")
    
    # 默认使用内存优化测试
    mode = "2"
    
    if mode == "1":
        print("\n🎯 运行完整测试...")
        success = test_cogvideox_model_loading()
    else:
        print("\n⚡ 运行内存优化测试...")
        success = test_cogvideox_memory_optimized()
    
    if success:
        print("\n🎉 CogVideoX-5B测试成功！")
        print("💡 CogVideoX-5B可能是您硬件的最佳选择")
    else:
        print("\n❌ CogVideoX-5B测试失败")
        print("💡 可能需要更多GPU内存或其他优化")
    
    return success

if __name__ == "__main__":
    main()
