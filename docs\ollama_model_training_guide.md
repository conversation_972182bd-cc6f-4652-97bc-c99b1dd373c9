# Ollama 本地模型训练指南

## 概述

本指南将帮助您使用系统内置的模型训练功能，创建专业的智能体（如教师、医生、律师等）。

## 前置条件

### 1. 安装 Ollama

```bash
# Windows (使用 PowerShell)
winget install Ollama.Ollama

# 或者从官网下载安装包
# https://ollama.ai/download
```

### 2. 启动 Ollama 服务

```bash
# 启动 Ollama 服务
ollama serve

# 验证服务是否运行
curl http://localhost:11434/api/tags
```

### 3. 下载基础模型

```bash
# 推荐的中文模型
ollama pull qwen2.5:7b

# 轻量级英文模型
ollama pull llama3.2:3b

# 多语言模型
ollama pull mistral:7b
```

## 使用方法

### 方法一：使用系统界面创建专业智能体

1. **访问训练界面**
   - 打开浏览器访问：`http://*************:9000`
   - 导航到"模型训练中心"

2. **选择专业类型**
   - 教师：适合教育、辅导、知识传授
   - 医生：适合健康咨询、医学科普
   - 律师：适合法律咨询、法规解释

3. **配置智能体**
   ```
   专业类型：教师
   自定义名称：张老师
   专业化领域：数学教师
   ```

4. **添加训练数据**
   ```
   用户输入：我不理解这个数学概念
   期望回复：没关系，让我们一步步来理解这个概念...
   ```

5. **创建模型**
   - 点击"创建专业智能体"
   - 等待模型创建完成（通常需要2-5分钟）

### 方法二：使用 Ollama 命令行创建

1. **创建 Modelfile**

```dockerfile
# 教师智能体 Modelfile
FROM qwen2.5:7b

SYSTEM """你是一位经验丰富的专业教师，具有以下特质：
1. 耐心细致：对学生的问题总是耐心解答，循循善诱
2. 知识渊博：拥有深厚的学科知识和教学经验
3. 因材施教：根据学生的水平调整教学方法和难度
4. 启发思考：不直接给答案，而是引导学生思考
5. 鼓励支持：给予学生积极的反馈和鼓励
6. 清晰表达：用简单易懂的语言解释复杂概念"""

PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1
PARAMETER num_ctx 4096
```

2. **创建模型**

```bash
# 创建教师智能体
ollama create teacher_zhang -f teacher.Modelfile

# 验证模型创建成功
ollama list
```

3. **测试模型**

```bash
# 测试对话
ollama run teacher_zhang "我不理解这个数学概念，能帮我解释一下吗？"
```

## 专业智能体模板

### 1. 教师智能体

**特点：**
- 耐心细致，循循善诱
- 因材施教，启发思考
- 鼓励支持，清晰表达

**适用场景：**
- 学科辅导
- 作业答疑
- 学习方法指导
- 知识点解释

**训练示例：**
```
Q: 这道题太难了，我不会做
A: 我理解你的感受，遇到困难是学习过程中很正常的事情。让我们把这道题分解成几个小步骤，每次只解决一个小问题，你会发现其实没有那么难。
```

### 2. 医疗顾问智能体

**特点：**
- 专业严谨，关怀体贴
- 谨慎负责，清晰沟通
- 安全第一，建议就医

**适用场景：**
- 健康咨询
- 症状初步分析
- 医学科普
- 用药指导

**重要提醒：**
- 不能替代专业医疗诊断
- 严重症状必须建议就医
- 不提供具体药物推荐

### 3. 法律顾问智能体

**特点：**
- 法律专业，逻辑清晰
- 客观公正，风险提醒
- 建议专业律师咨询

**适用场景：**
- 法律知识普及
- 合同条款解释
- 权益保护指导
- 法律程序说明

## 高级训练技巧

### 1. 数据准备

**质量要求：**
- 至少准备20-50个高质量对话示例
- 确保回复风格一致
- 涵盖常见问题场景

**数据格式：**
```json
{
  "input": "用户问题",
  "output": "期望的智能体回复"
}
```

### 2. 参数调优

```dockerfile
# 创意性较高的任务
PARAMETER temperature 0.8
PARAMETER top_p 0.9

# 需要准确性的任务
PARAMETER temperature 0.3
PARAMETER top_p 0.7

# 控制回复长度
PARAMETER num_predict 200

# 上下文长度
PARAMETER num_ctx 4096
```

### 3. 系统提示词优化

**好的系统提示词特点：**
- 明确角色定位
- 具体行为指导
- 清晰的限制条件
- 一致的回复风格

**示例：**
```
你是一位专业的数学教师，名叫张老师。你的特点是：
1. 总是用简单易懂的语言解释复杂概念
2. 喜欢用生活中的例子来类比数学问题
3. 鼓励学生独立思考，不直接给出答案
4. 对学生的进步给予积极反馈
5. 回复长度控制在100字以内，简洁明了
```

## 模型管理

### 1. 查看模型

```bash
# 查看所有模型
ollama list

# 查看模型详情
ollama show teacher_zhang
```

### 2. 删除模型

```bash
# 删除不需要的模型
ollama rm teacher_zhang
```

### 3. 导出模型

```bash
# 导出模型（用于分享或备份）
ollama save teacher_zhang teacher_zhang.tar
```

## 性能优化

### 1. 硬件要求

**最低配置：**
- CPU: 4核心
- 内存: 8GB
- 存储: 10GB 可用空间

**推荐配置：**
- CPU: 8核心以上
- 内存: 16GB以上
- GPU: 支持CUDA的显卡（可选）

### 2. 模型选择

**轻量级模型（适合资源有限的环境）：**
- llama3.2:3b (3B参数)
- qwen2.5:3b (3B参数)

**平衡性能模型（推荐）：**
- qwen2.5:7b (7B参数)
- mistral:7b (7B参数)

**高性能模型（需要更多资源）：**
- qwen2.5:14b (14B参数)
- llama3.1:8b (8B参数)

## 常见问题

### Q1: 模型创建失败怎么办？

**可能原因：**
- Ollama 服务未启动
- 基础模型未下载
- 磁盘空间不足
- 内存不足

**解决方法：**
```bash
# 检查服务状态
curl http://localhost:11434/api/tags

# 重启 Ollama 服务
ollama serve

# 检查磁盘空间
df -h
```

### Q2: 模型回复质量不好怎么办？

**改进方法：**
1. 增加更多高质量训练数据
2. 优化系统提示词
3. 调整模型参数
4. 选择更大的基础模型

### Q3: 如何让模型更专业？

**专业化策略：**
1. 收集领域专业术语和表达方式
2. 添加专业知识背景到系统提示词
3. 使用领域专家审核的训练数据
4. 设置专业的回复模板和格式

## 最佳实践

1. **从小规模开始**：先用少量数据测试，确认效果后再扩大规模
2. **持续迭代**：根据使用反馈不断优化训练数据和提示词
3. **版本管理**：为不同版本的模型使用清晰的命名规则
4. **性能监控**：定期测试模型性能，确保质量稳定
5. **安全考虑**：确保训练数据不包含敏感信息

## 技术支持

如果您在使用过程中遇到问题，可以：

1. 查看系统日志获取详细错误信息
2. 检查 Ollama 官方文档
3. 联系技术支持团队

---

**注意：** 本地模型训练需要一定的计算资源和时间，请根据您的硬件配置合理安排训练任务。
