newsapi/__init__.py,sha256=HIjVs-CYG5KMtbsFL5v-PyWhjHX_SlOt3ga1wD87Sok,57
newsapi/__pycache__/__init__.cpython-311.pyc,,
newsapi/__pycache__/const.cpython-311.pyc,,
newsapi/__pycache__/newsapi_auth.cpython-311.pyc,,
newsapi/__pycache__/newsapi_client.cpython-311.pyc,,
newsapi/__pycache__/newsapi_exception.cpython-311.pyc,,
newsapi/__pycache__/utils.cpython-311.pyc,,
newsapi/const.py,sha256=ixNQhb927sgrRo3QINL2VachrezI49HY2uEc1NCmeLk,1577
newsapi/newsapi_auth.py,sha256=bQSrXZJf3t7tqCn9YRfta1E1Q1NHmLir1Id7symWdlw,424
newsapi/newsapi_client.py,sha256=CU7eSEpBlQO3ApuLbuvDiMgMP45O9ruaSIiqgi5qWow,16353
newsapi/newsapi_exception.py,sha256=R6oR2VeSHdr8rK7ZoYSg6nPJfh7MVuO01f27dA4-Czw,558
newsapi/utils.py,sha256=T9Eov_PcKNDgOdHM-9u_3rKf09SDgNaJLCHieS8hRtc,2056
newsapi_python-0.2.7.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
newsapi_python-0.2.7.dist-info/LICENSE.txt,sha256=ElffhxD-BTNvrQmDZUyrvCU9fKvoZKjlvHAI2pRG6wI,1075
newsapi_python-0.2.7.dist-info/METADATA,sha256=Bwx6ImD5MdPEAWNEVDakpdPZwoF13S7jsOgaEIe_f74,1193
newsapi_python-0.2.7.dist-info/RECORD,,
newsapi_python-0.2.7.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
newsapi_python-0.2.7.dist-info/WHEEL,sha256=HX-v9-noUkyUoxyZ1PMSuS7auUxDAR4VBdoYLqD0xws,110
newsapi_python-0.2.7.dist-info/top_level.txt,sha256=SYwKIYYTwwvWrb0WFVZbt-pUoBPvp8L95A_KJ8v-LoQ,8
