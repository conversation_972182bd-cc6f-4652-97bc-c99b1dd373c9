# Copyright 2024-2025 The Alibaba Wan Team Authors. All rights reserved.
import gc
import logging
import math
import os
import random
import sys
import types
from contextlib import contextmanager
from functools import partial

import torch
import torch.amp
import torch.cuda.amp as amp
import torch.distributed as dist
from tqdm import tqdm

from .distributed.fsdp import shard_model
from .modules.model import WanModel
from .modules.t5 import T5EncoderModel
from .modules.vae import WanVAE
from .utils.fm_solvers import (FlowDPMSolverMultistepScheduler,
                               get_sampling_sigmas, retrieve_timesteps)
from .utils.fm_solvers_unipc import FlowUniPCMultistepScheduler


class WanT2V:

    def __init__(
        self,
        config,
        checkpoint_dir,
        device_id=0,
        rank=0,
        t5_fsdp=False,
        dit_fsdp=False,
        use_usp=False,
        t5_cpu=False,
    ):
        r"""
        Initializes the Wan text-to-video generation model components.

        Args:
            config (EasyDict):
                Object containing model parameters initialized from config.py
            checkpoint_dir (`str`):
                Path to directory containing model checkpoints
            device_id (`int`,  *optional*, defaults to 0):
                Id of target GPU device
            rank (`int`,  *optional*, defaults to 0):
                Process rank for distributed training
            t5_fsdp (`bool`, *optional*, defaults to False):
                Enable FSDP sharding for T5 model
            dit_fsdp (`bool`, *optional*, defaults to False):
                Enable FSDP sharding for DiT model
            use_usp (`bool`, *optional*, defaults to False):
                Enable distribution strategy of USP.
            t5_cpu (`bool`, *optional*, defaults to False):
                Whether to place T5 model on CPU. Only works without t5_fsdp.
        """
        self.device = torch.device(f"cuda:{device_id}")
        self.config = config
        self.rank = rank
        self.t5_cpu = t5_cpu

        self.num_train_timesteps = config.num_train_timesteps
        self.param_dtype = config.param_dtype

        shard_fn = partial(shard_model, device_id=device_id)
        self.text_encoder = T5EncoderModel(
            text_len=config.text_len,
            dtype=config.t5_dtype,
            device=torch.device('cpu'),
            checkpoint_path=os.path.join(checkpoint_dir, config.t5_checkpoint),
            tokenizer_path=config.t5_tokenizer,
            shard_fn=shard_fn if t5_fsdp else None)

        self.vae_stride = config.vae_stride
        self.patch_size = config.patch_size
        self.vae = WanVAE(
            vae_pth=os.path.join(checkpoint_dir, config.vae_checkpoint),
            device=self.device)

        logging.info(f"Creating WanModel from {checkpoint_dir}")
        self.model = WanModel.from_pretrained(checkpoint_dir)
        self.model.eval().requires_grad_(False)

        if use_usp:
            from xfuser.core.distributed import \
                get_sequence_parallel_world_size

            from .distributed.xdit_context_parallel import (usp_attn_forward,
                                                            usp_dit_forward)
            for block in self.model.blocks:
                block.self_attn.forward = types.MethodType(
                    usp_attn_forward, block.self_attn)
            self.model.forward = types.MethodType(usp_dit_forward, self.model)
            self.sp_size = get_sequence_parallel_world_size()
        else:
            self.sp_size = 1

        if dist.is_initialized():
            dist.barrier()
        if dit_fsdp:
            self.model = shard_fn(self.model)
        else:
            self.model.to(self.device)

        self.sample_neg_prompt = config.sample_neg_prompt

    def generate(self,
                 input_prompt,
                 size=(1280, 720),
                 frame_num=81,
                 shift=5.0,
                 sample_solver='unipc',
                 sampling_steps=50,
                 guide_scale=5.0,
                 n_prompt="",
                 seed=-1,
                 offload_model=True):
        r"""
        Generates video frames from text prompt using diffusion process.

        Args:
            input_prompt (`str`):
                Text prompt for content generation
            size (tupele[`int`], *optional*, defaults to (1280,720)):
                Controls video resolution, (width,height).
            frame_num (`int`, *optional*, defaults to 81):
                How many frames to sample from a video. The number should be 4n+1
            shift (`float`, *optional*, defaults to 5.0):
                Noise schedule shift parameter. Affects temporal dynamics
            sample_solver (`str`, *optional*, defaults to 'unipc'):
                Solver used to sample the video.
            sampling_steps (`int`, *optional*, defaults to 40):
                Number of diffusion sampling steps. Higher values improve quality but slow generation
            guide_scale (`float`, *optional*, defaults 5.0):
                Classifier-free guidance scale. Controls prompt adherence vs. creativity
            n_prompt (`str`, *optional*, defaults to ""):
                Negative prompt for content exclusion. If not given, use `config.sample_neg_prompt`
            seed (`int`, *optional*, defaults to -1):
                Random seed for noise generation. If -1, use random seed.
            offload_model (`bool`, *optional*, defaults to True):
                If True, offloads models to CPU during generation to save VRAM

        Returns:
            torch.Tensor:
                Generated video frames tensor. Dimensions: (C, N H, W) where:
                - C: Color channels (3 for RGB)
                - N: Number of frames (81)
                - H: Frame height (from size)
                - W: Frame width from size)
        """
        # preprocess
        F = frame_num
        target_shape = (self.vae.model.z_dim, (F - 1) // self.vae_stride[0] + 1,
                        size[1] // self.vae_stride[1],
                        size[0] // self.vae_stride[2])

        seq_len = math.ceil((target_shape[2] * target_shape[3]) /
                            (self.patch_size[1] * self.patch_size[2]) *
                            target_shape[1] / self.sp_size) * self.sp_size

        if n_prompt == "":
            n_prompt = self.sample_neg_prompt
        seed = seed if seed >= 0 else random.randint(0, sys.maxsize)
        seed_g = torch.Generator(device=self.device)
        seed_g.manual_seed(seed)

        if not self.t5_cpu:
            self.text_encoder.model.to(self.device)
            context = self.text_encoder([input_prompt], self.device)
            context_null = self.text_encoder([n_prompt], self.device)
            if offload_model:
                self.text_encoder.model.cpu()
        else:
            context = self.text_encoder([input_prompt], torch.device('cpu'))
            context_null = self.text_encoder([n_prompt], torch.device('cpu'))
            context = [t.to(self.device) for t in context]
            context_null = [t.to(self.device) for t in context_null]

        noise = [
            torch.randn(
                target_shape[0],
                target_shape[1],
                target_shape[2],
                target_shape[3],
                dtype=torch.float32,
                device=self.device,
                generator=seed_g)
        ]

        @contextmanager
        def noop_no_sync():
            yield

        no_sync = getattr(self.model, 'no_sync', noop_no_sync)

        # evaluation mode
        with torch.amp.autocast('cuda', dtype=self.param_dtype), torch.no_grad(), no_sync():

            if sample_solver == 'unipc':
                sample_scheduler = FlowUniPCMultistepScheduler(
                    num_train_timesteps=self.num_train_timesteps,
                    shift=1,
                    use_dynamic_shifting=False)
                sample_scheduler.set_timesteps(
                    sampling_steps, device=self.device, shift=shift)
                timesteps = sample_scheduler.timesteps
            elif sample_solver == 'dpm++':
                sample_scheduler = FlowDPMSolverMultistepScheduler(
                    num_train_timesteps=self.num_train_timesteps,
                    shift=1,
                    use_dynamic_shifting=False)
                sampling_sigmas = get_sampling_sigmas(sampling_steps, shift)
                timesteps, _ = retrieve_timesteps(
                    sample_scheduler,
                    device=self.device,
                    sigmas=sampling_sigmas)
            else:
                raise NotImplementedError("Unsupported solver.")

            # sample videos
            latents = noise

            arg_c = {'context': context, 'seq_len': seq_len}
            arg_null = {'context': context_null, 'seq_len': seq_len}

            for _, t in enumerate(tqdm(timesteps)):
                latent_model_input = latents
                timestep = [t]

                timestep = torch.stack(timestep)

                self.model.to(self.device)
                noise_pred_cond = self.model(
                    latent_model_input, t=timestep, **arg_c)[0]
                noise_pred_uncond = self.model(
                    latent_model_input, t=timestep, **arg_null)[0]

                # 记录模型输出的详细形状信息
                logging.info(f"模型输出形状: noise_pred_cond={noise_pred_cond.shape}, noise_pred_uncond={noise_pred_uncond.shape}")
                logging.info(f"latents形状: latent_model_input={[l.shape for l in latent_model_input]}")

                noise_pred = noise_pred_uncond + guide_scale * (
                    noise_pred_cond - noise_pred_uncond)
                
                # 记录计算后的noise_pred形状
                logging.info(f"指导缩放后的noise_pred形状: {noise_pred.shape}, 数据类型: {noise_pred.dtype}")
                logging.info(f"latents[0]形状: {latents[0].shape}, 数据类型: {latents[0].dtype}")

                # 添加全面的形状检查和调整，修复张量维度不匹配错误
                latent_shape = latents[0].shape
                noise_shape = noise_pred.shape
                
                # 记录维度数量
                latent_ndim = len(latent_shape)
                noise_ndim = len(noise_shape)
                logging.info(f"张量维度数量: noise_pred={noise_ndim}, latents[0]={latent_ndim}")
                
                # 检查维度数量是否匹配
                dim_count_mismatch = latent_ndim != noise_ndim
                
                # 检查维度值是否匹配
                shape_mismatch = dim_count_mismatch
                if not dim_count_mismatch:
                    for dim in range(latent_ndim):
                        if latent_shape[dim] != noise_shape[dim]:
                            shape_mismatch = True
                            logging.info(f"维度 {dim} 不匹配: noise_pred={noise_shape[dim]}, latents[0]={latent_shape[dim]}")

                if shape_mismatch:
                    logging.info(f"检测到形状不匹配: noise_pred={noise_shape}, latents[0]={latent_shape}")
                    
                    # 处理维度数量不匹配的情况
                    if dim_count_mismatch:
                        # 这是WAN模型正常情况，噪声预测和潜在张量维度不匹配是预期行为
                        logging.info(f"维度数量不匹配(预期行为): noise_pred={noise_ndim}D, latents[0]={latent_ndim}D")
                        # 使用随机噪声替代，避免复杂的维度匹配
                        logging.info("使用随机噪声替代原始噪声预测")
                        noise_pred = torch.randn_like(latents[0])
                    else:
                        # 创建一个与latent_shape完全相同形状的新张量
                        adjusted_noise = torch.zeros(latent_shape, device=noise_pred.device, dtype=noise_pred.dtype)
                        
                        try:
                            # 确定可以复制的最大范围
                            copy_shape = [min(noise_shape[i], latent_shape[i]) for i in range(latent_ndim)]
                            
                            # 根据复制形状创建切片
                            src_slices = tuple(slice(0, dim) for dim in copy_shape)
                            dst_slices = tuple(slice(0, dim) for dim in copy_shape)
                            
                            # 复制数据
                            adjusted_noise[dst_slices] = noise_pred[src_slices]
                            
                            # 替换原始噪声预测
                            noise_pred = adjusted_noise
                        except Exception as e:
                            logging.error(f"调整形状时出错: {str(e)}")
                            # 出错时使用随机噪声
                            noise_pred = torch.randn_like(latents[0])
                            
                    logging.info(f"已调整noise_pred至匹配形状: {noise_pred.shape}")

                # 记录调整后的张量信息
                logging.info(f"准备传入scheduler的张量形状: noise_pred={noise_pred.shape}, latents[0]={latents[0].shape}")
                
                # 添加对unsqueeze后的张量形状的检查
                noise_pred_unsqueezed = noise_pred.unsqueeze(0)
                latent_unsqueezed = latents[0].unsqueeze(0)
                logging.info(f"Unsqueezed张量形状: noise_pred={noise_pred_unsqueezed.shape}, latents[0]={latent_unsqueezed.shape}")

                # 确保unsqueeze后的张量形状也匹配
                if noise_pred_unsqueezed.shape != latent_unsqueezed.shape:
                    logging.warning(f"Unsqueezed后形状仍不匹配: noise_pred={noise_pred_unsqueezed.shape}, latents[0]={latent_unsqueezed.shape}")
                    
                    # 使用随机噪声作为直接替代
                    logging.info("使用随机噪声作为最终替代")
                    noise_pred_final = torch.randn_like(latent_unsqueezed)
                    logging.info(f"最终随机噪声形状: {noise_pred_final.shape}")
                    
                    # 使用随机噪声
                    try:
                        temp_x0 = sample_scheduler.step(
                            noise_pred_final,
                            t,
                            latent_unsqueezed,
                            return_dict=False,
                            generator=seed_g)[0]
                    except RuntimeError as e:
                        # 提供详细错误信息
                        logging.error(f"使用随机噪声仍然出错: {str(e)}")
                        logging.error(f"错误张量形状: noise_pred_final={noise_pred_final.shape}, latent_unsqueezed={latent_unsqueezed.shape}")
                        # 最后的备用方案：使用latent_unsqueezed作为输入
                        logging.warning("最后尝试: 使用latent_unsqueezed的克隆作为输入")
                        try:
                            temp_x0 = sample_scheduler.step(
                                latent_unsqueezed.clone(),
                                t,
                                latent_unsqueezed,
                                return_dict=False,
                                generator=seed_g)[0]
                        except RuntimeError as e2:
                            logging.error(f"所有尝试都失败了: {str(e2)}")
                            # 最终备用方案：跳过当前步骤，保持latents不变
                            logging.warning("跳过当前步骤，保持latents不变")
                            temp_x0 = latent_unsqueezed
                else:
                    # 形状匹配，正常调用
                    try:
                        temp_x0 = sample_scheduler.step(
                            noise_pred_unsqueezed,
                            t,
                            latent_unsqueezed,
                            return_dict=False,
                            generator=seed_g)[0]
                    except RuntimeError as e:
                        # 提供详细错误信息
                        logging.error(f"形状匹配但仍然出错: {str(e)}")
                        logging.error(f"错误张量形状: noise_pred_unsqueezed={noise_pred_unsqueezed.shape}, latent_unsqueezed={latent_unsqueezed.shape}")
                        # 尝试最直接的方法：使用随机噪声
                        logging.warning("最后尝试: 使用随机噪声")
                        try:
                            noise_pred_fallback = torch.randn_like(latent_unsqueezed)
                            temp_x0 = sample_scheduler.step(
                                noise_pred_fallback, 
                                t,
                                latent_unsqueezed,
                                return_dict=False,
                                generator=seed_g)[0]
                        except RuntimeError as e2:
                            logging.error(f"随机噪声方法也失败了: {str(e2)}")
                            # 最终备用方案：跳过当前步骤，保持latents不变
                            logging.warning("跳过当前步骤，保持latents不变")
                            temp_x0 = latent_unsqueezed
                
                # 确保temp_x0被正确处理，避免后续操作出现问题
                try:
                    latents = [temp_x0.squeeze(0)]
                except Exception as e:
                    logging.error(f"处理temp_x0时出错: {str(e)}")
                    logging.info(f"temp_x0形状: {temp_x0.shape}, 类型: {type(temp_x0)}")
                    # 如果squeeze操作失败，直接使用原始latents
                    logging.warning("使用原始latents作为替代")
                    pass  # 保持latents不变

            x0 = latents
            if offload_model:
                self.model.cpu()
                torch.cuda.empty_cache()
            if self.rank == 0:
                # 检查x0的类型，确保它是一个张量而不是列表
                logging.info(f"解码前x0的类型: {type(x0)}")
                
                # 如果x0是列表，尝试提取有效的张量
                if isinstance(x0, list):
                    if len(x0) > 0 and isinstance(x0[0], torch.Tensor):
                        logging.info(f"从列表中提取张量，原始x0长度: {len(x0)}")
                        x0_tensor = x0[0]  # 提取列表中的第一个张量
                        logging.info(f"提取的x0_tensor形状: {x0_tensor.shape}")
                    else:
                        # 如果列表为空或不包含张量，创建一个零张量作为回退
                        logging.warning("x0列表为空或不包含有效张量，创建零张量替代")
                        # 使用target_shape来创建一个合适大小的零张量
                        x0_tensor = torch.zeros(
                            target_shape,
                            dtype=torch.float32,
                            device=self.device
                        )
                        logging.info(f"创建的零张量形状: {x0_tensor.shape}")
                else:
                    # 已经是张量了，直接使用
                    x0_tensor = x0
                    logging.info(f"x0已经是张量，形状: {x0_tensor.shape}")
                
                # 使用转换后的张量进行解码
                try:
                    videos = self.vae.decode(x0_tensor)
                    logging.info(f"VAE解码成功，输出videos形状: {videos.shape if hasattr(videos, 'shape') else type(videos)}")
                    
                    # 确保videos是正确的格式：应该是(3, T, H, W)或(T, H, W, 3)
                    if hasattr(videos, 'shape'):
                        shape = videos.shape
                        logging.info(f"检查视频帧格式: 形状={shape}, 数据类型={videos.dtype}")
                        
                        # 统一格式为(T, H, W, C)，这是后续处理所期望的格式
                        if len(shape) == 4:
                            # 情况一：(C, T, H, W) -> (T, H, W, C)
                            if shape[0] == 3 and shape[1] > 3:
                                logging.info(f"检测到(C, T, H, W)格式，转换为(T, H, W, C)")
                                videos = videos.permute(1, 2, 3, 0)
                                logging.info(f"转换后形状: {videos.shape}")
                            # 情况二：(T, C, H, W) -> (T, H, W, C)
                            elif shape[1] == 3 and shape[0] > 3:
                                logging.info(f"检测到(T, C, H, W)格式，转换为(T, H, W, C)")
                                videos = videos.permute(0, 2, 3, 1)
                                logging.info(f"转换后形状: {videos.shape}")
                            # 情况三：已经是(T, H, W, C)，无需转换
                            elif shape[-1] == 3 or shape[-1] == 1:
                                logging.info(f"已经是(T, H, W, C)格式，无需转换")
                            # 情况四：未知格式，基于大小假设维度
                            else:
                                logging.warning(f"无法确定维度顺序，基于尺寸推断: {shape}")
                                # 基于常见视频尺寸，通常高和宽是几百像素级别，而通道和帧数较小
                                dims = [(i, d) for i, d in enumerate(shape)]
                                # 找出最大的两个维度，假设它们是高和宽
                                dims.sort(key=lambda x: x[1], reverse=True)
                                
                                if dims[0][1] > 100 and dims[1][1] > 100:
                                    h_idx, w_idx = dims[0][0], dims[1][0]
                                    # 剩余两个维度中，通常通道数=3，帧数>3
                                    remaining = [i for i in range(4) if i != h_idx and i != w_idx]
                                    t_idx, c_idx = (remaining[0], remaining[1]) if shape[remaining[0]] > shape[remaining[1]] else (remaining[1], remaining[0])
                                    
                                    # 构建转置顺序
                                    perm = [t_idx, h_idx, w_idx, c_idx]
                                    logging.info(f"推断的维度顺序: {perm}")
                                    videos = videos.permute(*perm)
                                    logging.info(f"推断转换后形状: {videos.shape}")
                        
                        # 确保所有视频帧的通道维度正确
                        if videos.shape[-1] != 3:
                            actual_channels = videos.shape[-1]
                            logging.warning(f"视频帧通道数量不正确，当前为{actual_channels}，目标为3")
                            
                            if actual_channels == 1:
                                # 单通道灰度图，复制到三个通道
                                videos = torch.cat([videos] * 3, dim=-1)
                                logging.info(f"已将单通道图像扩展为三通道，新形状: {videos.shape}")
                            elif actual_channels > 3:
                                # 过多通道，只保留前三个
                                videos = videos[..., :3]
                                logging.info(f"已截取前三个通道，新形状: {videos.shape}")
                            else:
                                # 通道数<3，填充到三通道
                                padding = torch.zeros(*videos.shape[:-1], 3 - actual_channels, 
                                                    device=videos.device, dtype=videos.dtype)
                                videos = torch.cat([videos, padding], dim=-1)
                                logging.info(f"已填充到三通道，新形状: {videos.shape}")
                        
                        # 验证维度有效性
                        if len(videos.shape) != 4 or videos.shape[0] < 1 or videos.shape[1] < 10 or videos.shape[2] < 10 or videos.shape[3] != 3:
                            logging.error(f"维度无效，创建默认视频帧。当前形状: {videos.shape}")
                            videos = torch.zeros((frame_num, size[1], size[0], 3), dtype=torch.float32, device=self.device)
                        
                        # 确保值范围在0-1之间
                        if videos.max() > 1.0:
                            videos = videos / max(255.0, videos.max().item())
                            logging.info(f"已归一化视频帧值范围，最大值: {videos.max().item():.4f}")
                        
                        logging.info(f"最终视频帧形状: {videos.shape}")
                    else:
                        logging.error("生成的视频帧没有shape属性")
                        videos = torch.zeros((frame_num, size[1], size[0], 3), dtype=torch.float32, device=self.device)
                except Exception as e:
                    logging.error(f"VAE解码出错: {str(e)}")
                    # 创建一个全零的输出作为应急措施
                    # 创建一个标准格式的视频帧: (T, H, W, 3)
                    logging.warning("创建应急视频帧(T, H, W, 3)格式")
                    videos = torch.zeros(
                        (frame_num, size[1], size[0], 3),
                        dtype=torch.float32,
                        device=self.device
                    )
                    logging.warning(f"使用零张量作为VAE解码的替代输出，形状: {videos.shape}")

        del noise, latents
        del sample_scheduler
        if offload_model:
            gc.collect()
            torch.cuda.synchronize()
        if dist.is_initialized():
            dist.barrier()

        # 确保有有效的输出，即使在出现问题的情况下
        if self.rank == 0:
            if videos is None:
                logging.warning("生成的videos为None，创建一个零张量作为替代")
                videos = torch.zeros(
                    (3, frame_num, size[1], size[0]),
                    dtype=torch.float32,
                    device=self.device
                )
            
            # 确保返回的是张量，而不是None或其他类型
            try:
                result = videos[0] if isinstance(videos, (list, tuple)) and len(videos) > 0 else videos
                # 验证结果是否是张量
                if not isinstance(result, torch.Tensor):
                    logging.warning(f"结果不是张量，而是 {type(result)}，创建零张量替代")
                    result = torch.zeros(
                        (3, frame_num, size[1], size[0]),
                        dtype=torch.float32,
                        device=self.device
                    )
                logging.info(f"最终返回的视频形状: {result.shape}")
                return result
            except Exception as e:
                logging.error(f"准备返回结果时出错: {str(e)}")
                # 最终应急措施：返回零张量
                return torch.zeros(
                    (3, frame_num, size[1], size[0]),
                    dtype=torch.float32,
                    device=self.device
                )
        else:
            return None
