#!/usr/bin/env python3
"""
视频生成回退方案
当Wanx 2.1不稳定时使用的备用视频生成方法
"""

import os
import sys
import time
import subprocess
from pathlib import Path
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

def generate_video_with_timeout(cmd, timeout_seconds=300, work_dir=None):
    """
    执行视频生成命令，带超时控制
    
    Args:
        cmd: 命令列表
        timeout_seconds: 超时时间（秒）
        work_dir: 工作目录
    
    Returns:
        (success, output, error)
    """
    try:
        logger.info(f"执行命令: {' '.join(cmd)}")
        logger.info(f"工作目录: {work_dir}")
        logger.info(f"超时设置: {timeout_seconds}秒")
        
        start_time = time.time()
        
        # 使用Popen以便可以监控进程
        process = subprocess.Popen(
            cmd,
            cwd=work_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        # 监控进程
        output_lines = []
        error_lines = []
        
        while True:
            # 检查进程是否完成
            if process.poll() is not None:
                # 进程已完成，读取剩余输出
                remaining_stdout, remaining_stderr = process.communicate()
                if remaining_stdout:
                    output_lines.append(remaining_stdout)
                if remaining_stderr:
                    error_lines.append(remaining_stderr)
                break
            
            # 检查超时
            elapsed = time.time() - start_time
            if elapsed > timeout_seconds:
                logger.warning(f"进程超时 ({timeout_seconds}秒)，终止进程")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                    time.sleep(1)
                
                return False, "", f"进程超时 ({timeout_seconds}秒)"
            
            # 短暂等待
            time.sleep(1)
        
        # 获取结果
        stdout = ''.join(output_lines)
        stderr = ''.join(error_lines)
        
        success = process.returncode == 0
        elapsed_time = time.time() - start_time
        
        logger.info(f"进程完成，返回码: {process.returncode}, 耗时: {elapsed_time:.1f}秒")
        
        if not success:
            logger.error(f"进程失败: {stderr}")
        
        return success, stdout, stderr
        
    except Exception as e:
        logger.error(f"执行命令异常: {e}")
        return False, "", str(e)

def generate_wanx_video_robust(
    prompt,
    output_path,
    model="t2v-1.3B",
    size="832*480",
    guidance_scale=6.0,
    steps=15,
    frames=9,
    seed=None,
    timeout=180  # 3分钟超时
):
    """
    稳定的Wanx视频生成
    
    Args:
        prompt: 提示词
        output_path: 输出路径
        model: 模型类型
        size: 分辨率
        guidance_scale: 引导强度
        steps: 推理步数
        frames: 帧数
        seed: 随机种子
        timeout: 超时时间（秒）
    
    Returns:
        (success, message, actual_output_path)
    """
    try:
        # Wanx模型路径
        wan_model_path = Path(__file__).parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
        wan_script_path = wan_model_path / "generate.py"
        
        if not wan_script_path.exists():
            return False, f"Wanx脚本不存在: {wan_script_path}", None
        
        # 检查模型目录
        if "1.3B" in model:
            ckpt_dir = wan_model_path / "Wan2.1-T2V-1.3B"
        else:
            ckpt_dir = wan_model_path / "Wan2.1-T2V-14B"
        
        if not ckpt_dir.exists():
            return False, f"模型目录不存在: {ckpt_dir}", None
        
        # 生成随机种子
        if seed is None:
            seed = int(time.time()) % 10000
        
        # 构建命令 - 使用最保守的参数
        relative_output = f"./{output_path.name}"
        
        cmd = [
            sys.executable,
            "generate.py",
            "--task", model,
            "--size", size,
            "--ckpt_dir", f"./Wan2.1-T2V-1.3B",
            "--prompt", prompt,
            "--offload_model", "True",
            "--t5_cpu",
            "--sample_shift", "8",
            "--sample_guide_scale", str(guidance_scale),
            "--sample_steps", str(steps),
            "--frame_num", str(frames),
            "--base_seed", str(seed),
            "--save_file", relative_output
        ]
        
        logger.info(f"开始Wanx视频生成: {prompt}")
        logger.info(f"参数: {size}, {frames}帧, {steps}步, 引导强度{guidance_scale}")
        
        # 执行生成
        success, stdout, stderr = generate_video_with_timeout(
            cmd, 
            timeout_seconds=timeout,
            work_dir=str(wan_model_path)
        )
        
        if success:
            # 检查输出文件
            temp_output = wan_model_path / relative_output.lstrip('./')
            if temp_output.exists() and temp_output.stat().st_size > 0:
                # 移动到最终位置
                import shutil
                output_path.parent.mkdir(parents=True, exist_ok=True)
                shutil.move(str(temp_output), str(output_path))
                
                logger.info(f"视频生成成功: {output_path}")
                return True, "视频生成成功", output_path
            else:
                logger.error(f"输出文件不存在或为空: {temp_output}")
                return False, "输出文件生成失败", None
        else:
            logger.error(f"Wanx生成失败: {stderr}")
            return False, f"生成失败: {stderr}", None
            
    except Exception as e:
        logger.error(f"视频生成异常: {e}")
        return False, f"生成异常: {e}", None

def create_placeholder_video(output_path, prompt, duration=5):
    """
    创建占位符视频（当真实生成失败时）
    """
    try:
        # 创建一个简单的占位符视频
        import cv2
        import numpy as np
        
        # 视频参数
        width, height = 832, 480
        fps = 8
        total_frames = duration * fps
        
        # 创建视频写入器
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        out = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
        
        # 生成帧
        for frame_idx in range(total_frames):
            # 创建渐变背景
            frame = np.zeros((height, width, 3), dtype=np.uint8)
            
            # 添加渐变色
            for y in range(height):
                color_value = int(255 * (y / height))
                frame[y, :] = [color_value // 3, color_value // 2, color_value]
            
            # 添加文本
            text = f"Video: {prompt}"
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 1
            color = (255, 255, 255)
            thickness = 2
            
            # 计算文本位置
            text_size = cv2.getTextSize(text, font, font_scale, thickness)[0]
            text_x = (width - text_size[0]) // 2
            text_y = (height + text_size[1]) // 2
            
            cv2.putText(frame, text, (text_x, text_y), font, font_scale, color, thickness)
            
            # 添加帧数信息
            frame_text = f"Frame: {frame_idx + 1}/{total_frames}"
            cv2.putText(frame, frame_text, (10, 30), font, 0.7, (255, 255, 255), 1)
            
            out.write(frame)
        
        out.release()
        
        logger.info(f"占位符视频创建成功: {output_path}")
        return True, "占位符视频创建成功", output_path
        
    except Exception as e:
        logger.error(f"创建占位符视频失败: {e}")
        return False, f"创建占位符视频失败: {e}", None

if __name__ == "__main__":
    # 测试脚本
    output_path = Path("test_output.mp4")
    
    print("测试Wanx视频生成...")
    success, message, result_path = generate_wanx_video_robust(
        prompt="cat running",
        output_path=output_path,
        timeout=120  # 2分钟超时
    )
    
    if success:
        print(f"✅ 生成成功: {result_path}")
    else:
        print(f"❌ 生成失败: {message}")
        
        print("创建占位符视频...")
        success2, message2, result_path2 = create_placeholder_video(
            output_path, "cat running"
        )
        
        if success2:
            print(f"✅ 占位符创建成功: {result_path2}")
        else:
            print(f"❌ 占位符创建失败: {message2}")
