/**
 * 视频生成服务
 * 提供文生视频、图生视频等功能的API调用
 */
import api from './api.js';

class VideoService {
  constructor() {
    this.baseUrl = '/api/v1/video-generation';
  }

  /**
   * 生成视频
   * @param {Object} params 生成参数
   * @param {string} params.prompt 提示词
   * @param {string} params.model 模型名称
   * @param {number} params.duration 视频时长（秒）
   * @param {string} params.resolution 分辨率
   * @param {number} params.fps 帧率
   * @param {number} params.guidance_scale 引导强度
   * @param {string} params.negative_prompt 负面提示词
   * @returns {Promise} API响应
   */
  async generateVideo(params) {
    try {
      console.log('发送视频生成请求:', params);
      
      const response = await api.post(`${this.baseUrl}/generate`, {
        prompt: params.prompt,
        model: params.model || 't2v-1.3B',
        duration: params.duration || 10,
        resolution: params.resolution || '768x512',
        fps: params.fps || 24,
        guidance_scale: params.guidance_scale || 7.5,
        negative_prompt: params.negative_prompt || '',
        // 添加其他可能需要的参数
        num_frames: Math.ceil((params.duration || 10) * (params.fps || 24)),
        sample_steps: 50,
        cfg_scale: params.guidance_scale || 7.5
      });

      console.log('视频生成API响应:', response);
      return response.data;
    } catch (error) {
      console.error('视频生成请求失败:', error);
      throw error;
    }
  }

  /**
   * 获取视频任务状态
   * @param {string} taskId 任务ID
   * @returns {Promise} 任务状态
   */
  async getVideoTask(taskId) {
    try {
      console.log('获取视频任务状态:', taskId);
      
      const response = await api.get(`${this.baseUrl}/task/${taskId}`);
      console.log('任务状态响应:', response);
      
      return response.data;
    } catch (error) {
      console.error('获取任务状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取视频任务进度（支持轮询）
   * @param {string} taskId 任务ID
   * @param {boolean} autoPolling 是否自动轮询
   * @returns {Promise} 任务进度
   */
  async getVideoTaskProgress(taskId, autoPolling = false) {
    try {
      console.log('获取视频任务进度:', taskId, '自动轮询:', autoPolling);

      if (autoPolling) {
        // 自动轮询模式
        return new Promise((resolve, reject) => {
          const pollInterval = 2000; // 2秒轮询一次
          const maxAttempts = 150; // 最多轮询150次（5分钟）
          let attempts = 0;
          let syncAttempted = false; // 是否已尝试同步

          const poll = async () => {
            try {
              attempts++;
              console.log(`轮询进度 - 第${attempts}次尝试`);

              const response = await api.get(`${this.baseUrl}/task/${taskId}/progress`);
              const result = response.data;

              console.log('进度轮询响应:', result);

              // 检查任务是否完成
              if (result.status === 'completed' || result.status === 'COMPLETED' ||
                  result.status === 'SUCCESS') {
                console.log('任务完成，停止轮询');
                resolve(result);
                return;
              }

              // 检查任务是否失败
              if (result.status === 'failed' || result.status === 'FAILED' ||
                  result.status === 'ERROR') {
                console.log('任务失败，停止轮询');
                resolve(result);
                return;
              }

              // 如果进度长时间停滞在低值，尝试强制同步一次
              if (!syncAttempted && attempts > 5 && result.progress < 10) {
                console.log('检测到进度停滞，尝试强制同步状态...');
                syncAttempted = true;
                try {
                  await this.syncTaskStatus(taskId);
                  console.log('状态同步完成，继续轮询...');
                } catch (syncError) {
                  console.warn('状态同步失败:', syncError);
                }
              }

              // 检查是否超过最大尝试次数
              if (attempts >= maxAttempts) {
                console.log('达到最大轮询次数，停止轮询');
                resolve({
                  status: 'timeout',
                  message: '任务超时',
                  progress: result.progress || 0
                });
                return;
              }

              // 继续轮询
              setTimeout(poll, pollInterval);

            } catch (error) {
              console.error('轮询进度失败:', error);

              // 如果是网络错误，继续重试
              if (attempts < maxAttempts) {
                setTimeout(poll, pollInterval);
              } else {
                reject(error);
              }
            }
          };

          // 开始轮询
          poll();
        });
      } else {
        // 单次查询模式
        const response = await api.get(`${this.baseUrl}/task/${taskId}/progress`);
        return response.data;
      }
    } catch (error) {
      console.error('获取任务进度失败:', error);
      throw error;
    }
  }

  /**
   * 强制同步任务状态
   * @param {string} taskId 任务ID
   * @returns {Promise} 同步结果
   */
  async syncTaskStatus(taskId) {
    try {
      console.log('强制同步任务状态:', taskId);

      const response = await api.post(`${this.baseUrl}/task/${taskId}/sync`);
      console.log('状态同步响应:', response);

      return response.data;
    } catch (error) {
      console.error('同步任务状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取视频任务列表
   * @param {Object} params 查询参数
   * @param {number} params.limit 限制数量
   * @param {number} params.offset 偏移量
   * @returns {Promise} 任务列表
   */
  async getVideoTasks(params = {}) {
    try {
      console.log('获取视频任务列表:', params);
      
      const queryParams = new URLSearchParams({
        limit: params.limit || 20,
        offset: params.offset || 0,
        ...params
      });
      
      const response = await api.get(`${this.baseUrl}/tasks?${queryParams}`);
      console.log('任务列表响应:', response);
      
      return response.data;
    } catch (error) {
      console.error('获取任务列表失败:', error);
      throw error;
    }
  }

  /**
   * 删除视频任务
   * @param {string} taskId 任务ID
   * @returns {Promise} 删除结果
   */
  async deleteVideoTask(taskId) {
    try {
      console.log('删除视频任务:', taskId);
      
      const response = await api.delete(`${this.baseUrl}/task/${taskId}`);
      console.log('删除任务响应:', response);
      
      return response.data;
    } catch (error) {
      console.error('删除任务失败:', error);
      throw error;
    }
  }

  /**
   * 图生视频
   * @param {Object} params 生成参数
   * @param {string} params.image_url 图片URL
   * @param {string} params.prompt 提示词
   * @param {number} params.duration 视频时长
   * @returns {Promise} API响应
   */
  async generateVideoFromImage(params) {
    try {
      console.log('发送图生视频请求:', params);
      
      const response = await api.post(`${this.baseUrl}/image-to-video`, {
        image_url: params.image_url,
        prompt: params.prompt || '',
        duration: params.duration || 5,
        model: params.model || 'i2v-1.3B',
        resolution: params.resolution || '768x512',
        fps: params.fps || 24,
        guidance_scale: params.guidance_scale || 7.5
      });

      console.log('图生视频API响应:', response);
      return response.data;
    } catch (error) {
      console.error('图生视频请求失败:', error);
      throw error;
    }
  }

  /**
   * 获取视频文件URL
   * @param {string} taskId 任务ID
   * @returns {string} 视频文件URL
   */
  getVideoUrl(taskId) {
    return `${this.baseUrl}/video/${taskId}`;
  }

  /**
   * 获取视频缩略图URL
   * @param {string} taskId 任务ID
   * @returns {string} 缩略图URL
   */
  getThumbnailUrl(taskId) {
    return `${this.baseUrl}/thumbnail/${taskId}`;
  }

  /**
   * 下载视频
   * @param {string} taskId 任务ID
   * @param {string} filename 文件名
   * @returns {Promise} 下载结果
   */
  async downloadVideo(taskId, filename) {
    try {
      const videoUrl = this.getVideoUrl(taskId);
      
      // 创建下载链接
      const link = document.createElement('a');
      link.href = videoUrl;
      link.download = filename || `video_${taskId}.mp4`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      return { success: true };
    } catch (error) {
      console.error('下载视频失败:', error);
      throw error;
    }
  }

  /**
   * 检查视频URL是否可访问
   * @param {string} url 视频URL
   * @returns {Promise<boolean>} 是否可访问
   */
  async checkVideoAccessibility(url) {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      console.error('检查视频可访问性失败:', error);
      return false;
    }
  }

  /**
   * 获取系统状态
   * @returns {Promise} 系统状态
   */
  async getSystemStatus() {
    try {
      const response = await api.get('/api/v1/system/status');
      return response.data;
    } catch (error) {
      console.error('获取系统状态失败:', error);
      throw error;
    }
  }

  /**
   * 修复黑屏问题
   * @returns {Promise} 修复结果
   */
  async fixBlackScreenIssue() {
    try {
      const response = await api.post('/api/v1/system/fix-black-screen');
      return response.data;
    } catch (error) {
      console.error('修复黑屏问题失败:', error);
      throw error;
    }
  }
}

// 创建并导出服务实例
const videoService = new VideoService();
export default videoService;
