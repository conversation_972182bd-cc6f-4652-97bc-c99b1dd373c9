#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Optional, Dict, Any, List, Tuple
import os
import json
import sys
import logging
import importlib.util
import subprocess
from pathlib import Path
import time

class WanModel(nn.Module):
    """
    WAN模型的包装类，用于与系统其他部分交互
    """
    
    def __init__(self, config):
        """
        初始化WAN模型
        
        Args:
            config: 模型配置对象
        """
        super().__init__()
        self.config = config
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.dtype = torch.float16 if torch.cuda.is_available() else torch.float32
        
        # 设置日志
        self.logger = logging.getLogger("WAN-Model")
        self.logger.info("初始化WAN模型")
        
        # 查找WAN2.1根目录
        self.wan_root = self._find_wan_root()
        self.logger.info(f"WAN2.1根目录: {self.wan_root}")
        
        # 导入WAN模块
        if self.wan_root:
            # 将WAN2.1根目录添加到sys.path
            if self.wan_root not in sys.path:
                sys.path.insert(0, str(self.wan_root))
            
            try:
                import wan
                self.wan_module_available = True
                self.logger.info("成功导入WAN模块")
            except ImportError:
                self.wan_module_available = False
                self.logger.error("无法导入WAN模块，将使用占位符实现")
        else:
            self.wan_module_available = False
            self.logger.error("未找到WAN2.1根目录，将使用占位符实现")
    
    def _find_wan_root(self):
        """查找WAN2.1根目录"""
        # 从当前文件位置向上查找
        current_path = Path(__file__).resolve()
        
        # 向上查找最多5层
        for _ in range(5):
            current_path = current_path.parent
            # 检查是否为WAN2.1根目录
            if (current_path / "generate.py").exists() and (current_path / "wan").exists():
                return current_path
        
        # 如果没有找到，尝试使用预定义路径
        possible_paths = [
            Path("backend/local_models/wan/Wan2.1"),
            Path("local_models/wan/Wan2.1"),
            Path("wan/Wan2.1"),
            Path("Wan2.1")
        ]
        
        for path in possible_paths:
            abs_path = Path.cwd() / path
            if (abs_path / "generate.py").exists() and (abs_path / "wan").exists():
                return abs_path
        
        return None
    
    def forward(self, x, t=None, context=None, seq_len=None):
        """
        模型前向传播方法（占位符）
        """
        # 这只是一个占位符，实际模型应该实现真正的前向传播逻辑
        return x
    
    @classmethod
    def from_pretrained(cls, model_path):
        """
        从预训练模型加载
        
        Args:
            model_path: 预训练模型路径
            
        Returns:
            WanModel: 加载的模型实例
        """
        # 创建配置对象
        class ConfigObject:
            def __init__(self, config_dict):
                for key, value in config_dict.items():
                    setattr(self, key, value)
            
            def get(self, key, default=None):
                return getattr(self, key, default)
        
        # 创建基本配置
        config = ConfigObject({
            'dim': 1536,
            'num_layers': 30,
            'num_heads': 12,
            'text_len': 512,
            'in_dim': 16,
            'out_dim': 16
        })
        
        # 创建模型实例
        model = cls(config)
        
        # 设置模型路径
        model.model_path = model_path
        
        return model
    
    def generate(self, inputs, steps=50, cfg_scale=8.0, seed=None):
        """
        生成视频的函数
        
        Args:
            inputs: 输入参数字典，包含提示词、尺寸等信息
            steps: 采样步数
            cfg_scale: 分类器自由引导系数
            seed: 随机种子
            
        Returns:
            生成的视频张量，格式为(T, H, W, C)
        """
        import logging
        logger = logging.getLogger("WAN-Model")
        
        # 提取输入参数
        prompt = inputs.get("prompt", "一个美丽的花园")
        width = inputs.get("width", 1024)
        height = inputs.get("height", 576)
        frames = inputs.get("frames", 16)
        
        logger.info(f"生成视频: 提示词='{prompt}', 尺寸={width}x{height}, 帧数={frames}")
        logger.info(f"采样参数: 步数={steps}, 引导系数={cfg_scale}")
        
        # 设置随机种子
        if seed is not None:
            import random
            import numpy as np
            torch.manual_seed(seed)
            torch.cuda.manual_seed_all(seed)
            np.random.seed(seed)
            random.seed(seed)
        
        # 检查是否可以使用WAN模块
        if hasattr(self, 'wan_module_available') and self.wan_module_available and self.wan_root:
            try:
                # 使用临时文件存储生成的视频
                import tempfile
                import os
                from pathlib import Path
                
                # 创建临时目录
                temp_dir = tempfile.mkdtemp()
                output_file = os.path.join(temp_dir, f"temp_video_{int(time.time())}.mp4")
                
                # 构建命令
                cmd = [
                    sys.executable,
                    os.path.join(self.wan_root, "generate.py"),
                    "--text", prompt,
                    "--width", str(width),
                    "--height", str(height),
                    "--length", str(frames),
                    "--output", output_file,
                    "--cfg", str(cfg_scale),
                    "--steps", str(steps),
                    "--device", self.device
                ]
                
                if seed is not None:
                    cmd.extend(["--seed", str(seed)])
                
                # 启用增强处理
                cmd.append("--enhance")
                
                # 如果是中文提示词，添加中文参数
                if any('\u4e00' <= char <= '\u9fff' for char in prompt):
                    cmd.append("--chinese")
                
                # 确保cmd列表中的所有元素都是字符串类型
                cmd = [str(item) if item is not None else "" for item in cmd]
                
                # 记录执行的命令
                logger.info(f"执行命令: {' '.join(cmd)}")
                
                # 执行命令
                process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    bufsize=1,
                    universal_newlines=True
                )
                
                # 读取输出
                while True:
                    stdout_line = process.stdout.readline()
                    if stdout_line:
                        logger.info(stdout_line.strip())
                    
                    stderr_line = process.stderr.readline()
                    if stderr_line:
                        logger.error(stderr_line.strip())
                    
                    # 检查进程是否结束
                    if process.poll() is not None and not stdout_line and not stderr_line:
                        break
                
                # 获取剩余输出
                remaining_stdout, remaining_stderr = process.communicate()
                if remaining_stdout:
                    for line in remaining_stdout.strip().split("\n"):
                        if line:
                            logger.info(line)
                if remaining_stderr:
                    for line in remaining_stderr.strip().split("\n"):
                        if line:
                            logger.error(line)
                
                # 检查执行结果
                return_code = process.returncode
                if return_code != 0:
                    logger.error(f"WAN脚本执行失败，返回码: {return_code}")
                    # 失败时返回随机噪声
                    return torch.rand((frames, height, width, 3), device=self.device)
                
                # 检查输出文件是否存在
                if not os.path.exists(output_file):
                    logger.error(f"输出文件不存在: {output_file}")
                    # 失败时返回随机噪声
                    return torch.rand((frames, height, width, 3), device=self.device)
                
                # 读取生成的视频
                import cv2
                import numpy as np
                
                cap = cv2.VideoCapture(output_file)
                frames_list = []
                
                while True:
                    ret, frame = cap.read()
                    if not ret:
                        break
                    
                    # 转换为RGB
                    frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    frames_list.append(frame)
                
                cap.release()
                
                # 转换为张量
                if frames_list:
                    video_tensor = torch.tensor(np.array(frames_list), dtype=torch.float32, device=self.device) / 255.0
                    logger.info(f"成功加载生成的视频，张量形状: {video_tensor.shape}")
                    return video_tensor
                else:
                    logger.error("无法从生成的视频中提取帧")
                    # 失败时返回随机噪声
                    return torch.rand((frames, height, width, 3), device=self.device)
                
            except Exception as e:
                import traceback
                logger.error(f"使用WAN模块生成视频时出错: {str(e)}")
                logger.error(traceback.format_exc())
                # 出错时返回随机噪声
                return torch.rand((frames, height, width, 3), device=self.device)
        
        # 如果无法使用WAN模块，使用占位符实现
        logger.warning("使用占位符实现生成视频")
        
        # 创建随机视频张量
        video = torch.rand((frames, height, width, 3), device=self.device)
        
        # 添加一些视觉元素，使其不仅仅是纯噪声
        # 添加随机渐变效果 - 确保在同一设备上
        y = torch.linspace(0, 1, height).view(1, height, 1, 1).repeat(frames, 1, width, 3).to(self.device)
        x = torch.linspace(0, 1, width).view(1, 1, width, 1).repeat(frames, height, 1, 3).to(self.device)
        t = torch.linspace(0, 1, frames).view(frames, 1, 1, 1).repeat(1, height, width, 3).to(self.device)
        
        # 混合随机张量和渐变效果
        video = video * 0.7 + (x * y * t) * 0.3
        
        # 确保值在0-1范围内
        video = torch.clamp(video, 0.0, 1.0)
        
        logger.info(f"视频生成完成，张量形状: {video.shape}")
        return video
        
def build_model(device="cuda", dtype=torch.float16):
    """
    构建WAN模型的工厂函数
    
    Args:
        device: 运行设备
        dtype: 数据类型
        
    Returns:
        WanModel: 构建的模型实例
    """
    import logging
    logger = logging.getLogger("WAN-Model")
    logger.info(f"构建WAN模型 (设备: {device}, 类型: {dtype})")
    
    # 创建配置
    config = type('obj', (object,), {
        'get': lambda self, key, default=None: getattr(self, str(key), default) if isinstance(key, (int, str)) else default,
        'dim': 1536,
        'num_layers': 30,
        'num_heads': 12,
        'text_len': 512,
        'in_dim': 16,
        'out_dim': 16
    })
    
    # 创建模型
    model = WanModel(config)
    
    # 设置设备和数据类型
    model.to(device=device, dtype=dtype)
    model.device = device
    model.dtype = dtype
    
    logger.info("WAN模型构建完成")
    return model 