xformers-0.0.31.post1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
xformers-0.0.31.post1.dist-info/LICENSE,sha256=xPdISGPi2k__n6EMnda0mDEa6uov8po3MbHeQX3dhCI,1645
xformers-0.0.31.post1.dist-info/METADATA,sha256=GH4FsSD0quvNBvPi-KKpprEtqFkyrD5LFRIi7VWUzDs,1074
xformers-0.0.31.post1.dist-info/RECORD,,
xformers-0.0.31.post1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers-0.0.31.post1.dist-info/WHEEL,sha256=pUu01ofZCIHFo5RcrlMZdZUe5NNmXquuEhBCj9dbhOY,100
xformers-0.0.31.post1.dist-info/top_level.txt,sha256=4Px1VcGhKk0j3XhKXjA8HtTm6EQOb0hazeJ5nQsNlKk,9
xformers/_C.pyd,sha256=kdLnpdFUYbLVKandX1xe_mgxxE6cT4LR5I-Ej_HZdGk,15212032
xformers/__init__.py,sha256=6Gn6UY8d4ij1NHxUkzc84pos2phtISO3FRh1q5Mnsaw,1783
xformers/__pycache__/__init__.cpython-311.pyc,,
xformers/__pycache__/_cpp_lib.cpython-311.pyc,,
xformers/__pycache__/_deprecation_warning.cpython-311.pyc,,
xformers/__pycache__/attn_bias_utils.cpython-311.pyc,,
xformers/__pycache__/checkpoint.cpython-311.pyc,,
xformers/__pycache__/info.cpython-311.pyc,,
xformers/__pycache__/test.cpython-311.pyc,,
xformers/__pycache__/utils.cpython-311.pyc,,
xformers/__pycache__/version.cpython-311.pyc,,
xformers/_cpp_lib.py,sha256=krwmMrZN5GR1Hm-wkOGZQ0fn8gqYz3Wd-RS_ISPBpgA,5113
xformers/_deprecation_warning.py,sha256=oASW0P7S8aHENqyPNRkgReNZimZZKLYlLxFF_GxRUX8,468
xformers/_flash_attn/__init__.py,sha256=-NPiS5WwomQeDiamj9JxE3yqZSadrkX1q4Vmq6Ql-Tk,302
xformers/_flash_attn/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/bert_padding.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/flash_attn_interface.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/flash_attn_triton.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/flash_attn_triton_og.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/flash_blocksparse_attention.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/flash_blocksparse_attn_interface.cpython-311.pyc,,
xformers/_flash_attn/__pycache__/fused_softmax.cpython-311.pyc,,
xformers/_flash_attn/bert_padding.py,sha256=dhN4U8f_JqPU2Qzo4fHNkYqThe3TGalR1eg4GEqlWe8,10148
xformers/_flash_attn/flash_attn_interface.py,sha256=iwPoG73fO_xB35MvhBD6LEMuT38hLj7qwgNlb_bl0hM,62283
xformers/_flash_attn/flash_attn_triton.py,sha256=W2NIPsie2qdcCfRcOcEqks8swWQFYgVOigfoOyFHq_4,42272
xformers/_flash_attn/flash_attn_triton_amd/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/bench.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/bwd_prefill.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/bwd_prefill_fused.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/bwd_prefill_onekernel.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/bwd_prefill_split.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/bwd_ref.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/fp8.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/fwd_decode.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/fwd_prefill.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/fwd_ref.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/interface_fa.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/test.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/train.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/__pycache__/utils.cpython-311.pyc,,
xformers/_flash_attn/flash_attn_triton_amd/bench.py,sha256=SXfGlxYXtwZfs-vhVzl2h1ABSy9MWLsNBQY7dvFx-gc,50440
xformers/_flash_attn/flash_attn_triton_amd/bwd_prefill.py,sha256=aOSvL5jKMATXcvDtRiJ_0x71Piqe57S49PdRfDzKCT8,27074
xformers/_flash_attn/flash_attn_triton_amd/bwd_prefill_fused.py,sha256=z-J8dtuJhgfbr_xLzPisEu09JmgKuTt5HeY3g5cC3h4,129493
xformers/_flash_attn/flash_attn_triton_amd/bwd_prefill_onekernel.py,sha256=wnsmYeGbdPWEQbLjbbiJN3bNLWcOic62ektddFm14mY,50695
xformers/_flash_attn/flash_attn_triton_amd/bwd_prefill_split.py,sha256=_3Q1Xq4sJ6h7P6tE7KEq5sKkrR7GFA3AFBWerTEtwqo,58828
xformers/_flash_attn/flash_attn_triton_amd/bwd_ref.py,sha256=yfuteAMVmoAv26a_fO7oRNnSheacSKJ-MnAgW1otisI,17840
xformers/_flash_attn/flash_attn_triton_amd/fp8.py,sha256=8PlzImdPRZhMuNVeFwnFo2ijNqiNsZ2hkqMPNmdCtaw,29451
xformers/_flash_attn/flash_attn_triton_amd/fwd_decode.py,sha256=cMTo6feEB_K0XK0Z9BD266m_dJR6clH2KYktR84PgaU,30389
xformers/_flash_attn/flash_attn_triton_amd/fwd_prefill.py,sha256=eV_GXq9PBCmfXmjUJvEHK2ofKdcUlGI_7jBtFBGa8Po,34412
xformers/_flash_attn/flash_attn_triton_amd/fwd_ref.py,sha256=MhKe03PiHHRDZdK7dKt7KHoCr8VYNGwKO_wa0R9H5O0,15502
xformers/_flash_attn/flash_attn_triton_amd/interface_fa.py,sha256=N9vm3e3kC1dAgDHzA6Xlr7IHMHOUsMk4ytWCvPmv9MM,31747
xformers/_flash_attn/flash_attn_triton_amd/test.py,sha256=AEB1slHD9iiaxSCirsjQHphjTPtgOS6Lhb6SyUhuQrc,35830
xformers/_flash_attn/flash_attn_triton_amd/train.py,sha256=tJ1HcE7YSsRDUXrDYNdHjWe4UjAXY4zuwby21f3gqbM,15163
xformers/_flash_attn/flash_attn_triton_amd/utils.py,sha256=aREdlHhDQJ8W2pUF3ixdPjl91-FRveI2rhqqueCzHqI,32997
xformers/_flash_attn/flash_attn_triton_og.py,sha256=LZm4Jlz0ECHtuOWEnpe69_iqUrNfNqGYT6AI5D4X5Qk,11693
xformers/_flash_attn/flash_blocksparse_attention.py,sha256=BvOsy6cS105Iijgmi0DgXl7-1PjUZoDxcMDztXvlyiA,7669
xformers/_flash_attn/flash_blocksparse_attn_interface.py,sha256=3z54--DCBdcS7cqF0oiC1Ux53Ye8o-TwbdSgdGJSea0,7465
xformers/_flash_attn/fused_softmax.py,sha256=-ZMBHj_1CjfOOZwsP9D1w1CZstUyRUVONUhz_rD5cAE,7994
xformers/_flash_attn/layers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/layers/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/layers/__pycache__/patch_embed.cpython-311.pyc,,
xformers/_flash_attn/layers/__pycache__/rotary.cpython-311.pyc,,
xformers/_flash_attn/layers/patch_embed.py,sha256=_2b237fpHQa2Q6lggjVHKllo1I7ofNju7ZugWlZieqQ,2203
xformers/_flash_attn/layers/rotary.py,sha256=zpfAGjekYXsB4y8o5lHJbOYZqFi6KfnATutqQ_fjjy0,18894
xformers/_flash_attn/losses/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/losses/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/losses/__pycache__/cross_entropy.cpython-311.pyc,,
xformers/_flash_attn/losses/cross_entropy.py,sha256=iOQYdYubepYe7CBenfgvFvdqAi_1kU1WGJmMauldT-I,3282
xformers/_flash_attn/models/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/models/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/baichuan.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/bert.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/bigcode.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/btlm.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/falcon.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/gpt.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/gpt_neox.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/gptj.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/llama.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/opt.cpython-311.pyc,,
xformers/_flash_attn/models/__pycache__/vit.cpython-311.pyc,,
xformers/_flash_attn/models/baichuan.py,sha256=qAyur0jJBkdzNlNwFLgHgDpZwgBVngDXcmVFDTV2gVA,5881
xformers/_flash_attn/models/bert.py,sha256=BCImImz4Zeg6bCH60LLoWlHig5Ha5SDYAOqd3HYHEbo,33996
xformers/_flash_attn/models/bigcode.py,sha256=D16KsDAurcLs6Spw2WUpzY6L03r-dv9735tTT-Y98js,9616
xformers/_flash_attn/models/btlm.py,sha256=ojSk0O5mezLfbUJC5b-es3ol__rEdpNUM2Hlu63tEfc,4733
xformers/_flash_attn/models/falcon.py,sha256=Z8eFr6U7BaAOX0cElGRX9W-nZdgRVRI9NtSf5A3kT6Q,6176
xformers/_flash_attn/models/gpt.py,sha256=3up3lDM0LrUtEZUpyou6klZrvLOWabTXgq7pgAVpcMM,48749
xformers/_flash_attn/models/gpt_neox.py,sha256=JnfIppqL6neut6OrcOwZy6QueRsD34T9d5afxoP0isM,5283
xformers/_flash_attn/models/gptj.py,sha256=mrJcKwuYQk6mGsFsV2-HA3Db79KP0LCbyIb3vnS9jbc,4545
xformers/_flash_attn/models/llama.py,sha256=Gu-fr9ltOFxKgUTCDHpgwNpTpFeA_d6MGzYT3jyG394,17003
xformers/_flash_attn/models/opt.py,sha256=w6LSHfxDBPC1d_CIyNUDAU1KifeynDh4WuipbYkUhDA,5280
xformers/_flash_attn/models/vit.py,sha256=L9AE7hiJo_HVHfMtNEK2JbkTnwQyhDy3PCih4NSJQio,14447
xformers/_flash_attn/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/modules/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/modules/__pycache__/block.cpython-311.pyc,,
xformers/_flash_attn/modules/__pycache__/embedding.cpython-311.pyc,,
xformers/_flash_attn/modules/__pycache__/mha.cpython-311.pyc,,
xformers/_flash_attn/modules/__pycache__/mlp.cpython-311.pyc,,
xformers/_flash_attn/modules/block.py,sha256=91FIlNAF8rBQlZOHWzlUwGsD-7jzQUl70ukwhmxukoY,17746
xformers/_flash_attn/modules/embedding.py,sha256=3U2vTsd7aQXfD2wiDLGvEKhfVyw3kC7dtfxbnh1P6BY,8909
xformers/_flash_attn/modules/mha.py,sha256=lnpmCuK_tIUyDVsPCv9ajhqUWtGiCdbJtCzXLMG3fY0,43101
xformers/_flash_attn/modules/mlp.py,sha256=ThtP6EiTA78xc1paV1OkHi4_c-em6VttFECxghi1d9Y,6224
xformers/_flash_attn/ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/ops/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/ops/__pycache__/activations.cpython-311.pyc,,
xformers/_flash_attn/ops/__pycache__/fused_dense.cpython-311.pyc,,
xformers/_flash_attn/ops/__pycache__/layer_norm.cpython-311.pyc,,
xformers/_flash_attn/ops/__pycache__/rms_norm.cpython-311.pyc,,
xformers/_flash_attn/ops/activations.py,sha256=jNBYjUjasYQCnG7oYea4xSfsrVdsevpVlWl_FoI6cFg,4074
xformers/_flash_attn/ops/fused_dense.py,sha256=YQWDXtBZ1kueYUEmpXqw6OE2M93WMH9-JHWdXgS3K8s,28603
xformers/_flash_attn/ops/layer_norm.py,sha256=re3-HG7fv-qeZtehElszHG5sQKgH17GmSvX1bJmsPcw,23243
xformers/_flash_attn/ops/rms_norm.py,sha256=0YbzNABBn31R_7asugdJCFUzXZjvohk1XYkkPKeZ0_U,4162
xformers/_flash_attn/ops/triton/__init__.py,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
xformers/_flash_attn/ops/triton/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/cross_entropy.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/k_activations.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/layer_norm.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/linear.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/mlp.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/__pycache__/rotary.cpython-311.pyc,,
xformers/_flash_attn/ops/triton/cross_entropy.py,sha256=sDZ0-UuSpNl1eCh65D3CXu2IdXYGQN2UqXzkNqBd9Ho,13208
xformers/_flash_attn/ops/triton/k_activations.py,sha256=EXip4m6AwLI4f3Go1b3WRLg32RepR70SU_uBZCl_4co,4196
xformers/_flash_attn/ops/triton/layer_norm.py,sha256=aj3XlV5onu9juw5BrFeltnnaKMBalo5paD9wqN3ZGEI,43850
xformers/_flash_attn/ops/triton/linear.py,sha256=hyB5-xYqH0cKtPGq26bqzCZJxAPAkN6M8QorwcVFzKs,21435
xformers/_flash_attn/ops/triton/mlp.py,sha256=kiPcO3XdMn70OZstowKIOcAXUhgUA6xl10LBU8IVHg4,6225
xformers/_flash_attn/ops/triton/rotary.py,sha256=S-HpOeVcMH6Oqxv0OW4ERJ8UNcMvH7LhQpBJhQzriaI,7273
xformers/_flash_attn/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
xformers/_flash_attn/utils/__pycache__/__init__.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/benchmark.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/distributed.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/generation.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/library.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/pretrained.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/testing.cpython-311.pyc,,
xformers/_flash_attn/utils/__pycache__/torch.cpython-311.pyc,,
xformers/_flash_attn/utils/benchmark.py,sha256=jU-SDghUtkLVtDNnetTAOWFnK1tsi5um1ua3rPoQBtk,7637
xformers/_flash_attn/utils/distributed.py,sha256=1KKwHrmoAjlGA2OD3O5ntO5LmoT34Xq3X9xkxX8X1Wg,5969
xformers/_flash_attn/utils/generation.py,sha256=LEg7sbmdeqjazwbi2dfEay9_VpMSF5TRBAzvKYSX1wM,31434
xformers/_flash_attn/utils/library.py,sha256=A-CJwjzWe_lLzinbsqKhw4jM1s1gY2lScLM1hFepGRM,2745
xformers/_flash_attn/utils/pretrained.py,sha256=z3aA3mwarpBSRVpXZX8y8bxNFqwzGeON0k14TvZDYIU,3325
xformers/_flash_attn/utils/testing.py,sha256=atu_jsAXEkjcKDQG-8XLHuARkphPYbZieFi1onZ-VfU,14144
xformers/_flash_attn/utils/torch.py,sha256=DVMv9UPqe6uTaILOeIFfRHbW4SYPqNxDlW5Is_MlNSw,668
xformers/attn_bias_utils.py,sha256=UVXnMpKjTm6Kaz0b92DC5AgacPYylI2rD5NO1rAju_4,20367
xformers/benchmarks/LRA/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/benchmarks/LRA/__pycache__/__init__.cpython-311.pyc,,
xformers/benchmarks/LRA/__pycache__/batch_fetch_results.cpython-311.pyc,,
xformers/benchmarks/LRA/__pycache__/batch_submit.cpython-311.pyc,,
xformers/benchmarks/LRA/__pycache__/run_grid_search.cpython-311.pyc,,
xformers/benchmarks/LRA/__pycache__/run_tasks.cpython-311.pyc,,
xformers/benchmarks/LRA/__pycache__/run_with_submitit.cpython-311.pyc,,
xformers/benchmarks/LRA/batch_fetch_results.py,sha256=P8_GIzJEONssaMGTnOhT-sYYga20lZxiIW5Bvx8ufB4,3604
xformers/benchmarks/LRA/batch_submit.py,sha256=spwxdzpH43ixNJSma-ZauJXXj1AHf5nrkqnDwgQaYa8,1734
xformers/benchmarks/LRA/code/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/benchmarks/LRA/code/__pycache__/__init__.cpython-311.pyc,,
xformers/benchmarks/LRA/code/__pycache__/dataset.cpython-311.pyc,,
xformers/benchmarks/LRA/code/__pycache__/model_wrapper.cpython-311.pyc,,
xformers/benchmarks/LRA/code/dataset.py,sha256=GxlIXt0cdpZJ-mA1pAnaOuSBoEpu4sLi3C6lxoK7FCQ,1449
xformers/benchmarks/LRA/code/model_wrapper.py,sha256=vFfNfBh1eyJvVs2GIr7n48KImatW3t0BSbHDrzGLICs,10065
xformers/benchmarks/LRA/run_grid_search.py,sha256=EmrmKas6pI03S0TH4yB3RlOfFf_3amjiFxZV5PrIRY4,5475
xformers/benchmarks/LRA/run_tasks.py,sha256=ASxseY6sKnvDMlaCVh35XKq4wdQkBnd7ZXwEskHXdVQ,9596
xformers/benchmarks/LRA/run_with_submitit.py,sha256=M8YM_dfcC5roD6X0KtWvin0WRAA_VKJOn95FweoTGr8,4765
xformers/benchmarks/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/benchmarks/__pycache__/__init__.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_attn_decoding.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_core.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_indexing.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_mem_eff_attention.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_merge_attentions.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_nystrom_utils.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_revnet.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_sddmm.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_sequence_parallel_fused.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_sp24.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_swiglu.cpython-311.pyc,,
xformers/benchmarks/__pycache__/benchmark_tiled_matmul.cpython-311.pyc,,
xformers/benchmarks/__pycache__/utils.cpython-311.pyc,,
xformers/benchmarks/benchmark_attn_decoding.py,sha256=QURUNVIVWU3zmgr9zfMAgvsbj4NuZczmQM2Gkj_lf6o,15037
xformers/benchmarks/benchmark_core.py,sha256=imxI2QUFzVPo86ruKeuYF-9SWsxr3LdknOob_f4JZF8,8887
xformers/benchmarks/benchmark_indexing.py,sha256=__DxpQvWHoOnaw-iY0rhJ0GQZiqyvOXXPiY7lQxZ3aI,5479
xformers/benchmarks/benchmark_mem_eff_attention.py,sha256=5TO6gI5D5Cnf5E_jG7Cplx8k8DYz1a7_VmWkvvulcfE,10474
xformers/benchmarks/benchmark_merge_attentions.py,sha256=BpNwGFYo0mTwyz03b8tCrjz5tCtqyv3QphlRqq-CQvc,3134
xformers/benchmarks/benchmark_nystrom_utils.py,sha256=G7RAbrTN84QAQM_BDuGjeCYSrKaQS3sBVdri6a4g8UQ,3331
xformers/benchmarks/benchmark_revnet.py,sha256=SKrETv347eMZdfq2lByBLvMRj_Ec7LbRD4mZ_XD7NBo,2754
xformers/benchmarks/benchmark_sddmm.py,sha256=TLQsLTGC1-YAgURnJO5K8fU3hzNYp3OmotToTjLISYU,3885
xformers/benchmarks/benchmark_sequence_parallel_fused.py,sha256=xhQiU1oPUnqWWlnugDP0tyEFD6XEfJYAQ6LRCKvr2oI,14927
xformers/benchmarks/benchmark_sp24.py,sha256=glLh6IJlr6CKgqdgiZ9HXnS-GqMdN6sp3NIRL_gpRXU,5039
xformers/benchmarks/benchmark_swiglu.py,sha256=tHuZWq3MeWq6xA0vCp7DOfT13vrl3cI7llfH3-W00co,4458
xformers/benchmarks/benchmark_tiled_matmul.py,sha256=1hNITXjF2wrcP5vgmSGZpU_d3Tt0NSS37dYGkWq_NYE,3568
xformers/benchmarks/utils.py,sha256=6_8CU9mN4KY088dvlEuUHl6pRxb6bs4kXtaFN1vvXHQ,25276
xformers/checkpoint.py,sha256=FuRwOK0XLTboQ5TKKLmM72xRGSXw0iQfT0G1AKod9U8,20883
xformers/components/__init__.py,sha256=9VrMqvBVDdQIAMPMlZsvQ9YLlTsXT4-8PmEF5D_s_WM,920
xformers/components/__pycache__/__init__.cpython-311.pyc,,
xformers/components/__pycache__/input_projection.cpython-311.pyc,,
xformers/components/__pycache__/residual.cpython-311.pyc,,
xformers/components/attention/__init__.py,sha256=vip5YmpRAi24pRDXT5LYapznezXz6B0W6Rpn7BIjqSY,3421
xformers/components/attention/__pycache__/__init__.cpython-311.pyc,,
xformers/components/attention/__pycache__/_sputnik_sparse.cpython-311.pyc,,
xformers/components/attention/__pycache__/attention_mask.cpython-311.pyc,,
xformers/components/attention/__pycache__/attention_patterns.cpython-311.pyc,,
xformers/components/attention/__pycache__/base.cpython-311.pyc,,
xformers/components/attention/__pycache__/core.cpython-311.pyc,,
xformers/components/attention/__pycache__/fourier_mix.cpython-311.pyc,,
xformers/components/attention/__pycache__/scaled_dot_product.cpython-311.pyc,,
xformers/components/attention/__pycache__/sparsity_config.cpython-311.pyc,,
xformers/components/attention/__pycache__/utils.cpython-311.pyc,,
xformers/components/attention/_sputnik_sparse.py,sha256=LOjNGsNeagDL6_VCOafkJLrK4exsefTdrIEejm3RYds,3285
xformers/components/attention/attention_mask.py,sha256=yidod0KphKvup4oP3Y-oKQNnWfUddwDZDV5KlzAgzJI,4728
xformers/components/attention/attention_patterns.py,sha256=2pliuyA3AfM8t2QX4YPoUopP5ryUkPfRxvHo-IT5biY,10240
xformers/components/attention/base.py,sha256=ElsvRD9XftwEynk5WJfUtcS2EHBP2R9rDp9rryARkGY,3400
xformers/components/attention/core.py,sha256=HiVeT8bzTlECl2aB-KCwnnv7l7f7nXmsath5Cs3pM44,7889
xformers/components/attention/fourier_mix.py,sha256=5PLunlyAOKtfMv5Q9fOBxMKzO4d_1N0XJUkv5OgIWKI,1219
xformers/components/attention/scaled_dot_product.py,sha256=HnhYiNOghpBWucf2tW3aYVtFlbPIdUz8izCVwotsZFI,4638
xformers/components/attention/sparsity_config.py,sha256=5I77G7bv9pIY2x9qFvcHocZGmnXcN0vdx4C3XgYz8s0,42420
xformers/components/attention/utils.py,sha256=rhtgdGUKRBHBesndiJWAttw1tpaNFxf0qbGdfcWQj4U,3911
xformers/components/input_projection.py,sha256=0E8N4WePLjrOt6L6zCbthll-JtlnOCduhW_oHZeoAK4,3223
xformers/components/residual.py,sha256=-20aoEzubyQe2_DC2SFflIK9c9QXsFwmjaZbCo1WTKA,5885
xformers/cpp_lib.json,sha256=Lzb3f5OwVhumOgjWzZNASgUREqCbsTFuVo6n6dA-WYk,395
xformers/flash_attn_3/_C.pyd,sha256=rmEUoYpCEalK495RV46SR_-a1m6MJhCvCNPf4ZjWNBE,311288832
xformers/flash_attn_3/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/flash_attn_3/__pycache__/__init__.cpython-311.pyc,,
xformers/info.py,sha256=AE-_vCiZ_nzOzRGMnKLJH1S5IJ_VwF-WPHoTx73AKTI,2749
xformers/ops/__init__.py,sha256=kjqcCLCO1_9e1byqUzicrN6Qpe0u9RG9-rDH07FnvHk,3578
xformers/ops/__pycache__/__init__.cpython-311.pyc,,
xformers/ops/__pycache__/common.cpython-311.pyc,,
xformers/ops/__pycache__/differentiable_collectives.cpython-311.pyc,,
xformers/ops/__pycache__/indexing.cpython-311.pyc,,
xformers/ops/__pycache__/modpar_layers.cpython-311.pyc,,
xformers/ops/__pycache__/rmsnorm.cpython-311.pyc,,
xformers/ops/__pycache__/rope_padded.cpython-311.pyc,,
xformers/ops/__pycache__/seqpar.cpython-311.pyc,,
xformers/ops/__pycache__/sequence_parallel_fused_ops.cpython-311.pyc,,
xformers/ops/__pycache__/sp24.cpython-311.pyc,,
xformers/ops/__pycache__/swiglu_op.cpython-311.pyc,,
xformers/ops/__pycache__/tiled_matmul.cpython-311.pyc,,
xformers/ops/__pycache__/tree_attention.cpython-311.pyc,,
xformers/ops/__pycache__/unbind.cpython-311.pyc,,
xformers/ops/_triton/__init__.py,sha256=33DygBWecHzeWixrEb8j3IDBuTFVaIG2Ajbmp-VH85o,760
xformers/ops/_triton/__pycache__/__init__.cpython-311.pyc,,
xformers/ops/_triton/__pycache__/k_index_select_cat.cpython-311.pyc,,
xformers/ops/_triton/__pycache__/k_scaled_index_add.cpython-311.pyc,,
xformers/ops/_triton/__pycache__/matmul_perf_model.cpython-311.pyc,,
xformers/ops/_triton/__pycache__/rmsnorm_kernels.cpython-311.pyc,,
xformers/ops/_triton/__pycache__/rope_padded_kernels.cpython-311.pyc,,
xformers/ops/_triton/__pycache__/tiled_matmul_kernels.cpython-311.pyc,,
xformers/ops/_triton/k_index_select_cat.py,sha256=_XVj_ytE-qcG7xUBAXfacJWFG9H9hQ609ar6TTXEo2Y,6378
xformers/ops/_triton/k_scaled_index_add.py,sha256=-J3qUjQsWL3G4d8h5D-Xul9ljyOfBMvEtCzw12ceq0Y,13167
xformers/ops/_triton/matmul_perf_model.py,sha256=FL02xJpfCfvmVb_F7DngUznwwBZHmwULoOZKQc8pSks,8635
xformers/ops/_triton/rmsnorm_kernels.py,sha256=1cWmiQpJ1j5DMn2iAcnH1PxBGYnTZUqUcNofWvEQ0dI,5283
xformers/ops/_triton/rope_padded_kernels.py,sha256=hS8K0qiTnF9o9ESuh3SZiTSi52Mb2BQ9VRlHdVoPnbk,7443
xformers/ops/_triton/tiled_matmul_kernels.py,sha256=Ze0lUty2-gBaFAJmCGCEk5VnhxCFhMGHHTBZR--e6oM,14300
xformers/ops/common.py,sha256=qF9wAu-whR9_0EXHjUkstWsXYQZbdWyiXz-_Ax2hOb0,1975
xformers/ops/differentiable_collectives.py,sha256=YKyCGxTRAIAAYzgi8pQ5FugH1XoytQ1r33YGtP42hUI,5530
xformers/ops/fmha/__init__.py,sha256=r9_IGselafoF_64TEl4g5lGmAIduyaAN0KabPIBI1as,30472
xformers/ops/fmha/__pycache__/__init__.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/attn_bias.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/ck.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/ck_decoder.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/ck_splitk.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/common.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/cutlass.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/dispatch.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/flash.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/flash3.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/torch_attention_compat.cpython-311.pyc,,
xformers/ops/fmha/__pycache__/triton_splitk.cpython-311.pyc,,
xformers/ops/fmha/_triton/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/ops/fmha/_triton/__pycache__/__init__.cpython-311.pyc,,
xformers/ops/fmha/_triton/__pycache__/splitk_kernels.cpython-311.pyc,,
xformers/ops/fmha/_triton/splitk_kernels.py,sha256=2fMAUN1vm0TJPLBY_sz_QzbN_rK_rJlDfK4F3OD0sAU,47177
xformers/ops/fmha/attn_bias.py,sha256=fOSLWhQHi6I563sEEAOQKfJlWrJBsiCiJFIYB92zJvA,75312
xformers/ops/fmha/ck.py,sha256=Iw-4GFn8NPS5fsQkjFz9LhRPVk35Dwsr4tE3wdFmh9E,18276
xformers/ops/fmha/ck_decoder.py,sha256=WjrGt2zsVcBe8ejYZ-lfzm3af5qEFlbJ_Uw-F0km9kc,5231
xformers/ops/fmha/ck_splitk.py,sha256=50plIcKhtWIfxN7znsc1ZOowoblZWQSWC8sntjmBQkw,6619
xformers/ops/fmha/common.py,sha256=kJMH749AmjAqplkCOsld56XLBDjuBHjKyevy4iWyNf4,22489
xformers/ops/fmha/cutlass.py,sha256=l-E9EF3jF2FpfPNkmosbAD2oLeGfqII69JSXc-Hv2t8,17610
xformers/ops/fmha/dispatch.py,sha256=3W4prwhonEe_Ud556N14Wh85a_6B9I6HNwZTUGgm80M,6641
xformers/ops/fmha/flash.py,sha256=_TXEq4-jqrUmM_zlFLYcqCevSUDdtga1qdOnATcNt98,30195
xformers/ops/fmha/flash3.py,sha256=_gKNy-4CVFu-gXOlW9yNdB_ZuBPkNgLXF5xXnBG9Q90,33293
xformers/ops/fmha/torch_attention_compat.py,sha256=brlDurqyRWMBDO7g_f1JvIipENUyD6XbqRiifDLPJfE,7096
xformers/ops/fmha/triton_splitk.py,sha256=cyw4-zDqUucchp8TdU2QQxbB3sYiCYxtHEzDOq-P5JQ,48024
xformers/ops/indexing.py,sha256=PNItxgTsir-KlMyKSWtgEoGwOa7VDLx5JlkJXGKvfj8,7164
xformers/ops/modpar_layers.py,sha256=vythQeMelCL48x7rCf7aHPDF5Z1J5OY3YEX0GRXAakw,5863
xformers/ops/rmsnorm.py,sha256=5t_BN6OXyz7FpnRQn_8UvhpA8f5QjCnyyelsY5s2Crg,3471
xformers/ops/rope_padded.py,sha256=QRqxXfQo-tWBNfkPbCgyKUlK5fg3_sLQVlNAWhgZTG0,12400
xformers/ops/seqpar.py,sha256=wLFUWq_jKJQi9V5Q3h1-4DLF6AODTUdGEvAcst5cAEA,12472
xformers/ops/sequence_parallel_fused_ops.py,sha256=_eKcxIHxaFaFyJjydWp_a77iDwFgNAlBYphyjsPOwQ0,34391
xformers/ops/sp24.py,sha256=wH7dWcjbF7p62ahjBou-2XZfLFH3smmYpdm2HxE74iU,28099
xformers/ops/swiglu_op.py,sha256=asmnEB0YJTaYbDD2yDnNR4cM_qman3C1zI9y6VO0sVc,17921
xformers/ops/tiled_matmul.py,sha256=mPUbxqd4b2i0OH-K6YITfyjsw2mx1mcKIcFYJTk-mCM,13110
xformers/ops/tree_attention.py,sha256=2Swdt19Nyc6wTpNMtk_CwLvRNNg1tQbZFwUJF8knhwQ,29147
xformers/ops/unbind.py,sha256=r41YS3wMzwbUWCPW-Bb12WtyydpVTh5NIUd_XATnQCc,4054
xformers/profiler/__init__.py,sha256=xvGnOERGU06eBFWqGHijjBwQLK9AIfkciZBPYSWT2B0,436
xformers/profiler/__pycache__/__init__.cpython-311.pyc,,
xformers/profiler/__pycache__/api.cpython-311.pyc,,
xformers/profiler/__pycache__/device_limits.cpython-311.pyc,,
xformers/profiler/__pycache__/find_slowest.cpython-311.pyc,,
xformers/profiler/__pycache__/profile_analyzer.cpython-311.pyc,,
xformers/profiler/__pycache__/profiler.cpython-311.pyc,,
xformers/profiler/__pycache__/profiler_dcgm.cpython-311.pyc,,
xformers/profiler/__pycache__/profiler_dcgm_impl.cpython-311.pyc,,
xformers/profiler/api.py,sha256=_uNdQBQBT8yjvrY_2lVfpN7UcFp8LPd8k_aICZdGY4c,2777
xformers/profiler/device_limits.py,sha256=CBiqpeiJVDPbSfHh9HF2LCM_uws0CzhNJ-eQHZEIzvE,3701
xformers/profiler/find_slowest.py,sha256=TxU4uMkH7yScTZHhoWTlq6QZUfhdgLTjZbt-RQ8mFPc,5244
xformers/profiler/profile_analyzer.py,sha256=3NWaIniweiAth9TX-NOhxVWWgotNlN_Embl1XJDSMgc,8954
xformers/profiler/profiler.py,sha256=F7ym4j9Qo0y8aeL4bRcqf2yA2mtfVDGyllnj0rc4t7g,13600
xformers/profiler/profiler_dcgm.py,sha256=X0ClInhmUcjdbW7k9iPUkdQvGR9nfvbffmdgPIR_d3g,1198
xformers/profiler/profiler_dcgm_impl.py,sha256=Npza5x9ts_vMx4Fi-DpEcL2plcDFEQHEMPjqttzrtY8,8688
xformers/sparse/__init__.py,sha256=dgI-6hEZfhbzKszzAeGSux-uumEG2PnKrePQC0uJcks,324
xformers/sparse/__pycache__/__init__.cpython-311.pyc,,
xformers/sparse/__pycache__/_csr_ops.cpython-311.pyc,,
xformers/sparse/__pycache__/blocksparse_tensor.cpython-311.pyc,,
xformers/sparse/__pycache__/csr_tensor.cpython-311.pyc,,
xformers/sparse/__pycache__/utils.cpython-311.pyc,,
xformers/sparse/_csr_ops.py,sha256=cJyyY_pCxEnpUwfvhyJWKesEU2azf15gh-nJeTheSLU,5039
xformers/sparse/blocksparse_tensor.py,sha256=Zauyo5YfyrNAlHKDjHeSidopLL4nj61s-ix0EjkCbFM,9172
xformers/sparse/csr_tensor.py,sha256=BN2Ii8ospSjPKzpUPX8I8xaN4NMX1U0IOsHpG88Cgas,14628
xformers/sparse/utils.py,sha256=b8XgQWh-exTkiNkNrwb5ncI1GKKbTtkMqKlCCJxCOxw,4397
xformers/test.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/triton/__init__.py,sha256=M4NStf3p8zmHeaRF4kDPx3J4tJv4zraCm5oYv6rp7Lc,202
xformers/triton/__pycache__/__init__.cpython-311.pyc,,
xformers/triton/__pycache__/importing.cpython-311.pyc,,
xformers/triton/__pycache__/vararg_kernel.cpython-311.pyc,,
xformers/triton/importing.py,sha256=DLDMor33OyqK0mgo_KGa5tBREHxBjoqMGVEGlLA86Kc,939
xformers/triton/vararg_kernel.py,sha256=imQCeMEsfsrriUKAhEA-v1oiRaxr0EXfB8VCWJF1n-M,9671
xformers/utils.py,sha256=Z0r6Jw8ok9enhvo6Gg687UXzr4NeFohmRu29QsUduwY,5209
xformers/version.py,sha256=B4Xg_aCW28oKRxeV4suaCKEfResLPF1pAN3L2jPZpn0,44
