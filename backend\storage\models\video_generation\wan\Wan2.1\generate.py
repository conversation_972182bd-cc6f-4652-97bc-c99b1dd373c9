# Copyright 2024-2025 The Alibaba Wan Team Authors. All rights reserved.
import tempfile
import argparse
from datetime import datetime
import logging
import os
import sys
import warnings
import time
# import tempfile 已在文件开头导入

warnings.filterwarnings('ignore')

import torch, random
import torch.distributed as dist
from PIL import Image
import cv2
import numpy as np
from tqdm import tqdm

import wan
from wan.configs import WAN_CONFIGS, SIZE_CONFIGS, MAX_AREA_CONFIGS, SUPPORTED_SIZES
from wan.utils.prompt_extend import DashScopePromptExpander, QwenPromptExpander
from wan.utils.utils import cache_video, cache_image, str2bool

EXAMPLE_PROMPT = {
    "t2v-1.3B": {
        "prompt": "Two anthropomorphic cats in comfy boxing gear and bright gloves fight intensely on a spotlighted stage.",
    },
    "t2v-14B": {
        "prompt": "Two anthropomorphic cats in comfy boxing gear and bright gloves fight intensely on a spotlighted stage.",
    },
    "t2i-14B": {
        "prompt": "一个朴素端庄的美人",
    },
    "i2v-14B": {
        "prompt":
            "Summer beach vacation style, a white cat wearing sunglasses sits on a surfboard. The fluffy-furred feline gazes directly at the camera with a relaxed expression. Blurred beach scenery forms the background featuring crystal-clear waters, distant green hills, and a blue sky dotted with white clouds. The cat assumes a naturally relaxed posture, as if savoring the sea breeze and warm sunlight. A close-up shot highlights the feline's intricate details and the refreshing atmosphere of the seaside.",
        "image":
            "examples/i2v_input.JPG",
    },
    "flf2v-14B": {
            "prompt":
                "CG动画风格，一只蓝色的小鸟从地面起飞，煽动翅膀。小鸟羽毛细腻，胸前有独特的花纹，背景是蓝天白云，阳光明媚。镜跟随小鸟向上移动，展现出小鸟飞翔的姿态和天空的广阔。近景，仰视视角。",
            "first_frame":
                "examples/flf2v_input_first_frame.png",
            "last_frame":
                "examples/flf2v_input_last_frame.png",
    },
}

# 初始化日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('WAN-Generate')

def _validate_args(args):
    # Basic check
    assert args.ckpt_dir is not None, "Please specify the checkpoint directory."
    assert args.task in WAN_CONFIGS, f"Unsupport task: {args.task}"
    assert args.task in EXAMPLE_PROMPT, f"Unsupport task: {args.task}"

    # The default sampling steps are 40 for image-to-video tasks and 50 for text-to-video tasks.
    if args.sample_steps is None:
        args.sample_steps = 40 if "i2v" in args.task else 50

    if args.sample_shift is None:
        args.sample_shift = 5.0
        if "i2v" in args.task and args.size in ["832*480", "480*832"]:
            args.sample_shift = 3.0
        if "flf2v" in args.task:
            args.sample_shift = 16

    # 确保视频至少有32帧，防止黑屏问题
    # The default number of frames are 1 for text-to-image tasks and 81 for other tasks.
    if args.frame_num is None:
        args.frame_num = 1 if "t2i" in args.task else 81
    else:
        # Enhanced frame count validation
        if "t2i" not in args.task and args.frame_num < 32:
            logging.warning(f"帧数过少 ({args.frame_num})，自动增加到32帧以避免黑屏问题")
            args.frame_num = 32

        # Ensure frame count is suitable for diffusion models (4n+1)
        if args.frame_num % 4 != 1 and "t2i" not in args.task:
            old_frame_num = args.frame_num
            args.frame_num = ((args.frame_num - 1) // 4) * 4 + 1
            if args.frame_num < 32:
                args.frame_num = 33  # Smallest valid value >= 32
            logging.info(f"调整帧数从 {old_frame_num} 到 {args.frame_num} (4n+1格式)")

    if "t2i" in args.task:
        assert args.frame_num == 1, f"Unsupport frame_num {args.frame_num} for task {args.task}"

    args.base_seed = args.base_seed if args.base_seed >= 0 else random.randint(
        0, sys.maxsize)
    # Size check
    assert args.size in SUPPORTED_SIZES[
        args.
        task], f"Unsupport size {args.size} for task {args.task}, supported sizes are: {', '.join(SUPPORTED_SIZES[args.task])}"


def parse_args():
    parser = argparse.ArgumentParser(description="WAN-Generate: 高级视频生成工具")
    
    # 基本参数
    parser.add_argument("--text", "-t", type=str, default="你好", help="描述视频内容的文本")
    parser.add_argument("--height", "-H", type=int, default=576, help="视频高度")
    parser.add_argument("--width", "-w", type=int, default=1024, help="视频宽度")
    parser.add_argument("--length", "-l", type=int, default=16, help="视频帧数")
    parser.add_argument("--output", "-o", type=str, default="output.mp4", help="输出文件路径")
    parser.add_argument("--seed", "-s", type=int, default=None, help="随机种子")
    
    # 新增参数
    parser.add_argument("--fps", type=int, default=8, help="视频帧率，默认8fps")
    parser.add_argument("--quality", type=int, default=28, choices=range(1, 52), help="视频质量(1-51)，数值越低质量越高，默认28")
    parser.add_argument("--enhance", action="store_true", help="启用视频增强处理")
    parser.add_argument("--chinese", action="store_true", help="使用中文提示词")
    parser.add_argument("--device", type=str, default="auto",
                        help="计算设备: auto/cuda/cpu，auto会自动检测最佳设备")
    parser.add_argument("--gpu_memory_limit", type=float, default=None,
                        help="GPU内存限制(GB)，超过此值将启用内存优化")
    parser.add_argument("--enable_memory_optimization", action="store_true",
                        help="启用内存优化模式")
    parser.add_argument("--fallback_to_cpu", action="store_true",
                        help="GPU失败时自动回退到CPU")
    
    # 高级参数
    advanced = parser.add_argument_group("高级选项")
    advanced.add_argument("--model", type=str, default="local", choices=["local", "remote"],
                        help="模型类型: local或remote")
    advanced.add_argument("--version", type=str, default="2.1", help="模型版本")
    advanced.add_argument("--cfg", type=float, default=8.0, help="提示词引导系数")
    advanced.add_argument("--steps", type=int, default=50, help="采样步数")
    
    args = parser.parse_args()
    
    # 参数验证和调整
    if args.height % 64 != 0 or args.width % 64 != 0:
        # 调整为最接近的64的倍数
        new_height = round(args.height / 64) * 64
        new_width = round(args.width / 64) * 64
        logger.warning(f"高度和宽度必须是64的倍数。调整为: {new_height}x{new_width}")
        args.height = new_height
        args.width = new_width
    
    if args.seed is None:
        import random
        args.seed = random.randint(0, 2**32 - 1)
        logger.info(f"随机种子设为: {args.seed}")
    
    return args


def _init_logging(rank):
    # logging
    if rank == 0:
        # set format
        logging.basicConfig(
            level=logging.INFO,
            format="[%(asctime)s] %(levelname)s: %(message)s",
            handlers=[logging.StreamHandler(stream=sys.stdout)])
    else:
        logging.basicConfig(level=logging.ERROR)

def _detect_optimal_device(args):
    """检测最佳计算设备并应用优化"""
    if args.device == "auto":
        if torch.cuda.is_available():
            # 检查GPU内存
            gpu_memory_gb = torch.cuda.get_device_properties(0).total_memory / 1024**3
            gpu_free_memory = (torch.cuda.get_device_properties(0).total_memory - torch.cuda.memory_allocated(0)) / 1024**3

            logging.info(f"🔍 检测到GPU: {torch.cuda.get_device_name(0)}")
            logging.info(f"📊 GPU内存: {gpu_free_memory:.1f}GB 可用 / {gpu_memory_gb:.1f}GB 总计")

            # 根据GPU内存自动调整参数
            if gpu_memory_gb < 6:
                logging.warning("⚠️  GPU内存不足6GB，启用内存优化模式")
                args.enable_memory_optimization = True
                args.offload_model = True
                args.t5_cpu = True
                # 限制帧数和分辨率
                if args.frame_num > 33:
                    args.frame_num = 33
                    logging.info(f"🔧 限制帧数到 {args.frame_num} 以节省内存")
                if args.size not in ["512x512", "768x512", "512x768"]:
                    args.size = "512x512"
                    logging.info(f"🔧 调整分辨率到 {args.size} 以节省内存")
            elif gpu_memory_gb < 12:
                logging.info("📊 中等GPU内存，启用部分优化")
                args.offload_model = True
                args.t5_cpu = True
                if args.frame_num > 65:
                    args.frame_num = 65
                    logging.info(f"🔧 限制帧数到 {args.frame_num}")
            else:
                logging.info("🚀 充足GPU内存，使用高性能配置")
                args.offload_model = False
                args.t5_cpu = False

            args.device = "cuda"
            return "cuda"
        else:
            logging.warning("⚠️  GPU不可用，使用CPU模式")
            args.device = "cpu"
            args.offload_model = True
            args.t5_cpu = True
            # CPU模式下的限制
            if args.frame_num > 33:
                args.frame_num = 33
                logging.info(f"🔧 CPU模式限制帧数到 {args.frame_num}")
            if args.sample_steps > 25:
                args.sample_steps = 25
                logging.info(f"🔧 CPU模式限制推理步数到 {args.sample_steps}")
            args.size = "512x512"
            return "cpu"
    else:
        return args.device

def _apply_memory_optimizations(args):
    """应用内存优化设置"""
    if args.enable_memory_optimization or args.gpu_memory_limit:
        logging.info("🔧 应用内存优化设置")

        # 启用各种内存优化
        args.offload_model = True
        args.t5_cpu = True

        # 如果指定了内存限制
        if args.gpu_memory_limit and torch.cuda.is_available():
            available_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
            if available_memory > args.gpu_memory_limit:
                logging.info(f"🔧 GPU内存超过限制 ({args.gpu_memory_limit}GB)，启用更激进的优化")
                # 更激进的优化
                if args.frame_num > 33:
                    args.frame_num = 33
                if args.sample_steps > 30:
                    args.sample_steps = 30


def generate(args, use_random_noise=False):
    rank = int(os.getenv("RANK", 0))
    world_size = int(os.getenv("WORLD_SIZE", 1))
    local_rank = int(os.getenv("LOCAL_RANK", 0))

    _init_logging(rank)

    # 应用设备检测和优化
    optimal_device = _detect_optimal_device(args)
    _apply_memory_optimizations(args)

    device = local_rank if optimal_device == "cuda" else "cpu"

    if args.offload_model is None:
        args.offload_model = False if world_size > 1 else True
        logging.info(
            f"offload_model is not specified, set to {args.offload_model}.")
    if world_size > 1:
        torch.cuda.set_device(local_rank)
        dist.init_process_group(
            backend="nccl",
            init_method="env://",
            rank=rank,
            world_size=world_size)
    else:
        assert not (
            args.t5_fsdp or args.dit_fsdp
        ), f"t5_fsdp and dit_fsdp are not supported in non-distributed environments."
        assert not (
            args.ulysses_size > 1 or args.ring_size > 1
        ), f"context parallel are not supported in non-distributed environments."

    if args.ulysses_size > 1 or args.ring_size > 1:
        assert args.ulysses_size * args.ring_size == world_size, f"The number of ulysses_size and ring_size should be equal to the world size."
        from xfuser.core.distributed import (initialize_model_parallel,
                                             init_distributed_environment)
        init_distributed_environment(
            rank=dist.get_rank(), world_size=dist.get_world_size())

        initialize_model_parallel(
            sequence_parallel_degree=dist.get_world_size(),
            ring_degree=args.ring_size,
            ulysses_degree=args.ulysses_size,
        )

    if args.use_prompt_extend:
        if args.prompt_extend_method == "dashscope":
            prompt_expander = DashScopePromptExpander(
                model_name=args.prompt_extend_model, is_vl="i2v" in args.task or "flf2v" in args.task)
        elif args.prompt_extend_method == "local_qwen":
            prompt_expander = QwenPromptExpander(
                model_name=args.prompt_extend_model,
                is_vl="i2v" in args.task,
                device=rank)
        else:
            raise NotImplementedError(
                f"Unsupport prompt_extend_method: {args.prompt_extend_method}")

    cfg = WAN_CONFIGS[args.task]
    if args.ulysses_size > 1:
        assert cfg.num_heads % args.ulysses_size == 0, f"`{cfg.num_heads=}` cannot be divided evenly by `{args.ulysses_size=}`."

    logging.info(f"Generation job args: {args}")
    logging.info(f"Generation model config: {cfg}")

    if dist.is_initialized():
        base_seed = [args.base_seed] if rank == 0 else [None]
        dist.broadcast_object_list(base_seed, src=0)
        args.base_seed = base_seed[0]

    if "t2v" in args.task or "t2i" in args.task:
        if args.prompt is None:
            args.prompt = EXAMPLE_PROMPT[args.task]["prompt"]
        logging.info(f"Input prompt: {args.prompt}")
        if args.use_prompt_extend:
            logging.info("Extending prompt ...")
            if rank == 0:
                prompt_output = prompt_expander(
                    args.prompt,
                    tar_lang=args.prompt_extend_target_lang,
                    seed=args.base_seed)
                if prompt_output.status == False:
                    logging.info(
                        f"Extending prompt failed: {prompt_output.message}")
                    logging.info("Falling back to original prompt.")
                    input_prompt = args.prompt
                else:
                    input_prompt = prompt_output.prompt
                input_prompt = [input_prompt]
            else:
                input_prompt = [None]
            if dist.is_initialized():
                dist.broadcast_object_list(input_prompt, src=0)
            args.prompt = input_prompt[0]
            logging.info(f"Extended prompt: {args.prompt}")

        logging.info("Creating WanT2V pipeline.")

        # 尝试创建GPU模型，失败时回退到CPU
        try:
            wan_t2v = wan.WanT2V(
                config=cfg,
                checkpoint_dir=args.ckpt_dir,
                device_id=device,
                rank=rank,
                t5_fsdp=args.t5_fsdp,
                dit_fsdp=args.dit_fsdp,
                use_usp=(args.ulysses_size > 1 or args.ring_size > 1),
                t5_cpu=args.t5_cpu,
            )
            logging.info("✅ WanT2V pipeline创建成功")
        except Exception as e:
            if args.fallback_to_cpu and args.device != "cpu":
                logging.warning(f"⚠️  GPU模型创建失败: {e}")
                logging.info("🔄 回退到CPU模式...")

                # 修改参数为CPU模式
                args.device = "cpu"
                args.offload_model = True
                args.t5_cpu = True
                device = "cpu"

                # 重新创建CPU模型
                wan_t2v = wan.WanT2V(
                    config=cfg,
                    checkpoint_dir=args.ckpt_dir,
                    device_id=device,
                    rank=rank,
                    t5_fsdp=args.t5_fsdp,
                    dit_fsdp=args.dit_fsdp,
                    use_usp=(args.ulysses_size > 1 or args.ring_size > 1),
                    t5_cpu=args.t5_cpu,
                )
                logging.info("✅ CPU模式WanT2V pipeline创建成功")
            else:
                raise e

        logging.info(
            f"Generating {'image' if 't2i' in args.task else 'video'} ...")
        
        # 添加重试和回退机制
        max_retries = 3
        retry_count = 0
        video = None
        
        while retry_count < max_retries and video is None:
            try:
                # 在重试时增加随机种子
                current_seed = args.base_seed + retry_count
                
                # 尝试生成视频
                logging.info(f"尝试生成视频 (尝试 {retry_count+1}/{max_retries}), 使用种子 {current_seed}")
                
                video = wan_t2v.generate(
                    args.prompt,
                    size=SIZE_CONFIGS[args.size],
                    frame_num=args.frame_num,
                    shift=args.sample_shift,
                    sample_solver=args.sample_solver,
                    sampling_steps=args.sample_steps,
                    guide_scale=args.sample_guide_scale,
                    seed=current_seed,
                    offload_model=args.offload_model)
                
                # 验证生成的视频张量是否有效
                if video is not None:
                    # 检查视频张量是否包含全零或接近全零的值
                    if isinstance(video, torch.Tensor) and video.numel() > 0:
                        min_val = float(video.min())
                        max_val = float(video.max())
                        logging.info(f"生成视频张量范围: [{min_val:.6f}, {max_val:.6f}]")
                        
                        # 如果值范围非常小或全为零，增加随机性
                        if max_val - min_val < 1e-3:
                            logging.warning("生成的视频张量值域过小，可能会导致黑屏，添加随机噪声...")
                            # 添加随机噪声避免黑屏
                            if "t2i" not in args.task:  # 对于视频任务
                                video = video + torch.randn_like(video) * 0.1
                                # 确保值仍在合理范围
                                video = torch.clamp(video, -1.0, 1.0)
                                logging.info(f"添加噪声后范围: [{float(video.min()):.6f}, {float(video.max()):.6f}]")
                    else:
                        logging.warning("生成的视频张量无效或为空")
                        video = None  # 强制进行下一次重试
                
            except Exception as e:
                error_message = str(e)
                logging.error(f"生成时发生错误: {error_message}")
                
                # 记录更详细的错误信息
                import traceback
                logging.error(traceback.format_exc())
                
                # 对于形状不匹配或其他已知可恢复的错误，尝试下一次重试
                video = None
            
            retry_count += 1
            
            # 如果视频仍然为空且已达到最大重试次数，但用户要求强制生成
            if video is None and retry_count >= max_retries and args.force_generate:
                logging.warning("所有尝试都失败，创建应急视频...")
                
                # 创建一个随机噪声视频作为应急方案
                if "t2i" in args.task:
                    # 对于图像任务，创建随机图像
                    w, h = SIZE_CONFIGS[args.size]
                    video = torch.rand((3, h, w), device=device) * 0.5
                else:
                    # 对于视频任务，创建随机视频
                    w, h = SIZE_CONFIGS[args.size]
                    video = torch.rand((3, args.frame_num, h, w), device=device) * 0.5
                
                logging.info(f"创建的应急视频张量形状: {video.shape}")
        
        return video

def main():
    args = parse_args()
    logger.info(f"使用设备: {args.device}")
    logger.info(f"生成视频: {args.text}")
    logger.info(f"输出参数: {args.height}x{args.width}, {args.length}帧, {args.fps}fps")
    
    if args.model == "local":
        from wan.utils.utils import str2bool
        
        # 修正路径设置
        sys_path = os.path.dirname(os.path.abspath(__file__))
        os.chdir(sys_path)
        
        # 基于参数调用本地模型
        from wan.inference import inference_wan
        logger.info(f"使用本地WAN {args.version}模型")
        
        result_tensor = inference_wan(
            text=args.text,
            height=args.height,
            width=args.width,
            length=args.length,
            version=args.version,
            cfg=args.cfg,
            steps=args.steps,
            use_chinese=args.chinese,
            seed=args.seed,
            enhance=args.enhance,
            device=args.device
        )
        
        # 保存视频
        from wan.utils.utils import cache_video
        cache_video(result_tensor, args.output)
        
        # 如果需要，使用FFmpeg提高视频质量
        if args.fps != 8 or args.quality != 28:
            import subprocess
            temp_output = args.output + ".temp.mp4"
            os.rename(args.output, temp_output)
            
            cmd = [
                'ffmpeg', '-y',
                '-i', temp_output,
                '-c:v', 'libx264',
                '-preset', 'slow',
                '-crf', str(args.quality),
                '-r', str(args.fps),
                '-pix_fmt', 'yuv420p',
                args.output
            ]
            
            logger.info(f"使用FFmpeg优化视频: fps={args.fps}, quality={args.quality}")
            subprocess.run(cmd)
            os.remove(temp_output)
        
        logger.info(f"视频已保存至: {args.output}")
    else:
        logger.error("暂不支持远程模型")
        return

if __name__ == "__main__":
    main()