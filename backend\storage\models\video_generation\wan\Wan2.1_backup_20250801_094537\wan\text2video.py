# Copyright 2024-2025 The Alibaba Wan Team Authors. All rights reserved.
import gc
import logging
import math
import os
import random
import sys
import types
from contextlib import contextmanager
from functools import partial

import torch
import torch.amp
import torch.cuda.amp as amp
import torch.distributed as dist
from tqdm import tqdm
import torch.nn.functional as F

from .distributed.fsdp import shard_model
from .modules.model import WanModel
from .modules.t5 import T5EncoderModel
from .modules.vae import WanVAE
from .utils.fm_solvers import (FlowDPMSolverMultistepScheduler,
                               get_sampling_sigmas, retrieve_timesteps)
from .utils.fm_solvers_unipc import FlowUniPCMultistepScheduler


class WanT2V:

    def __init__(
        self,
        config,
        checkpoint_dir,
        device_id=0,
        rank=0,
        t5_fsdp=False,
        dit_fsdp=False,
        use_usp=False,
        t5_cpu=False,
    ):
        r"""
        Initializes the Wan text-to-video generation model components.

        Args:
            config (EasyDict):
                Object containing model parameters initialized from config.py
            checkpoint_dir (`str`):
                Path to directory containing model checkpoints
            device_id (`int`,  *optional*, defaults to 0):
                Id of target GPU device
            rank (`int`,  *optional*, defaults to 0):
                Process rank for distributed training
            t5_fsdp (`bool`, *optional*, defaults to False):
                Enable FSDP sharding for T5 model
            dit_fsdp (`bool`, *optional*, defaults to False):
                Enable FSDP sharding for DiT model
            use_usp (`bool`, *optional*, defaults to False):
                Enable distribution strategy of USP.
            t5_cpu (`bool`, *optional*, defaults to False):
                Whether to place T5 model on CPU. Only works without t5_fsdp.
        """
        self.device = torch.device(f"cuda:{device_id}")
        self.config = config
        self.rank = rank
        self.t5_cpu = t5_cpu

        self.num_train_timesteps = config.num_train_timesteps
        self.param_dtype = config.param_dtype

        shard_fn = partial(shard_model, device_id=device_id)
        self.text_encoder = T5EncoderModel(
            text_len=config.text_len,
            dtype=config.t5_dtype,
            device=torch.device('cpu'),
            checkpoint_path=os.path.join(checkpoint_dir, config.t5_checkpoint),
            tokenizer_path=config.t5_tokenizer,
            shard_fn=shard_fn if t5_fsdp else None)

        self.vae_stride = config.vae_stride
        self.patch_size = config.patch_size
        self.vae = WanVAE(
            vae_pth=os.path.join(checkpoint_dir, config.vae_checkpoint),
            device=self.device)

        logging.info(f"Creating WanModel from {checkpoint_dir}")
        self.model = WanModel.from_pretrained(checkpoint_dir)
        self.model.eval().requires_grad_(False)

        if use_usp:
            from xfuser.core.distributed import \
                get_sequence_parallel_world_size

            from .distributed.xdit_context_parallel import (usp_attn_forward,
                                                            usp_dit_forward)
            for block in self.model.blocks:
                block.self_attn.forward = types.MethodType(
                    usp_attn_forward, block.self_attn)
            self.model.forward = types.MethodType(usp_dit_forward, self.model)
            self.sp_size = get_sequence_parallel_world_size()
        else:
            self.sp_size = 1

        if dist.is_initialized():
            dist.barrier()
        if dit_fsdp:
            self.model = shard_fn(self.model)
        else:
            self.model.to(self.device)

        self.sample_neg_prompt = config.sample_neg_prompt

    def generate(self,
                 input_prompt,
                 size=(1280, 720),
                 frame_num=81,
                 shift=5.0,
                 sample_solver='unipc',
                 sampling_steps=50,
                 guide_scale=5.0,
                 n_prompt="",
                 seed=-1,
                 offload_model=True):
        r"""
        Generates video frames from text prompt using diffusion process.

        Args:
            input_prompt (`str`):
                Text prompt for content generation
            size (tupele[`int`], *optional*, defaults to (1280,720)):
                Controls video resolution, (width,height).
            frame_num (`int`, *optional*, defaults to 81):
                How many frames to sample from a video. The number should be 4n+1
            shift (`float`, *optional*, defaults to 5.0):
                Noise schedule shift parameter. Affects temporal dynamics
            sample_solver (`str`, *optional*, defaults to 'unipc'):
                Solver used to sample the video.
            sampling_steps (`int`, *optional*, defaults to 40):
                Number of diffusion sampling steps. Higher values improve quality but slow generation
            guide_scale (`float`, *optional*, defaults 5.0):
                Classifier-free guidance scale. Controls prompt adherence vs. creativity
            n_prompt (`str`, *optional*, defaults to ""):
                Negative prompt for content exclusion. If not given, use `config.sample_neg_prompt`
            seed (`int`, *optional*, defaults to -1):
                Random seed for noise generation. If -1, use random seed.
            offload_model (`bool`, *optional*, defaults to True):
                If True, offloads models to CPU during generation to save VRAM

        Returns:
            torch.Tensor:
                Generated video frames tensor. Dimensions: (C, N H, W) where:
                - C: Color channels (3 for RGB)
                - N: Number of frames (81)
                - H: Frame height (from size)
                - W: Frame width from size)
        """
        # preprocess
        F = frame_num
        target_shape = (self.vae.model.z_dim, (F - 1) // self.vae_stride[0] + 1,
                        size[1] // self.vae_stride[1],
                        size[0] // self.vae_stride[2])

        seq_len = math.ceil((target_shape[2] * target_shape[3]) /
                            (self.patch_size[1] * self.patch_size[2]) *
                            target_shape[1] / self.sp_size) * self.sp_size

        if n_prompt == "":
            n_prompt = self.sample_neg_prompt
        seed = seed if seed >= 0 else random.randint(0, sys.maxsize)
        seed_g = torch.Generator(device=self.device)
        seed_g.manual_seed(seed)

        if not self.t5_cpu:
            self.text_encoder.model.to(self.device)
            context = self.text_encoder([input_prompt], self.device)
            context_null = self.text_encoder([n_prompt], self.device)
            if offload_model:
                self.text_encoder.model.cpu()
        else:
            context = self.text_encoder([input_prompt], torch.device('cpu'))
            context_null = self.text_encoder([n_prompt], torch.device('cpu'))
            context = [t.to(self.device) for t in context]
            context_null = [t.to(self.device) for t in context_null]

        noise = [
            torch.randn(
                target_shape[0],
                target_shape[1],
                target_shape[2],
                target_shape[3],
                dtype=torch.float32,
                device=self.device,
                generator=seed_g)
        ]

        # Ensure non-zero initialization
        # Add additional initialization verification
        for i in range(len(noise)):
            if noise[i].abs().max() < 1e-6:
                logging.warning(f"Initial noise tensor {i} has very small values, adding additional noise")
                noise[i] += torch.randn_like(noise[i]) * 0.1
        logging.info(f"Initial noise range: [{noise[0].min().item():.4f}, {noise[0].max().item():.4f}]")

        @contextmanager
        def noop_no_sync():
            yield

        no_sync = getattr(self.model, 'no_sync', noop_no_sync)

        # evaluation mode
        with torch.amp.autocast('cuda', dtype=self.param_dtype), torch.no_grad(), no_sync():

            if sample_solver == 'unipc':
                sample_scheduler = FlowUniPCMultistepScheduler(
                    num_train_timesteps=self.num_train_timesteps,
                    shift=1,
                    use_dynamic_shifting=False)
                sample_scheduler.set_timesteps(
                    sampling_steps, device=self.device, shift=shift)
                timesteps = sample_scheduler.timesteps
            elif sample_solver == 'dpm++':
                sample_scheduler = FlowDPMSolverMultistepScheduler(
                    num_train_timesteps=self.num_train_timesteps,
                    shift=1,
                    use_dynamic_shifting=False)
                sampling_sigmas = get_sampling_sigmas(sampling_steps, shift)
                timesteps, _ = retrieve_timesteps(
                    sample_scheduler,
                    device=self.device,
                    sigmas=sampling_sigmas)
            else:
                raise NotImplementedError("Unsupported solver.")

            # sample videos
            latents = noise

            arg_c = {'context': context, 'seq_len': seq_len}
            arg_null = {'context': context_null, 'seq_len': seq_len}

            for _, t in enumerate(tqdm(timesteps)):
                latent_model_input = latents
                timestep = [t]

                timestep = torch.stack(timestep)

                self.model.to(self.device)
                noise_pred_cond = self.model(
                    latent_model_input, t=timestep, **arg_c)[0]
                noise_pred_uncond = self.model(
                    latent_model_input, t=timestep, **arg_null)[0]

                # 记录模型输出的详细形状信息
                logging.info(f"模型输出形状: noise_pred_cond={noise_pred_cond.shape}, noise_pred_uncond={noise_pred_uncond.shape}")
                logging.info(f"latents形状: latent_model_input={[l.shape for l in latent_model_input]}")

                noise_pred = noise_pred_uncond + guide_scale * (
                    noise_pred_cond - noise_pred_uncond)
                
                # 记录计算后的noise_pred形状
                logging.info(f"指导缩放后的noise_pred形状: {noise_pred.shape}, 数据类型: {noise_pred.dtype}")
                logging.info(f"latents[0]形状: {latents[0].shape}, 数据类型: {latents[0].dtype}")

                # 选择中间帧而不是首帧
                if noise_pred.ndim == 5 and latents[0].ndim == 4:
                    logging.info(f"使用更稳定的形状匹配：选择帧索引5而非中间帧")
                    # 使用帧索引5而不是中间帧，这通常包含更明确的内容
                    frame_idx = min(5, noise_pred.shape[2] - 1)  # 避免索引越界
                    noise_pred = noise_pred[:, :, frame_idx, :, :]
                    logging.info(f"选择帧索引{frame_idx}后的形状: {noise_pred.shape}")
                    
                    # 确保通道数匹配
                    if noise_pred.shape[1] != latents[0].shape[1]:
                        target_channels = latents[0].shape[1]
                        # 如果通道数过多，选择前N个通道
                        if noise_pred.shape[1] > target_channels:
                            noise_pred = noise_pred[:, :target_channels, :, :]
                        # 如果通道数不足，通过复制第一个通道来扩展
                        else:
                            missing_channels = target_channels - noise_pred.shape[1]
                            if missing_channels > 0:
                                first_channel = noise_pred[:, :1, :, :].repeat(1, missing_channels, 1, 1)
                                noise_pred = torch.cat([noise_pred, first_channel], dim=1)
                        
                        logging.info(f"调整通道数后的形状: {noise_pred.shape}")
                    
                # 确保形状完全匹配
                if noise_pred.shape != latents[0].shape:
                    logging.info(f"进行完整形状匹配")
                    # 创建与latents[0]相同形状的目标张量
                    matched_noise = torch.zeros_like(latents[0])
                    
                    # 确定可复制的最大范围
                    min_batch = min(noise_pred.shape[0], latents[0].shape[0])
                    min_channels = min(noise_pred.shape[1], latents[0].shape[1])
                    min_height = min(noise_pred.shape[2], latents[0].shape[2])
                    min_width = min(noise_pred.shape[3], latents[0].shape[3])
                    
                    # 复制可用数据
                    matched_noise[:min_batch, :min_channels, :min_height, :min_width] = \
                        noise_pred[:min_batch, :min_channels, :min_height, :min_width]
                    
                    # 如果还有剩余部分需要填充，使用边缘值而不是随机噪声
                    if matched_noise.shape[1] > min_channels:
                        last_valid_channel = matched_noise[:, min_channels-1:min_channels, :, :]
                        for c in range(min_channels, matched_noise.shape[1]):
                            matched_noise[:, c:c+1, :, :] = last_valid_channel
                    
                    if matched_noise.shape[2] > min_height:
                        last_valid_row = matched_noise[:, :, min_height-1:min_height, :]
                        for h in range(min_height, matched_noise.shape[2]):
                            matched_noise[:, :, h:h+1, :] = last_valid_row
                    
                    if matched_noise.shape[3] > min_width:
                        last_valid_col = matched_noise[:, :, :, min_width-1:min_width]
                        for w in range(min_width, matched_noise.shape[3]):
                            matched_noise[:, :, :, w:w+1] = last_valid_col
                    
                    noise_pred = matched_noise
                    logging.info(f"完整形状匹配后: {noise_pred.shape}")
                
                # 确保噪声预测值具有适当的强度以生成清晰内容
                noise_magnitude = noise_pred.abs().mean()
                if noise_magnitude < 0.1:
                    logging.warning(f"噪声强度过低: {noise_magnitude.item():.4f}，进行放大")
                    # 放大信号但保持相对方向不变
                    scaling_factor = 0.2 / max(noise_magnitude.item(), 1e-5)
                    noise_pred = noise_pred * scaling_factor
                    logging.info(f"放大后的噪声范围: [{noise_pred.min().item():.4f}, {noise_pred.max().item():.4f}]")
                
                # 添加少量噪声避免图像过于平滑
                noise_pred = noise_pred + torch.randn_like(noise_pred) * 0.05

                # 添加维度以满足scheduler的要求
                unsqueezed_noise_pred = noise_pred.unsqueeze(0)
                unsqueezed_latents = latents[0].unsqueeze(0)
                logging.info(f"Unsqueezed张量形状: noise_pred={unsqueezed_noise_pred.shape}, latents[0]={unsqueezed_latents.shape}")

                # 计算下一步的latents
                temp_x0 = sample_scheduler.step(
                    unsqueezed_noise_pred,
                    t,
                    unsqueezed_latents,
                    return_dict=False,
                    generator=seed_g)[0]
                
                # 提取结果
                latents = [temp_x0.squeeze(0)]

            x0 = latents
            if offload_model:
                self.model.cpu()
                torch.cuda.empty_cache()
            if self.rank == 0:
                # 更稳健的解码流程，确保不会出现黑屏
                try:
                    # 检查x0的类型，确保它是一个张量而不是列表
                    logging.info(f"解码前x0的类型: {type(x0)}")
                    
                    # 如果x0是列表，尝试提取有效的张量
                    if isinstance(x0, list):
                        if len(x0) > 0 and isinstance(x0[0], torch.Tensor):
                            logging.info(f"从列表中提取张量，原始x0长度: {len(x0)}")
                            x0_tensor = x0[0]  # 提取列表中的第一个张量
                            logging.info(f"提取的x0_tensor形状: {x0_tensor.shape}")
                        else:
                            # 列表为空或不包含张量，创建随机噪声作为回退
                            logging.warning("x0列表为空或不包含有效张量，创建随机噪声替代")
                            x0_tensor = torch.randn(
                                target_shape,
                                dtype=torch.float32,
                                device=self.device
                            ) * 0.1
                            logging.info(f"创建的随机噪声形状: {x0_tensor.shape}")
                    else:
                        # 已经是张量了，直接使用
                        x0_tensor = x0
                        logging.info(f"x0已经是张量，形状: {x0_tensor.shape}")
                    
                    # 确保x0_tensor不是全零或全接近零
                    if x0_tensor.abs().max() < 1e-6:
                        logging.warning("解码前的x0_tensor几乎为零，添加随机噪声")
                        x0_tensor = x0_tensor + torch.randn_like(x0_tensor) * 0.1
                    
                    # 使用转换后的张量进行解码
                    logging.info(f"正在解码，x0_tensor范围: [{x0_tensor.min().item():.4f}, {x0_tensor.max().item():.4f}]")
                    videos = self.vae.decode(x0_tensor)
                    
                    # 验证解码后的视频张量
                    if videos is not None:
                        # 分析张量值以确保非零输出
                        if hasattr(videos, 'shape'):
                            video_min = videos.min().item() if hasattr(videos, 'min') else 0
                            video_max = videos.max().item() if hasattr(videos, 'max') else 0
                            logging.info(f"VAE decoded video range: [{video_min:.4f}, {video_max:.4f}]")
                            
                            # 添加噪声如果值太均匀
                            if abs(video_max - video_min) < 1e-3:
                                logging.warning("VAE decoded video values are too uniform, adding noise")
                                videos = videos + torch.randn_like(videos) * 0.1
                                # 使用tanh确保值在合理范围内
                                videos = torch.tanh(videos)
                        else:
                            logging.warning("解码后的videos没有shape属性")
                    else:
                        logging.warning("VAE解码结果为None")
                    
                    # 记录视频张量信息
                    logging.info(f"VAE解码成功，输出videos形状: {videos.shape if hasattr(videos, 'shape') else type(videos)}")
                    
                    # 确保videos是正确的格式：需要转换为(T, H, W, 3)格式
                    if hasattr(videos, 'shape'):
                        shape = videos.shape
                        logging.info(f"检查视频帧格式: 形状={shape}, 数据类型={videos.dtype}")
                        
                        # 统一格式为(T, H, W, C)，这是后续处理所期望的格式
                        if len(shape) == 4:
                            # 对应不同的输入格式进行转换
                            if shape[0] == 3 and shape[1] >= frame_num:  # (C, T, H, W)
                                logging.info(f"检测到(C, T, H, W)格式，转换为(T, H, W, C)")
                                videos = videos.permute(1, 2, 3, 0)
                            elif shape[1] == 3 and shape[0] >= frame_num:  # (T, C, H, W)
                                logging.info(f"检测到(T, C, H, W)格式，转换为(T, H, W, C)")
                                videos = videos.permute(0, 2, 3, 1)
                            elif shape[-1] == 3:  # 已经是(T, H, W, C)
                                logging.info(f"已经是(T, H, W, C)格式，无需转换")
                            else:
                                logging.warning(f"无法确定维度顺序，进行启发式推断")
                                # 基于常见视频尺寸进行推断
                                if shape[0] < 5 and shape[1] >= frame_num:  # 可能是(C, T, H, W)
                                    videos = videos.permute(1, 2, 3, 0)
                                elif shape[0] >= frame_num and shape[1] < 5:  # 可能是(T, C, H, W)
                                    videos = videos.permute(0, 2, 3, 1)
                                else:
                                    # 如果形状仍然无法确定，尝试生成正确形状的随机张量
                                    logging.warning(f"无法确定维度，创建标准格式随机张量")
                                    videos = torch.rand((frame_num, size[1], size[0], 3), 
                                                      device=self.device) * 0.6 - 0.3
                        elif len(shape) < 4:
                            logging.warning(f"维度不足: {shape}，创建标准格式随机张量")
                            videos = torch.rand((frame_num, size[1], size[0], 3), 
                                              device=self.device) * 0.6 - 0.3
                        
                        # 确保所有视频帧的通道维度正确
                        if videos.shape[-1] != 3:
                            actual_channels = videos.shape[-1]
                            logging.warning(f"视频帧通道数量不正确，当前为{actual_channels}，目标为3")
                            
                            if actual_channels == 1:
                                # 单通道灰度图，复制到三个通道
                                videos = videos.repeat(1, 1, 1, 3) if videos.shape[-1] == 1 else torch.cat([videos] * 3, dim=-1)
                            elif actual_channels > 3:
                                # 过多通道，只保留前三个
                                videos = videos[..., :3]
                            
                        # 确保帧数正确
                        if videos.shape[0] != frame_num:
                            logging.warning(f"帧数不匹配: 当前{videos.shape[0]}，目标{frame_num}")
                            
                            if videos.shape[0] > frame_num:
                                # 如果帧数过多，只保留需要的帧
                                videos = videos[:frame_num]
                            else:
                                # 如果帧数不足，重复最后一帧
                                last_frame = videos[-1:].repeat(frame_num - videos.shape[0], 1, 1, 1)
                                videos = torch.cat([videos, last_frame], dim=0)
                        
                        # 确保尺寸正确
                        if videos.shape[1] != size[1] or videos.shape[2] != size[0]:
                            logging.warning(f"尺寸不匹配: 当前{videos.shape[1]}x{videos.shape[2]}，目标{size[1]}x{size[0]}")
                            
                            # 使用插值调整大小
                            videos_reshaped = videos.permute(0, 3, 1, 2)  # (T, H, W, C) -> (T, C, H, W)
                            videos_resized = torch.nn.functional.interpolate(
                                videos_reshaped, 
                                size=(size[1], size[0]), 
                                mode='bilinear', 
                                align_corners=False
                            )
                            videos = videos_resized.permute(0, 2, 3, 1)  # (T, C, H, W) -> (T, H, W, C)
                        
                        # 确保值范围合理
                        if videos.min() < -1.0 or videos.max() > 1.0:
                            logging.warning(f"值范围异常: [{videos.min().item():.4f}, {videos.max().item():.4f}]")
                            videos = torch.tanh(videos)
                            logging.info(f"归一化后范围: [{videos.min().item():.4f}, {videos.max().item():.4f}]")
                        
                        # 确保值范围在0-1之间（如果为[-1,1]范围则转换）
                        if videos.min() < 0:
                            videos = (videos + 1) / 2
                            logging.info(f"转换到[0,1]范围: [{videos.min().item():.4f}, {videos.max().item():.4f}]")
                    else:
                        logging.error("生成的视频帧没有shape属性")
                        videos = torch.rand((frame_num, size[1], size[0], 3), 
                                         device=self.device) * 0.6 - 0.3
                        videos = (videos + 1) / 2  # 转换到[0,1]范围
                except Exception as e:
                    logging.error(f"VAE解码出错: {str(e)}")
                    # 提供详细的异常信息
                    import traceback
                    logging.error(traceback.format_exc())
                    
                    # 创建一个合理的替代视频
                    logging.warning("创建应急视频帧(T, H, W, 3)格式")
                    videos = torch.rand(
                        (frame_num, size[1], size[0], 3),
                        dtype=torch.float32,
                        device=self.device
                    ) * 0.6 - 0.3
                    videos = (videos + 1) / 2  # 转换到[0,1]范围
                    logging.warning(f"创建的应急视频形状: {videos.shape}")

        del noise, latents
        del sample_scheduler
        if offload_model:
            gc.collect()
            torch.cuda.synchronize()
        if dist.is_initialized():
            dist.barrier()

        # 确保有有效的输出，即使在出现问题的情况下
        if self.rank == 0:
            if videos is None:
                logging.warning("生成的videos为None，创建一个零张量作为替代")
                videos = torch.zeros(
                    (3, frame_num, size[1], size[0]),
                    dtype=torch.float32,
                    device=self.device
                )
            
            # 确保返回的是张量，而不是None或其他类型
            try:
                result = videos[0] if isinstance(videos, (list, tuple)) and len(videos) > 0 else videos
                # 验证结果是否是张量
                if not isinstance(result, torch.Tensor):
                    logging.warning(f"结果不是张量，而是 {type(result)}，创建零张量替代")
                    result = torch.zeros(
                        (3, frame_num, size[1], size[0]),
                        dtype=torch.float32,
                        device=self.device
                    )
                logging.info(f"最终返回的视频形状: {result.shape}")
                return result
            except Exception as e:
                logging.error(f"准备返回结果时出错: {str(e)}")
                # 最终应急措施：返回零张量
                return torch.zeros(
                    (3, frame_num, size[1], size[0]),
                    dtype=torch.float32,
                    device=self.device
                )
        else:
            return None
