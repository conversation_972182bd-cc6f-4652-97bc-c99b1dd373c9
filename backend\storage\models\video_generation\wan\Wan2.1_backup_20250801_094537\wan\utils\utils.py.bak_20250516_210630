# Copyright 2024-2025 The Alibaba Wan Team Authors. All rights reserved.
import argparse
import binascii
import os
import os.path as osp

import imageio
import torch
import torchvision

__all__ = ['cache_video', 'cache_image', 'str2bool']


def rand_name(length=8, suffix=''):
    name = binascii.b2a_hex(os.urandom(length)).decode('utf-8')
    if suffix:
        if not suffix.startswith('.'):
            suffix = '.' + suffix
        name += suffix
    return name


def cache_video(tensor,
                save_file=None,
                fps=30,
                suffix='.mp4',
                nrow=8,
                normalize=True,
                value_range=(-1, 1),
                retry=5,
                quality=8,  # quality参数范围在1-10之间
                bitrate=None):  # 可选的比特率参数，单位kbps
    """
    将视频张量保存为MP4文件。
    
    Args:
        tensor: 要保存的视频张量，支持多种格式：
            - (T, C, H, W): 帧数, 通道数, 高度, 宽度
            - (T, H, W, C): 帧数, 高度, 宽度, 通道数
            - (B, T, C, H, W): 批次, 帧数, 通道数, 高度, 宽度
            - 其他格式也会尝试自动处理
        save_file: 保存路径，如果为None，则保存到临时文件
        fps: 帧率，默认30
        suffix: 文件后缀，默认'.mp4'
        nrow: 网格排列时每行的图像数，默认8
        normalize: 是否归一化像素值，默认True
        value_range: 归一化的值范围，默认(-1, 1)
        retry: 重试次数，默认5
        quality: 视频质量参数(1-10)，值越大质量越高，默认8
        bitrate: 视频比特率，单位kbps，默认为None(使用默认比特率)
    
    Returns:
        成功时返回保存的文件路径，失败时返回None
    """
    # 确保quality在有效范围内
    quality = max(1, min(10, quality))
    
    # cache file
    cache_file = osp.join('/tmp', rand_name(
        suffix=suffix)) if save_file is None else save_file

    # 准备额外的编码器参数
    extra_args = {}
    if bitrate is not None:
        extra_args['bitrate'] = int(bitrate) * 1000  # 转换为bps
    
    # save to cache
    error = None
    for attempt in range(retry):
        try:
            # 处理不同维度的输入
            if tensor.dim() == 6:  # (B, T, C, 3, H, W) 格式，需要降维
                print(f"检测到6维张量，形状: {tensor.shape}，执行降维操作", flush=True)
                # 假设B=1和C=1
                tensor = tensor.squeeze(0).squeeze(1)  # 现在应该是 (T, 3, H, W)
            elif tensor.dim() == 5:  # (B, T, 3, H, W) 或 (T, C, 3, H, W) 格式
                print(f"检测到5维张量，形状: {tensor.shape}，执行降维操作", flush=True)
                tensor = tensor.squeeze(0)  # 假设B=1，现在应该是 (T, 3, H, W) 或 (T, 3, H, W)
            elif tensor.dim() == 4 and tensor.shape[1] != 3:  # (T, C, H, W) 但C不是3
                print(f"检测到4维张量但通道数不是3，形状: {tensor.shape}，检查是否需要调整", flush=True)
                if tensor.shape[1] == 1 and tensor.shape[2] == 3:  # (T, 1, 3, H, W) 格式误传
                    tensor = tensor.squeeze(1)  # 变为 (T, 3, H, W)
            
            # 如果是(T, H, W, C)格式，需要转换为(T, C, H, W)
            if tensor.dim() == 4 and tensor.shape[3] == 3:  # (T, H, W, C) 格式
                print(f"检测到(T, H, W, C)格式，形状: {tensor.shape}，转换为(T, C, H, W)", flush=True)
                tensor = tensor.permute(0, 3, 1, 2)  # 变为 (T, C, H, W)
            
            # 打印处理后的张量形状
            print(f"处理后的张量形状: {tensor.shape}, 数据类型: {tensor.dtype}", flush=True)
            
            # preprocess
            tensor = tensor.clamp(min(value_range), max(value_range))
            tensor = torch.stack([
                torchvision.utils.make_grid(
                    u, nrow=nrow, normalize=normalize, value_range=value_range)
                for u in tensor.unbind(0)  # 现在假设张量是(T, C, H, W)格式
            ],
                                 dim=0).permute(0, 2, 3, 1)  # 变为(T, H, W, C)，适合视频导出
            tensor = (tensor * 255).type(torch.uint8).cpu()

            # write video
            codec_args = {'codec': 'libx264', 'quality': quality}
            codec_args.update(extra_args)  # 添加额外的编码器参数
            
            writer = imageio.get_writer(
                cache_file, fps=fps, **codec_args)
            
            # 保存视频帧
            for frame in tensor.numpy():
                writer.append_data(frame)
            writer.close()
            
            # 记录编码参数和尝试次数信息
            if attempt > 0:
                print(f'成功保存视频，尝试次数: {attempt+1}/{retry}', flush=True)
                
            return cache_file
        except Exception as e:
            error = e
            # 如果是quality/bitrate参数错误，尝试使用更保守的参数
            if 'quality' in str(e).lower() or 'bitrate' in str(e).lower():
                print(f'编码器参数错误，尝试使用更保守的参数: {e}', flush=True)
                extra_args = {}  # 重置额外参数
                quality = max(1, quality - 2)  # 降低质量要求
            continue
    else:
        print(f'cache_video failed, error: {error}', flush=True)
        return None


def cache_image(tensor,
                save_file,
                nrow=8,
                normalize=True,
                value_range=(-1, 1),
                retry=5):
    # cache file
    suffix = osp.splitext(save_file)[1]
    if suffix.lower() not in [
            '.jpg', '.jpeg', '.png', '.tiff', '.gif', '.webp'
    ]:
        suffix = '.png'

    # save to cache
    error = None
    for _ in range(retry):
        try:
            tensor = tensor.clamp(min(value_range), max(value_range))
            torchvision.utils.save_image(
                tensor,
                save_file,
                nrow=nrow,
                normalize=normalize,
                value_range=value_range)
            return save_file
        except Exception as e:
            error = e
            continue


def str2bool(v):
    """
    Convert a string to a boolean.

    Supported true values: 'yes', 'true', 't', 'y', '1'
    Supported false values: 'no', 'false', 'f', 'n', '0'

    Args:
        v (str): String to convert.

    Returns:
        bool: Converted boolean value.

    Raises:
        argparse.ArgumentTypeError: If the value cannot be converted to boolean.
    """
    if isinstance(v, bool):
        return v
    v_lower = v.lower()
    if v_lower in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v_lower in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected (True/False)')
