"""
API v1版本路由
"""

from fastapi import APIRouter
from .digital_human import router as digital_human_router
from .translation import router as translation_router
from .user import router as user_router
from .ai_agent import router as ai_agent_router
from .avatar_manager import router as avatar_manager_router
from .model_manager import router as model_manager_router
from .template_manager import router as template_manager_router
from .image_generation import router as image_generation_router
from .digital_human_generation import router as digital_human_generation_router
from .realtime_conversation import router as realtime_conversation_router
from .system_monitor import router as system_monitor_router
from .task_monitor import router as task_monitor_router
from .tts_api import router as tts_router
from .video_generation import router as video_generation_router
from .file_upload import router as file_upload_router
from .opinion_analysis import router as opinion_analysis_router
from .data_collection import router as data_collection_router
from .websocket_routes import router as websocket_router
from .ollama import router as ollama_router
from .digital_human_realtime import router as digital_human_realtime_router
from .lip_sync import router as lip_sync_router
from .model_training import router as model_training_router

# v1 API路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(digital_human_router, prefix="/digital-human", tags=["数字人"])
api_router.include_router(translation_router, prefix="/translation", tags=["翻译"])
api_router.include_router(user_router, prefix="/user", tags=["用户"])
api_router.include_router(ai_agent_router, prefix="/agents", tags=["AI智能体"])
api_router.include_router(avatar_manager_router, prefix="/avatars", tags=["头像管理"])
api_router.include_router(model_manager_router, prefix="/models", tags=["模型管理"])
api_router.include_router(template_manager_router, prefix="/templates", tags=["模板管理"])
api_router.include_router(image_generation_router, prefix="/image-generation", tags=["图像生成"])
api_router.include_router(digital_human_generation_router, prefix="/digital-human-generation", tags=["数字人生成"])
api_router.include_router(realtime_conversation_router, prefix="/realtime", tags=["实时对话"])
api_router.include_router(system_monitor_router, prefix="/system", tags=["系统监控"])
api_router.include_router(task_monitor_router, prefix="/task-monitor", tags=["任务监控"])
api_router.include_router(tts_router, prefix="/tts", tags=["TTS语音合成"])
api_router.include_router(video_generation_router, prefix="/video-generation", tags=["视频生成"])
api_router.include_router(file_upload_router, prefix="/files", tags=["文件上传"])
api_router.include_router(opinion_analysis_router, prefix="/opinion", tags=["舆情分析"])
api_router.include_router(data_collection_router, prefix="/data-collection", tags=["数据采集"])
api_router.include_router(websocket_router, prefix="/ws", tags=["WebSocket实时通信"])
api_router.include_router(ollama_router, prefix="/ollama", tags=["Ollama本地模型"])
api_router.include_router(digital_human_realtime_router, tags=["数字人实时动画"])
api_router.include_router(lip_sync_router, prefix="/lip-sync", tags=["口型同步"])
api_router.include_router(model_training_router, tags=["模型训练"])
