# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from typing import Union
from typing_extensions import Literal, Annotated, TypeAlias

from .._utils import PropertyInfo
from .._models import BaseModel
from .text_block import TextBlock
from .thinking_block import Thinking<PERSON><PERSON>
from .tool_use_block import Tool<PERSON><PERSON><PERSON><PERSON>
from .server_tool_use_block import ServerTool<PERSON><PERSON><PERSON>lock
from .redacted_thinking_block import RedactedThinking<PERSON>lock
from .web_search_tool_result_block import WebSearchToolResultBlock

__all__ = ["RawContentBlockStartEvent", "ContentBlock"]

ContentBlock: TypeAlias = Annotated[
    Union[TextBlock, ThinkingBlock, RedactedThinking<PERSON>lock, Tool<PERSON>se<PERSON><PERSON>, ServerToolU<PERSON><PERSON>lock, WebSearchToolResultBlock],
    PropertyInfo(discriminator="type"),
]


class RawContentBlockStartEvent(BaseModel):
    content_block: ContentBlock

    index: int

    type: Literal["content_block_start"]
