#!/usr/bin/env python3
"""
测试Wanx 2.1参数映射
验证我们的参数格式是否正确
"""

import sys
import subprocess
from pathlib import Path

def test_wanx_params():
    """测试Wanx 2.1参数映射"""
    
    # 路径设置
    wan_model_path = Path(__file__).parent / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
    wan_script_path = wan_model_path / "generate.py"
    
    print(f"Wanx路径: {wan_model_path}")
    print(f"脚本路径: {wan_script_path}")
    print(f"脚本存在: {wan_script_path.exists()}")
    
    if not wan_script_path.exists():
        print("❌ Wanx脚本不存在")
        return False
    
    # 测试参数格式（不实际生成，只验证参数）
    cmd = [
        sys.executable,
        str(wan_script_path),
        "--help"  # 只显示帮助，不实际运行
    ]
    
    print(f"\n检查参数支持:")
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            help_text = result.stdout
            
            # 检查我们需要的参数是否支持
            required_params = [
                "--task",
                "--size", 
                "--ckpt_dir",
                "--prompt",
                "--offload_model",
                "--t5_cpu",
                "--sample_shift",
                "--sample_guide_scale",
                "--sample_steps",
                "--frame_num",
                "--base_seed",
                "--save_file"
            ]
            
            print("参数支持检查:")
            for param in required_params:
                if param in help_text:
                    print(f"✅ {param}")
                else:
                    print(f"❌ {param}")
            
            # 检查任务类型
            print("\n支持的任务类型:")
            if "t2v-1.3B" in help_text:
                print("✅ t2v-1.3B")
            else:
                print("❌ t2v-1.3B")
                
            if "t2v-14B" in help_text:
                print("✅ t2v-14B")
            else:
                print("❌ t2v-14B")
            
            # 检查分辨率支持
            print("\n支持的分辨率:")
            resolutions = ["832*480", "480*832", "1280*720", "720*1280"]
            for res in resolutions:
                if res in help_text:
                    print(f"✅ {res}")
                else:
                    print(f"❌ {res}")
            
            return True
            
        else:
            print(f"❌ 获取帮助失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_our_command():
    """测试我们构建的命令格式"""
    
    print("\n=== 测试我们的命令格式 ===")
    
    # 模拟我们的参数映射
    wan_model_path = Path(__file__).parent / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
    wan_script_path = wan_model_path / "generate.py"
    
    # 1.3B模型配置
    model = "t2v-1.3B"
    prompt = "cat running"
    size = "832*480"
    ckpt_dir = wan_model_path / "Wan2.1-T2V-1.3B"
    guidance_scale = 6.0
    sample_shift = 8
    num_inference_steps = 20
    num_frames = 17
    seed = 12345
    output_path = wan_model_path / "test_output.mp4"
    
    cmd = [
        sys.executable,
        str(wan_script_path),
        "--task", model,
        "--size", size,
        "--ckpt_dir", str(ckpt_dir),
        "--prompt", prompt,
        "--offload_model", "True",
        "--t5_cpu",
        "--sample_shift", str(sample_shift),
        "--sample_guide_scale", str(guidance_scale),
        "--sample_steps", str(num_inference_steps),
        "--frame_num", str(num_frames),
        "--base_seed", str(seed),
        "--save_file", str(output_path)
    ]
    
    print("构建的命令:")
    print(" ".join(cmd))
    
    # 验证命令格式（不实际执行）
    print(f"\n参数验证:")
    print(f"✅ 任务类型: {model}")
    print(f"✅ 分辨率: {size}")
    print(f"✅ 检查点目录: {ckpt_dir}")
    print(f"✅ 提示词: {prompt}")
    print(f"✅ 模型卸载: True")
    print(f"✅ T5 CPU: 启用")
    print(f"✅ 采样偏移: {sample_shift}")
    print(f"✅ 引导强度: {guidance_scale}")
    print(f"✅ 推理步数: {num_inference_steps}")
    print(f"✅ 帧数: {num_frames}")
    print(f"✅ 随机种子: {seed}")
    print(f"✅ 输出文件: {output_path}")
    
    return True

if __name__ == "__main__":
    print("=== Wanx 2.1 参数测试 ===\n")
    
    print("1. 检查脚本和参数支持:")
    success1 = test_wanx_params()
    
    print("\n" + "="*50)
    
    print("2. 测试我们的命令格式:")
    success2 = test_our_command()
    
    print(f"\n=== 测试结果: {'成功' if success1 and success2 else '失败'} ===")
