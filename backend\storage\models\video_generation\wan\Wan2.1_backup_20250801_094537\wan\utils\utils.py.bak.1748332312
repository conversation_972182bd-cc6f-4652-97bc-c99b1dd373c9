# Copyright 2024-2025 The Alibaba Wan Team Authors. All rights reserved.
import argparse
import binascii
import os
import os.path as osp
import shutil
import logging  # 添加logging导入
import tempfile
import subprocess
import numpy as np
import torch
import torchvision
import random
import cv2
import time
from pathlib import Path

# 配置基本日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# 创建logger实例
logger = logging.getLogger("WAN-Utils")

import imageio

__all__ = ['cache_video', 'cache_image', 'str2bool']


def rand_name(length=8, suffix=''):
    name = binascii.b2a_hex(os.urandom(length)).decode('utf-8')
    if suffix:
        if not suffix.startswith('.'):
            suffix = '.' + suffix
        name += suffix
    return name


def cache_video(tensor,
                save_file=None,
                fps=16,
                suffix=None,
                nrow=None,
                normalize=True,
                value_range=None,
                retry=3,
                quality=8,  # quality参数范围在1-10之间
                bitrate=None):  # 可选的比特率参数，单位kbps
    """
    将张量保存为MP4视频文件。
    
    参数:
        tensor (torch.Tensor | list): 要保存的张量，格式可以是:
            - (T, C, H, W): T帧，每帧C通道，高度H，宽度W
            - (B, T, C, H, W): B批次，每批次T帧，每帧C通道，高度H，宽度W
            - (C, T, H, W): C通道，T帧，高度H，宽度W
            - 也可以是上述格式张量的列表
        save_file (str, optional): 保存路径。如果为None，将生成随机名称。
        fps (int, optional): 视频帧率。默认为16。
        suffix (str, optional): 文件后缀。默认为None，将使用'.mp4'。
        nrow (int, optional): 如果张量是批次，指定每行显示的样本数。默认为None。
        normalize (bool, optional): 是否归一化张量值。默认为True。
        value_range (tuple, optional): 归一化的值范围。默认为None。
        retry (int, optional): 保存失败时的重试次数。默认为3。
        quality (int, optional): 视频质量，范围1-10。默认为8。
        bitrate (str, optional): 视频比特率。默认为None。
    
    返回:
        str: 保存的视频文件路径
    """
    logger.info(f"最终视频帧形状: {tensor.shape}")
    
    # 确保张量是浮点类型，便于后续处理
    if not tensor.is_floating_point():
        tensor = tensor.to(torch.float32)
        logger.info(f"将张量转换为浮点类型: {tensor.dtype}")
    
    # 确保张量在CPU上
    if tensor.is_cuda:
        tensor = tensor.cpu()
        logger.info("将张量从GPU移至CPU")
    
    # 自动检测数据范围
    if value_range is None:
        min_val = tensor.min().item()
        max_val = tensor.max().item()
        logger.info(f"自动检测数据范围: [{min_val}, {max_val}]")
        
        # 根据检测到的范围推断归一化方式
        if max_val <= 1.0 and min_val >= 0.0:
            logger.info("检测到数据范围为0-1")
        elif max_val <= 255.0 and min_val >= 0.0:
            logger.info("检测到数据范围可能为0-255，归一化到0-1")
            tensor = tensor / 255.0
    
    # 检查张量维度
    if tensor.dim() < 3:
        raise ValueError(f"张量维度过低: {tensor.dim()}, 需要至少3维")
    
    # 处理张量格式
    if tensor.dim() == 3:  # (C, H, W) -> (1, C, H, W)
        tensor = tensor.unsqueeze(0)
        
    # 检查视频帧数量
    if tensor.shape[0] < 16:
        logger.warning(f"视频帧数过少 ({tensor.shape[0]}), 增加到至少16帧")
        # 重复帧以达到至少16帧
        repeat_factor = max(1, int(16 / tensor.shape[0]) + 1)
        tensor = tensor.repeat(repeat_factor, 1, 1, 1)
        tensor = tensor[:16]  # 截取前16帧
    
    # 检查张量格式并转换为(T, H, W, C)格式
    if tensor.shape[1] == 3:  # 可能是(T, C, H, W)格式
        logger.info("检测到(T, C, H, W)格式，转换为(T, H, W, C)")
        tensor = tensor.permute(0, 2, 3, 1)
    
    # 检查张量值是否全部相同
    if tensor.min().item() == tensor.max().item():
        logger.warning(f"警告: 视频帧数据可能无效，所有值相同: {tensor.min().item()}")
        print(f"警告: 视频帧数据可能无效，所有值相同: {tensor.min().item()}")
        print("尝试添加小噪声以避免全黑视频")
        # 添加小噪声
        tensor = tensor + torch.randn_like(tensor) * 0.01
    
    # 确保值在0-1范围内
    if normalize:
        if value_range is not None:
            min_val, max_val = value_range
            tensor = (tensor - min_val) / (max_val - min_val)
        tensor = torch.clamp(tensor, 0, 1)
    
    # 转换为0-255范围的uint8类型，用于保存
    logger.info("检测到视频数据范围为0-1，转换为0-255")
    tensor = tensor.float()  # 确保是浮点型
    tensor = torch.clamp(tensor, 0, 1)  # 确保值在0-1范围内
    tensor = (tensor * 255.0).round()  # 四舍五入到最接近的整数
    tensor = tensor.type(torch.uint8).cpu()  # 转换为uint8类型
    
    # 处理保存路径
    if save_file is None:
        save_file = f"video_{int(time.time())}.mp4"
    elif suffix is not None and not save_file.endswith(suffix):
        save_file = f"{save_file}{suffix}"
    elif not save_file.endswith('.mp4'):
        save_file = f"{save_file}.mp4"
    
    # 确保保存目录存在
    save_dir = os.path.dirname(save_file)
    if save_dir and not os.path.exists(save_dir):
        try:
            os.makedirs(save_dir, exist_ok=True)
            logger.info(f"创建保存目录: {save_dir}")
        except Exception as e:
            logger.warning(f"无法创建保存目录 {save_dir}: {e}")
            # 如果无法创建目录，则保存到当前目录
            save_file = os.path.basename(save_file)
            logger.info(f"将保存到当前目录: {save_file}")
    
    logger.info(f"最终视频形状: {tensor.shape} - 准备保存到 {save_file}")
    
    # 使用临时目录保存帧
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"创建临时目录保存视频帧: {temp_dir}")
        
        try:
            # 保存每一帧为PNG
            for i in range(tensor.shape[0]):
                frame = tensor[i].numpy()
                cv2.imwrite(os.path.join(temp_dir, f"frame_{i:04d}.png"), 
                          cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))
            
            # 使用FFmpeg生成视频
            cmd = [
                "ffmpeg", "-y",
                "-framerate", str(fps),
                "-i", os.path.join(temp_dir, "frame_%04d.png"),
                "-c:v", "libx264",
                "-preset", "slow",
                "-crf", str(18),  # 质量控制，数值越低质量越高
                "-pix_fmt", "yuv420p",
                save_file
            ]
            
            # 添加比特率参数
            if bitrate:
                cmd.extend(["-b:v", bitrate])
            
            # 执行FFmpeg命令
            logger.info(f"执行FFmpeg命令: {' '.join(cmd)}")
            process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            stdout, stderr = process.communicate()
            
            if process.returncode != 0:
                logger.error(f"使用FFmpeg保存视频失败: {stderr.decode('utf-8', errors='ignore')}")
                # 尝试使用备用方法
                try:
                    backup_file = save_file.replace(".mp4", "_backup.mp4")
                    logger.info(f"尝试使用OpenCV保存到备用文件: {backup_file}")
                    
                    # 确保备用文件的目录存在
                    backup_dir = os.path.dirname(backup_file)
                    if backup_dir and not os.path.exists(backup_dir):
                        os.makedirs(backup_dir, exist_ok=True)
                    
                    video_writer = cv2.VideoWriter(
                        backup_file,
                        cv2.VideoWriter_fourcc(*'mp4v'),
                        fps,
                        (tensor.shape[2], tensor.shape[1])
                    )
                    
                    # 设置视频质量
                    try:
                        video_writer.set(cv2.VIDEOWRITER_PROP_QUALITY, quality * 10)
                    except Exception as e:
                        logger.warning(f"设置视频质量参数失败: {e}")
                    
                    for i in range(tensor.shape[0]):
                        frame = tensor[i].numpy()
                        video_writer.write(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))
                    
                    video_writer.release()
                    
                    # 检查文件是否成功保存
                    if os.path.exists(backup_file) and os.path.getsize(backup_file) > 0:
                        logger.info(f"使用备用方法成功保存视频: {backup_file}")
                        return backup_file
                    else:
                        logger.error(f"备用保存方法也失败: 文件不存在或大小为0")
                        raise Exception("备用保存方法也失败")
                except Exception as e:
                    logger.error(f"备用保存方法也失败: {e}")
                    raise e
            else:
                # 检查文件是否成功保存
                if os.path.exists(save_file) and os.path.getsize(save_file) > 0:
                    logger.info(f"FFmpeg成功生成视频: {save_file}, 大小: {os.path.getsize(save_file)} 字节")
                    return save_file
                else:
                    logger.error(f"使用FFmpeg保存视频文件失败，文件不存在或大小为0: {save_file}")
                    raise Exception("FFmpeg保存失败，文件不存在或大小为0")
                
        except Exception as e:
            if retry > 0:
                logger.info(f"重试保存视频 (剩余尝试次数: {retry-1})")
                return cache_video(tensor, save_file, fps, None, nrow, normalize, value_range, retry-1, quality, bitrate)
            else:
                logger.error(f"所有保存尝试都失败")
                raise e
    
    return save_file


def cache_image(tensor,
                save_file,
                nrow=8,
                normalize=True,
                value_range=(-1, 1),
                retry=5):
    # cache file
    suffix = osp.splitext(save_file)[1]
    if suffix.lower() not in [
            '.jpg', '.jpeg', '.png', '.tiff', '.gif', '.webp'
    ]:
        suffix = '.png'

    # save to cache
    error = None
    for attempt in range(retry):
        try:
            tensor = tensor.clamp(min(value_range), max(value_range))
            torchvision.utils.save_image(
                tensor,
                save_file,
                nrow=nrow,
                normalize=normalize,
                value_range=value_range)
            return save_file
        except Exception as e:
            error = e
            continue


def str2bool(v):
    """
    Convert a string to a boolean.

    Supported true values: 'yes', 'true', 't', 'y', '1'
    Supported false values: 'no', 'false', 'f', 'n', '0'

    Args:
        v (str): String to convert.

    Returns:
        bool: Converted boolean value.

    Raises:
        argparse.ArgumentTypeError: If the value cannot be converted to boolean.
    """
    if isinstance(v, bool):
        return v
    v_lower = v.lower()
    if v_lower in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v_lower in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected (True/False)')
