# Wanx 2.1 专用Docker环境
# 解决Windows环境兼容性问题

FROM nvidia/cuda:12.1-devel-ubuntu22.04

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV CUDA_HOME=/usr/local/cuda
ENV PATH=${CUDA_HOME}/bin:${PATH}
ENV LD_LIBRARY_PATH=${CUDA_HOME}/lib64:${LD_LIBRARY_PATH}

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    python3.10 \
    python3.10-dev \
    python3-pip \
    git \
    wget \
    curl \
    ffmpeg \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libglib2.0-0 \
    libgl1-mesa-glx \
    && rm -rf /var/lib/apt/lists/*

# 创建Python符号链接
RUN ln -s /usr/bin/python3.10 /usr/bin/python

# 升级pip
RUN python -m pip install --upgrade pip

# 安装PyTorch（CUDA 12.1兼容）
RUN pip install torch==2.4.0 torchvision==0.19.0 torchaudio==2.4.0 --index-url https://download.pytorch.org/whl/cu121

# 安装Wanx依赖
RUN pip install \
    diffusers==0.30.2 \
    transformers==4.44.0 \
    tokenizers==0.19.1 \
    accelerate==0.33.0 \
    opencv-python==********* \
    imageio==2.35.1 \
    imageio-ffmpeg==0.5.1 \
    easydict==1.13 \
    ftfy==6.2.3 \
    numpy==1.26.4 \
    tqdm==4.66.5

# 尝试安装flash_attn（可选）
RUN pip install flash-attn --no-build-isolation || echo "flash_attn installation failed, continuing..."

# 创建工作目录
WORKDIR /app

# 创建模型目录
RUN mkdir -p /app/models

# 复制Wanx脚本（将在运行时挂载）
VOLUME ["/app/models", "/app/output"]

# 设置入口点
COPY docker_entrypoint.py /app/
RUN chmod +x /app/docker_entrypoint.py

# 暴露端口（如果需要）
EXPOSE 8000

# 入口点
ENTRYPOINT ["python", "/app/docker_entrypoint.py"]
