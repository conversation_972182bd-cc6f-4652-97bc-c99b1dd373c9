import torch
import torch.nn as nn

class SimpleVAE(nn.Module):
    """
    简化版VAE模型，作为WAN 2.1模型加载过程中的替代品
    """
    def __init__(self):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.Conv2d(3, 16, kernel_size=3, padding=1),
            nn.ReLU()
        )
        self.decoder = nn.Sequential(
            nn.Conv2d(16, 3, kernel_size=3, padding=1),
            nn.Sigmoid()
        )
    
    def encode(self, x):
        return self.encoder(x), None
    
    def decode(self, z):
        return self.decoder(z)
    
    def forward(self, x):
        z, _ = self.encode(x)
        return self.decode(z) 