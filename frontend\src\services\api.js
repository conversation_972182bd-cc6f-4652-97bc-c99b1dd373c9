/**
 * 统一API服务
 * 提供标准化的HTTP请求方法
 */

import axios from 'axios';
import { ElMessage } from 'element-plus';
import apiConfig from '@/config/api-config.js';

class ApiService {
  constructor() {
    this.client = axios.create({
      baseURL: '', // 初始为空，将动态设置
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    });

    this.setupInterceptors();
    this.initializeBaseUrl();
  }

  /**
   * 初始化 API 基础 URL
   */
  async initializeBaseUrl() {
    try {
      // 检测当前访问方式
      const { hostname } = window.location;

      if (hostname === '*************') {
        // 公网访问，使用相对路径让 Vite 代理处理
        this.client.defaults.baseURL = '';
        console.log(`[API] 公网访问，使用代理模式: ${hostname}`);
      } else if (hostname === 'localhost' || hostname === '127.0.0.1') {
        // localhost 访问，使用相对路径让 Vite 代理处理
        this.client.defaults.baseURL = '';
        console.log('[API] 本地访问，使用代理模式');
      } else {
        // 内网 IP 访问，直接连接后端
        const apiBaseUrl = await apiConfig.getApiBaseUrl();
        this.client.defaults.baseURL = apiBaseUrl;
        console.log(`[API] 内网访问，直连后端: ${apiBaseUrl}`);
      }
    } catch (error) {
      console.error('[API] 初始化基础URL失败:', error);
      // 使用默认配置
      this.client.defaults.baseURL = '';
    }
  }

  /**
   * 设置请求和响应拦截器
   */
  setupInterceptors() {
    // 请求拦截器
    this.client.interceptors.request.use(
      (config) => {
        // 添加认证token
        const token = localStorage.getItem('auth_token');
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        console.log(`[API] ${config.method?.toUpperCase()} ${config.url}`);
        return config;
      },
      (error) => {
        console.error('[API] 请求错误:', error);
        return Promise.reject(error);
      }
    );

    // 响应拦截器
    this.client.interceptors.response.use(
      (response) => {
        console.log(`[API] 响应成功: ${response.status}`);
        return response;
      },
      (error) => {
        console.error('[API] 响应错误:', error);
        
        // 统一错误处理
        if (error.response) {
          const { status, data } = error.response;
          
          switch (status) {
            case 401:
              ElMessage.error('认证失败，请重新登录');
              // 可以在这里处理登出逻辑
              break;
            case 403:
              ElMessage.error('权限不足');
              break;
            case 404:
              ElMessage.error('请求的资源不存在');
              break;
            case 500:
              ElMessage.error('服务器内部错误');
              break;
            default:
              ElMessage.error(data?.message || '请求失败');
          }
        } else if (error.request) {
          ElMessage.error('网络连接失败');
        } else {
          ElMessage.error('请求配置错误');
        }
        
        return Promise.reject(error);
      }
    );
  }

  /**
   * GET请求
   */
  async get(url, config = {}) {
    return this.client.get(url, config);
  }

  /**
   * POST请求
   */
  async post(url, data = {}, config = {}) {
    return this.client.post(url, data, config);
  }

  /**
   * PUT请求
   */
  async put(url, data = {}, config = {}) {
    return this.client.put(url, data, config);
  }

  /**
   * DELETE请求
   */
  async delete(url, config = {}) {
    return this.client.delete(url, config);
  }

  /**
   * PATCH请求
   */
  async patch(url, data = {}, config = {}) {
    return this.client.patch(url, data, config);
  }

  /**
   * 上传文件
   */
  async upload(url, formData, onUploadProgress = null) {
    return this.client.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress
    });
  }

  /**
   * 下载文件
   */
  async download(url, filename = null) {
    const response = await this.client.get(url, {
      responseType: 'blob'
    });

    // 创建下载链接
    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename || 'download';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(downloadUrl);

    return response;
  }
}

// 创建单例实例
const api = new ApiService();

export default api;
