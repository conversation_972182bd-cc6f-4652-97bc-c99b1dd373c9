"""
AI智能体业务服务
"""

import httpx
from typing import Dict, Any


class AiAgentService:
    """AI智能体服务类"""

    def __init__(self):
        self.ollama_base_url = "http://localhost:11434"
        # 使用您系统中可用的模型，按性能优先级排序
        self.available_models = [
            "qwen2.5:7b",                      # 中文友好对话模型（推荐）
            "mistral:7b",                      # Mistral对话模型
            "llama3.2:3b",                     # 轻量级对话模型
            "deepseek-ultra-fast:latest",      # DeepSeek 快速模型
            "deepseek-r1_8b_fast:latest"       # DeepSeek R1 快速模型
        ]
        self.default_model = "qwen2.5:7b"
        self._model_cache = None  # 缓存选择的模型
    
    async def chat_with_agent(self, agent: Dict[str, Any], message: str) -> str:
        """与智能体对话"""
        try:
            agent_type = agent.get("agent_type", "chat")
            agent_name = agent.get("name", "智能体")
            system_prompt = agent.get("system_prompt", "")

            # 获取智能体配置
            agent_config = agent.get('config', {}) if isinstance(agent.get('config'), dict) else {}
            model_config = agent_config.get('model', {})

            # 确定使用的模型
            selected_model = model_config.get('model_name', self.default_model)

            # 如果使用自定义训练的模型
            if model_config.get('use_custom_model') and model_config.get('custom_model_id'):
                selected_model = model_config.get('custom_model_id')
                print(f"[INFO] 使用自定义训练模型: {selected_model}")

            print(f"[DEBUG] 智能体 {agent_name} 使用模型: {selected_model}")

            # 如果配置中有自定义系统提示词，优先使用
            if model_config.get('system_prompt'):
                system_prompt = model_config.get('system_prompt')
                print(f"[DEBUG] 使用自定义系统提示词")

            # 构建系统提示词
            if not system_prompt:
                # 根据智能体类型生成默认系统提示词
                if agent_type == "translation":
                    system_prompt = f"你是{agent_name}，一个专业的翻译助手。请直接、简洁地帮助用户翻译。不要显示思考过程，直接给出翻译结果和简要说明。"
                elif agent_type == "writing":
                    system_prompt = f"你是{agent_name}，一个专业的写作助手。请直接提供写作建议和改进方案，保持回复简洁实用。"
                elif agent_type == "coding":
                    system_prompt = f"你是{agent_name}，一个专业的编程助手。请直接提供代码解决方案和技术建议，避免冗长的解释。"
                elif agent_type == "language_tutor":
                    system_prompt = f"""You are {agent_name}, a friendly and professional English tutor.

IMPORTANT INSTRUCTIONS:
- Respond DIRECTLY as the teacher, not as an AI analyzing the conversation
- Keep responses natural, conversational, and encouraging
- Ask follow-up questions to keep the conversation flowing
- Provide gentle corrections when needed
- Be supportive and patient
- When introducing yourself, simply say "Hi! I'm {agent_name}, your English tutor."

DO NOT:
- Analyze what the user said
- Explain your thought process
- Say things like "the user said..." or "I need to respond..."
- Be overly formal or robotic

Example interaction:
User: "Hi, my name is Pengwei"
You: "Nice to meet you, Pengwei! I'm {agent_name}. How are you doing today? What would you like to work on in our English lesson?"

Always respond as the teacher character directly."""
                else:
                    system_prompt = f"你是{agent_name}，一个智能助手。请友好、简洁地回答用户的问题，保持对话自然。"

            # 添加强化的禁止思考过程指令
            system_prompt += """

CRITICAL: You must respond DIRECTLY as the character. Never show analysis, thinking, or meta-commentary.

FORBIDDEN phrases:
- "Alright, so the user..."
- "I need to respond..."
- "The user said..."
- "I should..."
- Any analysis of what the user said

REQUIRED: Respond immediately as the character would speak in real conversation."""

            # 准备模型参数
            model_params = {
                'temperature': model_config.get('temperature', 0.7),
                'num_predict': model_config.get('max_tokens', 2048),
                'top_p': model_config.get('top_p', 0.9),
                'top_k': model_config.get('top_k', 40),
                'repeat_penalty': model_config.get('repeat_penalty', 1.1)
            }

            print(f"[DEBUG] 模型参数: {model_params}")

            # 调用 Ollama API
            response = await self._call_ollama(system_prompt, message, selected_model, model_params)
            return response

        except Exception as e:
            print(f"[ERROR] 智能体对话失败: {str(e)}")
            return f"抱歉，处理您的消息时出现了错误：{str(e)}"

    async def _get_best_model(self) -> str:
        """获取最佳可用模型（带缓存）"""
        # 如果已经缓存了模型，直接返回
        if self._model_cache:
            return self._model_cache

        try:
            async with httpx.AsyncClient(timeout=5.0) as client:  # 减少超时时间
                response = await client.get(f"{self.ollama_base_url}/api/tags")
                if response.status_code == 200:
                    models_data = response.json()
                    available_models = [model["name"] for model in models_data.get("models", [])]

                    # 按优先级选择模型（8B优先）
                    for model in self.available_models:
                        if model in available_models:
                            print(f"[DEBUG] 选择并缓存模型: {model}")
                            self._model_cache = model  # 缓存选择的模型
                            return model

                    # 如果没有找到预设模型，使用第一个可用的
                    if available_models:
                        print(f"[DEBUG] 使用第一个可用模型: {available_models[0]}")
                        self._model_cache = available_models[0]
                        return available_models[0]
        except Exception as e:
            print(f"[WARNING] 获取模型列表失败: {e}")

        # 回退到默认模型
        self._model_cache = self.default_model
        return self.default_model

    async def _call_ollama(self, system_prompt: str, user_message: str, model: str = None, model_params: dict = None) -> str:
        """调用 Ollama API"""
        try:
            # 使用传入的模型，如果没有则动态选择最佳模型
            if not model:
                model = await self._get_best_model()

            # 使用传入的模型参数，如果没有则使用默认参数
            if not model_params:
                model_params = {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "num_predict": 200,
                    "num_ctx": 4096,
                    "repeat_penalty": 1.05,
                    "top_k": 40
                }

            async with httpx.AsyncClient(timeout=30.0) as client:
                payload = {
                    "model": model,
                    "messages": [
                        {
                            "role": "system",
                            "content": system_prompt
                        },
                        {
                            "role": "user",
                            "content": user_message
                        }
                    ],
                    "stream": False,
                    "options": model_params
                }

                print(f"[DEBUG] 调用 Ollama API: {self.ollama_base_url}/api/chat")
                print(f"[DEBUG] 使用模型: {model}")
                print(f"[DEBUG] 系统提示: {system_prompt[:100]}...")
                print(f"[DEBUG] 用户消息: {user_message}")

                response = await client.post(
                    f"{self.ollama_base_url}/api/chat",
                    json=payload
                )

                if response.status_code == 200:
                    result = response.json()
                    print(f"[DEBUG] Ollama 完整响应: {result}")

                    assistant_message = result.get("message", {}).get("content", "")

                    if not assistant_message:
                        print(f"[WARNING] Ollama 返回空响应，完整结果: {result}")
                        return "Hello! I'm your language learning assistant. How can I help you today?"

                    # 清理响应：移除思考标签和不必要的内容
                    cleaned_message = self._clean_response(assistant_message)

                    print(f"[DEBUG] Ollama 原始响应长度: {len(assistant_message)}")
                    print(f"[DEBUG] Ollama 原始响应: {assistant_message[:200]}...")
                    print(f"[DEBUG] 清理后响应长度: {len(cleaned_message)}")
                    print(f"[DEBUG] 清理后响应: {cleaned_message[:200]}...")
                    return cleaned_message
                else:
                    print(f"[ERROR] Ollama API 错误: {response.status_code} - {response.text}")
                    return f"抱歉，AI服务暂时不可用。错误代码：{response.status_code}"

        except httpx.ConnectError:
            print(f"[ERROR] 无法连接到 Ollama 服务 ({self.ollama_base_url})")
            return "抱歉，AI服务暂时不可用。请确保 Ollama 服务正在运行。"
        except Exception as e:
            print(f"[ERROR] 调用 Ollama 失败: {str(e)}")
            return f"抱歉，AI服务出现错误：{str(e)}"

    def _clean_response(self, response: str) -> str:
        """清理AI响应，移除思考标签和不必要的内容"""
        if not response:
            return "Hello! I'm here to help you with your language learning. How can I assist you today?"

        import re

        # 移除各种思考标签及其内容
        response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL | re.IGNORECASE)
        response = re.sub(r'<thinking>.*?</thinking>', '', response, flags=re.DOTALL | re.IGNORECASE)
        response = re.sub(r'<thought>.*?</thought>', '', response, flags=re.DOTALL | re.IGNORECASE)

        # 移除其他XML/HTML标签，但保留内容
        response = re.sub(r'<[^>]+>', '', response)

        # 简化的思考过程移除：只移除明显的分析句子
        lines = response.split('\n')
        filtered_lines = []

        for line in lines:
            line_stripped = line.strip()
            line_lower = line_stripped.lower()

            # 跳过明显的思考分析句子
            if (line_lower.startswith('alright, so') or
                line_lower.startswith('okay, so') or
                line_lower.startswith('i need to respond') or
                line_lower.startswith('the user') or
                'user said' in line_lower or
                'user introduced' in line_lower or
                'i should' in line_lower and 'respond' in line_lower):
                continue

            # 保留其他内容
            if line_stripped:
                filtered_lines.append(line)

        response = '\n'.join(filtered_lines)

        # 清理多余的空白字符
        response = re.sub(r'\n\s*\n+', '\n\n', response)  # 多个空行变成两个
        response = re.sub(r'^\s+|\s+$', '', response)     # 移除首尾空白

        # 如果清理后响应太短或为空，提供默认回复
        if len(response.strip()) < 10:
            return "Hello! I'm here to help you with your language learning. How can I assist you today?"

        return response

    def get_agent_types(self):
        """获取智能体类型列表"""
        return [
            {"id": "chat", "name": "对话助手", "description": "通用对话智能体"},
            {"id": "translation", "name": "翻译助手", "description": "专业翻译智能体"},
            {"id": "writing", "name": "写作助手", "description": "文案写作智能体"},
            {"id": "coding", "name": "编程助手", "description": "代码编写智能体"},
            {"id": "learning", "name": "学习助手", "description": "教育学习智能体"}
        ]
    
    def validate_agent_config(self, agent_type: str, config: dict) -> bool:
        """验证智能体配置"""
        try:
            # 这里可以根据不同的智能体类型验证配置
            if agent_type == "translation":
                # 翻译助手的配置验证
                required_fields = ["source_languages", "target_languages"]
                return all(field in config for field in required_fields)
            elif agent_type == "writing":
                # 写作助手的配置验证
                required_fields = ["writing_style", "target_audience"]
                return all(field in config for field in required_fields)
            else:
                # 其他类型的基本验证
                return True
                
        except Exception:
            return False
