#!/usr/bin/env python3
"""
测试AnimateDiff - 更适合RTX 3060 Ti的视频生成模型
"""

import torch
import os
import time
from pathlib import Path

def test_animatediff_working():
    """测试AnimateDiff实际生成"""
    print("=== 测试AnimateDiff视频生成 ===")
    
    try:
        # 检查CUDA
        if not torch.cuda.is_available():
            print("❌ CUDA不可用")
            return False
        
        print(f"✅ CUDA可用: {torch.cuda.get_device_name(0)}")
        print(f"✅ GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
        
        # 清理GPU缓存
        torch.cuda.empty_cache()
        print("✅ GPU缓存已清理")
        
        # 导入AnimateDiff
        from diffusers import AnimateDiffPipeline, MotionAdapter, EulerDiscreteScheduler
        from diffusers.utils import export_to_gif
        print("✅ AnimateDiff导入成功")
        
        # 创建输出目录
        output_dir = Path("./animatediff_outputs")
        output_dir.mkdir(exist_ok=True)
        
        print("🔄 加载AnimateDiff模型...")
        
        # 加载motion adapter
        adapter = MotionAdapter.from_pretrained(
            "guoyww/animatediff-motion-adapter-v1-5-2", 
            torch_dtype=torch.float16
        )
        print("✅ Motion Adapter加载成功")
        
        # 创建pipeline
        model_id = "runwayml/stable-diffusion-v1-5"
        pipe = AnimateDiffPipeline.from_pretrained(
            model_id, 
            motion_adapter=adapter, 
            torch_dtype=torch.float16
        )
        
        # 使用更快的调度器
        scheduler = EulerDiscreteScheduler.from_pretrained(model_id, subfolder="scheduler")
        pipe.scheduler = scheduler
        
        # 启用内存优化
        pipe.enable_vae_slicing()
        pipe.enable_model_cpu_offload()
        
        print("✅ Pipeline创建成功")
        
        # 生成测试视频
        prompts = [
            "一只可爱的小猫在花园里玩耍，高质量，细节丰富",
            "a cute cat playing in a garden, high quality, detailed",
            "beautiful sunset over mountains, cinematic"
        ]
        
        for i, prompt in enumerate(prompts):
            print(f"\n🎬 生成视频 {i+1}/3: {prompt}")
            
            start_time = time.time()
            
            # 生成视频
            output = pipe(
                prompt=prompt,
                num_frames=16,
                guidance_scale=7.5,
                num_inference_steps=25,
                generator=torch.Generator("cpu").manual_seed(42 + i),
            )
            
            end_time = time.time()
            
            frames = output.frames[0]
            
            # 保存为GIF
            output_path = output_dir / f"animatediff_test_{i+1}.gif"
            export_to_gif(frames, str(output_path))
            
            print(f"✅ 视频 {i+1} 生成成功: {output_path}")
            print(f"⏱️  生成时间: {end_time - start_time:.2f}秒")
            print(f"📊 帧数: {len(frames)}")
            
            # 检查文件大小
            file_size = os.path.getsize(output_path)
            print(f"📊 文件大小: {file_size / 1024:.1f} KB")
            
            # 清理GPU缓存
            torch.cuda.empty_cache()
        
        print(f"\n🎉 所有视频生成完成！")
        print(f"📁 输出目录: {output_dir}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        print("💡 请安装: pip install diffusers transformers accelerate")
        return False
    except Exception as e:
        print(f"❌ AnimateDiff测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_animatediff_quick():
    """快速AnimateDiff测试"""
    print("\n=== 快速AnimateDiff测试 ===")
    
    try:
        from diffusers import AnimateDiffPipeline, MotionAdapter
        import torch
        
        # 清理GPU缓存
        torch.cuda.empty_cache()
        
        print("🔄 快速加载模型...")
        
        # 使用更小的模型
        adapter = MotionAdapter.from_pretrained(
            "guoyww/animatediff-motion-adapter-v1-5-2", 
            torch_dtype=torch.float16
        )
        
        pipe = AnimateDiffPipeline.from_pretrained(
            "runwayml/stable-diffusion-v1-5", 
            motion_adapter=adapter, 
            torch_dtype=torch.float16
        )
        
        # 最大内存优化
        pipe.enable_vae_slicing()
        pipe.enable_model_cpu_offload()
        pipe.enable_attention_slicing(1)
        
        print("✅ 快速模型加载完成")
        
        # 快速生成
        prompt = "cat walking, simple animation"
        print(f"🎬 快速生成: {prompt}")
        
        start_time = time.time()
        
        output = pipe(
            prompt=prompt,
            num_frames=8,  # 更少帧数
            guidance_scale=6.0,  # 更低引导
            num_inference_steps=15,  # 更少步数
            generator=torch.Generator("cpu").manual_seed(42),
        )
        
        end_time = time.time()
        
        # 保存
        from diffusers.utils import export_to_gif
        output_path = "animatediff_quick.gif"
        export_to_gif(output.frames[0], output_path)
        
        print(f"✅ 快速生成完成: {output_path}")
        print(f"⏱️  生成时间: {end_time - start_time:.2f}秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始AnimateDiff测试")
    
    # 先尝试快速测试
    print("选择测试模式:")
    print("1. 完整测试 (3个视频, 16帧, 25步)")
    print("2. 快速测试 (1个视频, 8帧, 15步)")
    
    # 默认快速测试
    mode = "2"
    
    if mode == "1":
        print("\n🎯 运行完整测试...")
        success = test_animatediff_working()
    else:
        print("\n⚡ 运行快速测试...")
        success = test_animatediff_quick()
    
    if success:
        print("\n🎉 AnimateDiff测试成功！")
        print("💡 AnimateDiff比Wanx 2.1更适合您的硬件配置")
    else:
        print("\n❌ AnimateDiff测试失败")
    
    print("\n=== 测试完成 ===")
