#!/usr/bin/env python3
"""
测试API接口
"""

import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_health():
    """测试健康检查接口"""
    print("=== 测试健康检查接口 ===")
    try:
        response = requests.get(f"{BASE_URL}/health")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 健康检查成功")
            print(f"状态: {data.get('status')}")
            print(f"消息: {data.get('message')}")
            print(f"可用引擎数: {len(data.get('engines', {}))}")
            return True
        else:
            print(f"❌ 健康检查失败: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_engine_status():
    """测试引擎状态接口"""
    print("\n=== 测试引擎状态接口 ===")
    try:
        # 尝试不同的路径
        paths = [
            "/api/v1/engines/status",
            "/api/v1/video-generation/status",
            "/api/v1/video-generation/engines"
        ]

        for path in paths:
            try:
                response = requests.get(f"{BASE_URL}{path}")
                print(f"尝试路径 {path}: {response.status_code}")
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 引擎状态获取成功")
                    print(f"响应数据: {data}")
                    return True
            except Exception as e:
                print(f"路径 {path} 失败: {e}")
                continue

        print("❌ 所有路径都失败了")
        return False
    except Exception as e:
        print(f"❌ 引擎状态获取异常: {e}")
        return False

def test_video_generation():
    """测试视频生成接口"""
    print("\n=== 测试视频生成接口 ===")
    try:
        payload = {
            "prompt": "一只可爱的小猫在花园里玩耍",
            "duration": 3,
            "width": 512,
            "height": 512
        }

        # 尝试不同的路径
        paths = [
            "/api/v1/video/generate",
            "/api/v1/video-generation/generate",
            "/api/v1/video-generation/text-to-video"
        ]

        for path in paths:
            try:
                print(f"尝试路径 {path}")
                print(f"发送请求: {payload}")
                response = requests.post(
                    f"{BASE_URL}{path}",
                    json=payload,
                    timeout=30
                )

                print(f"状态码: {response.status_code}")
                if response.status_code == 200:
                    data = response.json()
                    print(f"✅ 视频生成请求成功")
                    print(f"响应数据: {data}")
                    return True
                elif response.status_code == 404:
                    print(f"路径 {path} 不存在，尝试下一个")
                    continue
                else:
                    print(f"❌ 视频生成失败: {response.text}")
                    return False
            except Exception as e:
                print(f"路径 {path} 异常: {e}")
                continue

        print("❌ 所有路径都失败了")
        return False
    except Exception as e:
        print(f"❌ 视频生成异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API测试")
    
    # 等待服务启动
    print("等待服务启动...")
    time.sleep(2)
    
    # 测试健康检查
    health_ok = test_health()
    
    # 测试引擎状态
    engine_ok = test_engine_status()
    
    # 测试视频生成
    video_ok = test_video_generation()
    
    # 总结
    print("\n=== 测试总结 ===")
    print(f"健康检查: {'✅' if health_ok else '❌'}")
    print(f"引擎状态: {'✅' if engine_ok else '❌'}")
    print(f"视频生成: {'✅' if video_ok else '❌'}")
    
    if health_ok and engine_ok and video_ok:
        print("🎉 所有测试通过！系统运行正常！")
    else:
        print("⚠️  部分测试失败，请检查系统状态")

if __name__ == "__main__":
    main()
