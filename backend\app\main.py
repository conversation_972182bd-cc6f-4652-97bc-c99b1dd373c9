#!/usr/bin/env python3
"""
AI系统后端主应用
"""

from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import Optional, Dict, Any
import logging
import traceback

# 导入核心模块
from app.core.config import settings
from app.core.database import get_db
from app.tasks.video_generation_manager import video_manager

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="AI系统后端",
    description="智能视频生成和数字人系统",
    version="2.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 在生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 请求模型
class VideoGenerationRequest(BaseModel):
    prompt: str
    duration: Optional[int] = 5
    width: Optional[int] = 512
    height: Optional[int] = 512
    engine: Optional[str] = None

class HealthCheckResponse(BaseModel):
    status: str
    message: str
    engines: Dict[str, Any]

# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "AI系统后端服务",
        "version": "2.0.0",
        "status": "running"
    }

# 健康检查
@app.get("/health", response_model=HealthCheckResponse)
async def health_check():
    """健康检查接口"""
    try:
        # 检查视频生成引擎状态
        engine_status = video_manager.get_engine_status()
        best_engine = video_manager.get_best_engine()
        
        return HealthCheckResponse(
            status="healthy",
            message=f"系统正常运行，最佳引擎: {engine_status.get(best_engine, {}).get('name', '无')}",
            engines=engine_status
        )
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return HealthCheckResponse(
            status="unhealthy",
            message=f"系统异常: {str(e)}",
            engines={}
        )

# 视频生成接口
@app.post("/api/v1/video/generate")
async def generate_video(request: VideoGenerationRequest):
    """视频生成接口"""
    try:
        logger.info(f"收到视频生成请求: {request.prompt}")
        
        # 检查引擎状态
        if request.engine:
            engine_status = video_manager.get_engine_status()
            if request.engine not in engine_status or engine_status[request.engine]['status'] != 'available':
                raise HTTPException(
                    status_code=400, 
                    detail=f"指定的引擎 '{request.engine}' 不可用"
                )
        
        # 获取最佳引擎
        best_engine = video_manager.get_best_engine(preferred_engine=request.engine)
        if not best_engine:
            raise HTTPException(
                status_code=503, 
                detail="当前没有可用的视频生成引擎"
            )
        
        # 生成视频
        result = await video_manager.generate_video_async(
            prompt=request.prompt,
            duration=request.duration,
            width=request.width,
            height=request.height,
            engine=best_engine
        )
        
        return {
            "status": "success",
            "message": "视频生成成功",
            "engine": best_engine,
            "result": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"视频生成失败: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(
            status_code=500,
            detail=f"视频生成失败: {str(e)}"
        )

# 引擎状态接口
@app.get("/api/v1/engines/status")
async def get_engine_status():
    """获取引擎状态"""
    try:
        status = video_manager.get_engine_status()
        best_engine = video_manager.get_best_engine()
        
        return {
            "status": "success",
            "engines": status,
            "best_engine": best_engine,
            "best_engine_info": status.get(best_engine, {}) if best_engine else None
        }
    except Exception as e:
        logger.error(f"获取引擎状态失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取引擎状态失败: {str(e)}"
        )

# 异常处理
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """全局异常处理"""
    logger.error(f"未处理的异常: {exc}")
    logger.error(traceback.format_exc())
    return JSONResponse(
        status_code=500,
        content={
            "status": "error",
            "message": "服务器内部错误",
            "detail": str(exc)
        }
    )

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
