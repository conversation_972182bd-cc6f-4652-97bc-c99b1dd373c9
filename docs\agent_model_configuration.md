# 智能体模型配置系统完整实现

## 🎯 解决的核心问题

您提出的关键问题：
**"那我智能体用的是哪个模型，在哪里可以设置？"**

## ✅ 完整的智能体模型配置系统

### 1. 🔧 后台模型配置API

#### 获取可用模型列表
```python
@router.get("/available-models")
async def get_available_models():
    """获取可用的模型列表"""
    # 获取本地Ollama模型
    models = ollama_service.list_models()
    
    # 格式化模型信息
    formatted_models = []
    for model in models:
        model_info = {
            "name": model.get("name", ""),
            "display_name": model.get("name", "").replace(":latest", ""),
            "description": get_model_description(model.get("name", "")),
            "recommended": model.get("name", "") in ["qwen2.5:7b", "mistral:7b"],
            "language_support": get_model_language_support(model.get("name", "")),
            "use_cases": get_model_use_cases(model.get("name", ""))
        }
        formatted_models.append(model_info)
```

#### 智能体模型配置管理
```python
@router.put("/{agent_id}/model-config")
async def update_agent_model_config(agent_id: str, config: dict):
    """更新智能体的模型配置"""
    model_config = {
        "model_name": config.get("model_name", "qwen2.5:7b"),
        "temperature": config.get("temperature", 0.7),
        "max_tokens": config.get("max_tokens", 2048),
        "top_p": config.get("top_p", 0.9),
        "top_k": config.get("top_k", 40),
        "repeat_penalty": config.get("repeat_penalty", 1.1),
        "system_prompt": config.get("system_prompt", ""),
        "use_custom_model": config.get("use_custom_model", False),
        "custom_model_id": config.get("custom_model_id", "")
    }
    
    # 保存到数据库
    current_config["model"] = model_config
    db_manager.execute_query(
        "UPDATE agents SET config = ? WHERE id = ?",
        (json.dumps(current_config), agent_id)
    )

@router.get("/{agent_id}/model-config")
async def get_agent_model_config(agent_id: str):
    """获取智能体的模型配置"""
    # 从数据库读取配置
    config = agent_data.get("config", {})
    model_config = config.get("model", default_config)
    return model_config
```

### 2. 🤖 智能体服务模型调用

#### 动态模型选择
```python
async def chat_with_agent(self, agent: Dict[str, Any], message: str) -> str:
    # 获取智能体配置
    agent_config = agent.get('config', {})
    model_config = agent_config.get('model', {})
    
    # 确定使用的模型
    selected_model = model_config.get('model_name', self.default_model)
    
    # 如果使用自定义训练的模型
    if model_config.get('use_custom_model') and model_config.get('custom_model_id'):
        selected_model = model_config.get('custom_model_id')
        print(f"[INFO] 使用自定义训练模型: {selected_model}")
    
    # 准备模型参数
    model_params = {
        'temperature': model_config.get('temperature', 0.7),
        'num_predict': model_config.get('max_tokens', 2048),
        'top_p': model_config.get('top_p', 0.9),
        'top_k': model_config.get('top_k', 40),
        'repeat_penalty': model_config.get('repeat_penalty', 1.1)
    }
    
    # 调用 Ollama API
    response = await self._call_ollama(system_prompt, message, selected_model, model_params)
```

### 3. 🎨 前端模型配置界面

#### 模型配置对话框组件
```vue
<template>
  <el-dialog v-model="visible" title="模型配置" width="800px">
    <!-- 基础模型选择 -->
    <div class="config-section">
      <h3>🤖 基础模型选择</h3>
      <el-radio-group v-model="config.use_custom_model">
        <el-radio :label="false">使用标准模型</el-radio>
        <el-radio :label="true">使用自定义训练模型</el-radio>
      </el-radio-group>
      
      <!-- 标准模型选择 -->
      <div v-if="!config.use_custom_model" class="standard-models">
        <div class="models-grid">
          <div 
            v-for="model in availableModels" 
            :key="model.name"
            :class="['model-card', { selected: config.model_name === model.name }]"
            @click="selectModel(model)"
          >
            <div class="model-header">
              <span class="model-name">{{ model.display_name }}</span>
              <span v-if="model.recommended" class="recommended-badge">推荐</span>
            </div>
            <div class="model-info">
              <p class="model-desc">{{ model.description }}</p>
              <div class="model-details">
                <span>大小: {{ formatSize(model.size) }}</span>
                <span>语言: {{ model.language_support.join(', ') }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 自定义模型选择 -->
      <div v-else class="custom-models">
        <el-select v-model="config.custom_model_id" placeholder="选择自定义训练的模型">
          <el-option
            v-for="model in customModels"
            :key="model.id"
            :label="model.name"
            :value="model.id"
          />
        </el-select>
      </div>
    </div>
    
    <!-- 模型参数配置 -->
    <div class="config-section">
      <h3>⚙️ 模型参数</h3>
      <el-form :model="config">
        <el-form-item label="创造性">
          <el-slider v-model="config.temperature" :min="0" :max="2" :step="0.1" />
        </el-form-item>
        <el-form-item label="最大长度">
          <el-slider v-model="config.max_tokens" :min="100" :max="4000" :step="100" />
        </el-form-item>
        <el-form-item label="核心采样">
          <el-slider v-model="config.top_p" :min="0.1" :max="1" :step="0.1" />
        </el-form-item>
      </el-form>
    </div>
    
    <!-- 自定义系统提示词 -->
    <div class="config-section">
      <h3>💬 自定义系统提示词</h3>
      <el-input
        v-model="config.system_prompt"
        type="textarea"
        :rows="6"
        placeholder="输入自定义的系统提示词"
      />
    </div>
  </el-dialog>
</template>
```

#### 智能体详情页面集成
```vue
<!-- 操作按钮 -->
<div class="action-buttons">
  <button @click="tryAgent" class="btn-secondary">🎯 试用智能体</button>
  <button @click="openModelConfig" class="btn-edit">🤖 模型配置</button>
  <button @click="useAgent" class="btn-primary">🚀 开始使用</button>
</div>

<!-- 模型配置对话框 -->
<ModelConfigDialog 
  v-model="showModelConfig"
  :agent-id="agentId"
  @config-updated="onModelConfigUpdated"
/>
```

### 4. 🔄 完整的配置流程

#### 1. 访问智能体详情页面
```
http://100.76.39.231:9000/agent-detail?id=your_agent_id
```

#### 2. 点击"🤖 模型配置"按钮
- 打开模型配置对话框
- 自动加载当前智能体的模型配置
- 显示可用的标准模型和自定义训练模型

#### 3. 选择模型类型
**标准模型选择**：
- qwen2.5:7b (推荐) - 中文理解能力强
- mistral:7b - 多语言支持
- llama3.2:3b - 轻量级，响应快速

**自定义训练模型**：
- 李老师_teacher_20250731 - 专业英语教师
- 其他训练完成的专业模型

#### 4. 调整模型参数
- **创造性 (Temperature)**: 0-2，控制回答的创造性
- **最大长度 (Max Tokens)**: 100-4000，控制回答长度
- **核心采样 (Top P)**: 0.1-1.0，控制词汇多样性
- **重复惩罚 (Repeat Penalty)**: 1.0-2.0，避免重复

#### 5. 自定义系统提示词
- 可以覆盖智能体的默认行为
- 支持专业化定制
- 留空则使用智能体默认设置

#### 6. 保存配置
- 配置立即生效
- 下次对话使用新的模型和参数

### 5. 🎯 模型使用优先级

#### 配置优先级
1. **自定义训练模型** (最高优先级)
   - 如果启用 `use_custom_model` 且设置了 `custom_model_id`
   - 使用训练好的专业模型

2. **指定标准模型**
   - 用户在配置中选择的标准模型
   - 如：qwen2.5:7b, mistral:7b

3. **系统默认模型** (最低优先级)
   - 如果没有配置，使用系统默认的 qwen2.5:7b

#### 参数使用优先级
1. **用户自定义参数** - 在模型配置中设置的参数
2. **智能体默认参数** - 智能体类型的默认参数
3. **系统默认参数** - 全局默认参数

### 6. 📊 配置存储结构

#### 数据库存储
```json
{
  "id": "agent_123",
  "name": "我的英语老师",
  "agent_type": "language_tutor",
  "config": {
    "model": {
      "model_name": "qwen2.5:7b",
      "temperature": 0.7,
      "max_tokens": 2048,
      "top_p": 0.9,
      "top_k": 40,
      "repeat_penalty": 1.1,
      "system_prompt": "你是一位专业的英语教师...",
      "use_custom_model": true,
      "custom_model_id": "li_teacher_teacher_20250731_173637"
    }
  }
}
```

### 7. 🚀 使用指南

#### 如何为智能体配置模型

1. **进入智能体详情页面**：
   - 在智能体市场中点击智能体
   - 或直接访问：`/agent-detail?id=智能体ID`

2. **打开模型配置**：
   - 点击"🤖 模型配置"按钮
   - 系统会加载当前配置

3. **选择模型**：
   - **使用标准模型**：从可用的Ollama模型中选择
   - **使用自定义模型**：选择您训练的专业模型

4. **调整参数**：
   - 根据需要调整创造性、回答长度等参数
   - 可以添加自定义系统提示词

5. **保存并测试**：
   - 点击"保存配置"
   - 立即生效，可以开始对话测试

#### 推荐配置

**中文对话智能体**：
- 模型：qwen2.5:7b
- 创造性：0.7
- 最大长度：2048

**专业咨询智能体**：
- 模型：自定义训练的专业模型
- 创造性：0.5 (更严谨)
- 最大长度：1500

**创意写作智能体**：
- 模型：mistral:7b
- 创造性：1.2 (更有创意)
- 最大长度：3000

## 🎉 功能总结

### ✅ 现在您可以：

1. **🔍 查看智能体使用的模型**：
   - 在智能体详情页面点击"模型配置"
   - 查看当前使用的模型和参数

2. **⚙️ 灵活配置模型**：
   - 选择标准Ollama模型
   - 使用自定义训练的专业模型
   - 调整模型参数和行为

3. **🎯 专业化定制**：
   - 为不同智能体配置不同模型
   - 英语老师用专业训练模型
   - 通用助手用标准模型

4. **📊 实时生效**：
   - 配置保存后立即生效
   - 下次对话使用新配置
   - 支持随时调整优化

### 🎯 核心优势

- **模型透明化**：清楚知道智能体使用哪个模型
- **配置灵活性**：支持标准模型和自定义模型
- **参数可调节**：精细控制模型行为
- **专业化支持**：训练的专业模型可直接应用
- **实时生效**：配置即时生效，无需重启

现在您完全掌握了智能体的模型配置！每个智能体都可以独立配置使用的模型和参数，实现真正的个性化AI服务。🎊
