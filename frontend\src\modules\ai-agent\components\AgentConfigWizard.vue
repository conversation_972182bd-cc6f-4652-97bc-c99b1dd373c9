<template>
  <div class="agent-config-wizard">
    <!-- 步骤指示器 -->
    <div class="wizard-steps">
      <div 
        v-for="(step, index) in steps" 
        :key="step.id"
        :class="['step-item', { 
          active: currentStep === index, 
          completed: index < currentStep 
        }]"
        @click="goToStep(index)"
      >
        <div class="step-number">{{ index + 1 }}</div>
        <div class="step-info">
          <div class="step-title">{{ step.title }}</div>
          <div class="step-desc">{{ step.description }}</div>
        </div>
      </div>
    </div>

    <!-- 步骤内容 -->
    <div class="wizard-content">
      <!-- 步骤1: 选择智能体类型 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="step-header">
          <h2>🎯 选择您的智能体类型</h2>
          <p>根据您的需求选择最适合的智能体类型，我们会自动配置相应的能力</p>
        </div>

        <div class="agent-types-grid">
          <div 
            v-for="(template, key) in agentTemplates" 
            :key="key"
            :class="['agent-type-card', { selected: selectedTemplate === key }]"
            @click="selectTemplate(key)"
          >
            <div class="card-icon">{{ template.icon }}</div>
            <div class="card-title">{{ template.name }}</div>
            <div class="card-description">{{ template.description }}</div>
            <div class="card-features">
              <div class="feature-tag" v-for="feature in template.features" :key="feature">
                {{ feature }}
              </div>
            </div>
            <div class="card-use-cases">
              <div class="use-case-title">适用场景：</div>
              <ul>
                <li v-for="useCase in template.useCases" :key="useCase">{{ useCase }}</li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤2: 基本信息 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="step-header">
          <h2>📝 设置基本信息</h2>
          <p>为您的智能体设置名称和描述</p>
        </div>

        <div class="form-section">
          <div class="form-group">
            <label class="form-label">
              <span class="label-text">智能体名称</span>
              <span class="label-required">*</span>
            </label>
            <input 
              v-model="agentConfig.name" 
              type="text" 
              class="form-input"
              placeholder="例如：小明老师、客服小助手、数据分析师小王"
            />
            <div class="form-hint">给您的智能体起一个友好的名字</div>
          </div>

          <div class="form-group">
            <label class="form-label">
              <span class="label-text">功能描述</span>
            </label>
            <textarea 
              v-model="agentConfig.description" 
              class="form-textarea"
              placeholder="描述这个智能体的主要功能和用途，例如：专业的数学教师，擅长解答学生疑问，提供个性化学习指导"
              rows="4"
            ></textarea>
            <div class="form-hint">简单描述智能体的主要功能，帮助用户了解它能做什么</div>
          </div>

          <div class="form-group">
            <label class="form-label">
              <span class="label-text">个性设置</span>
            </label>
            <div class="personality-options">
              <div 
                v-for="personality in personalityOptions" 
                :key="personality.id"
                :class="['personality-card', { selected: agentConfig.personality === personality.id }]"
                @click="agentConfig.personality = personality.id"
              >
                <div class="personality-icon">{{ personality.icon }}</div>
                <div class="personality-name">{{ personality.name }}</div>
                <div class="personality-desc">{{ personality.description }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤3: 能力配置 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="step-header">
          <h2>🛠️ 配置智能体能力</h2>
          <p>选择您的智能体需要具备的技能，我们已根据类型预选了推荐技能</p>
        </div>

        <div class="capabilities-section">
          <div class="capability-category" v-for="category in capabilityCategories" :key="category.id">
            <div class="category-header">
              <div class="category-icon">{{ category.icon }}</div>
              <div class="category-info">
                <h3>{{ category.name }}</h3>
                <p>{{ category.description }}</p>
              </div>
            </div>

            <div class="capability-grid">
              <div 
                v-for="capability in category.capabilities" 
                :key="capability.id"
                :class="['capability-card', { 
                  selected: isCapabilitySelected(capability.id),
                  recommended: capability.recommended 
                }]"
                @click="toggleCapability(capability.id)"
              >
                <div class="capability-header">
                  <div class="capability-icon">{{ capability.icon }}</div>
                  <div class="capability-name">{{ capability.name }}</div>
                  <div v-if="capability.recommended" class="recommended-badge">推荐</div>
                </div>
                <div class="capability-desc">{{ capability.description }}</div>
                <div class="capability-example">
                  <strong>示例：</strong>{{ capability.example }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤4: 高级设置 -->
      <div v-if="currentStep === 3" class="step-content">
        <div class="step-header">
          <h2>⚙️ 高级设置</h2>
          <p>配置记忆能力和其他高级功能（可选）</p>
        </div>

        <div class="advanced-settings">
          <div class="setting-group">
            <div class="setting-header">
              <h3>🧠 记忆能力</h3>
              <p>控制智能体如何记住和使用历史信息</p>
            </div>
            
            <div class="memory-options">
              <div 
                v-for="memory in memoryOptions" 
                :key="memory.id"
                :class="['memory-card', { selected: isMemorySelected(memory.id) }]"
                @click="toggleMemory(memory.id)"
              >
                <div class="memory-icon">{{ memory.icon }}</div>
                <div class="memory-info">
                  <div class="memory-name">{{ memory.name }}</div>
                  <div class="memory-desc">{{ memory.description }}</div>
                  <div class="memory-example">{{ memory.example }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="setting-group">
            <div class="setting-header">
              <h3>🔄 工作流程</h3>
              <p>是否启用复杂任务的自动化流程</p>
            </div>
            
            <div class="workflow-toggle">
              <label class="toggle-switch">
                <input 
                  type="checkbox" 
                  v-model="agentConfig.workflow_enabled"
                >
                <span class="toggle-slider"></span>
              </label>
              <div class="toggle-info">
                <div class="toggle-title">启用工作流</div>
                <div class="toggle-desc">
                  允许智能体执行多步骤的复杂任务，如"分析数据 → 生成报告 → 发送邮件"
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 步骤5: 预览和确认 -->
      <div v-if="currentStep === 4" class="step-content">
        <div class="step-header">
          <h2>👀 预览配置</h2>
          <p>确认您的智能体配置，一切准备就绪后即可创建</p>
        </div>

        <div class="config-preview">
          <div class="preview-section">
            <h3>基本信息</h3>
            <div class="preview-item">
              <span class="item-label">名称：</span>
              <span class="item-value">{{ agentConfig.name || '未设置' }}</span>
            </div>
            <div class="preview-item">
              <span class="item-label">类型：</span>
              <span class="item-value">{{ getSelectedTemplateName() }}</span>
            </div>
            <div class="preview-item">
              <span class="item-label">描述：</span>
              <span class="item-value">{{ agentConfig.description || '未设置' }}</span>
            </div>
          </div>

          <div class="preview-section">
            <h3>已选择的能力</h3>
            <div class="selected-capabilities">
              <div 
                v-for="capability in getSelectedCapabilities()" 
                :key="capability.id"
                class="selected-capability"
              >
                <span class="capability-icon">{{ capability.icon }}</span>
                <span class="capability-name">{{ capability.name }}</span>
              </div>
            </div>
          </div>

          <div class="preview-section">
            <h3>记忆设置</h3>
            <div class="selected-memories">
              <div 
                v-for="memory in getSelectedMemories()" 
                :key="memory.id"
                class="selected-memory"
              >
                <span class="memory-icon">{{ memory.icon }}</span>
                <span class="memory-name">{{ memory.name }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 导航按钮 -->
    <div class="wizard-navigation">
      <button 
        v-if="currentStep > 0" 
        @click="previousStep" 
        class="nav-btn nav-btn-secondary"
      >
        ← 上一步
      </button>
      
      <div class="nav-spacer"></div>
      
      <button 
        v-if="currentStep < steps.length - 1" 
        @click="nextStep" 
        class="nav-btn nav-btn-primary"
        :disabled="!canProceed()"
      >
        下一步 →
      </button>
      
      <button 
        v-if="currentStep === steps.length - 1" 
        @click="createAgent" 
        class="nav-btn nav-btn-success"
        :disabled="!canCreate()"
      >
        🚀 创建智能体
      </button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import trueAgentService from '../services/trueAgentService.js'

export default {
  name: 'AgentConfigWizard',
  emits: ['agent-created'],
  
  setup(props, { emit }) {
    const currentStep = ref(0)
    const selectedTemplate = ref('')
    
    // 智能体配置
    const agentConfig = reactive({
      name: '',
      description: '',
      personality: 'friendly',
      tools: [],
      memory_types: [],
      workflow_enabled: false
    })

    // 向导步骤
    const steps = [
      { id: 'type', title: '选择类型', description: '确定智能体用途' },
      { id: 'basic', title: '基本信息', description: '设置名称描述' },
      { id: 'capabilities', title: '配置能力', description: '选择所需技能' },
      { id: 'advanced', title: '高级设置', description: '记忆和工作流' },
      { id: 'preview', title: '预览确认', description: '检查并创建' }
    ]

    // 智能体类型模板
    const agentTemplates = {
      teacher: {
        name: '教师助手',
        icon: '👨‍🏫',
        description: '专业的教育助手，擅长教学和答疑',
        features: ['耐心教学', '个性化指导', '知识问答'],
        useCases: ['在线教育', '作业辅导', '知识答疑', '学习规划'],
        tools: ['web_search', 'document_parsing', 'text_analysis'],
        memory_types: ['short_term', 'long_term', 'semantic']
      },
      customer_service: {
        name: '客服助手',
        icon: '🎧',
        description: '友好的客户服务助手，提供专业支持',
        features: ['24小时服务', '多语言支持', '问题解决'],
        useCases: ['在线客服', '售后支持', '产品咨询', 'FAQ回答'],
        tools: ['web_search', 'database_query', 'email_send'],
        memory_types: ['short_term', 'episodic']
      },
      data_analyst: {
        name: '数据分析师',
        icon: '📊',
        description: '专业的数据分析助手，擅长数据处理和可视化',
        features: ['数据处理', '图表生成', '趋势分析'],
        useCases: ['业务分析', '报告生成', '数据可视化', '趋势预测'],
        tools: ['data_analysis', 'code_execution', 'file_operation'],
        memory_types: ['short_term', 'procedural']
      },
      creative_writer: {
        name: '创意写手',
        icon: '✍️',
        description: '富有创意的写作助手，擅长各种文体创作',
        features: ['创意写作', '文案策划', '内容优化'],
        useCases: ['文章写作', '广告文案', '创意策划', '内容编辑'],
        tools: ['web_search', 'text_analysis', 'translation'],
        memory_types: ['short_term', 'semantic', 'episodic']
      }
    }

    // 个性选项
    const personalityOptions = [
      { id: 'friendly', name: '友好亲切', icon: '😊', description: '温和友善，平易近人' },
      { id: 'professional', name: '专业严谨', icon: '👔', description: '正式专业，逻辑清晰' },
      { id: 'humorous', name: '幽默风趣', icon: '😄', description: '轻松幽默，活泼有趣' },
      { id: 'patient', name: '耐心细致', icon: '🤗', description: '耐心教导，细致入微' }
    ]

    // 能力分类
    const capabilityCategories = [
      {
        id: 'basic',
        name: '基础能力',
        icon: '🔍',
        description: '智能体的基本技能',
        capabilities: [
          {
            id: 'web_search',
            name: '网络搜索',
            icon: '🔍',
            description: '搜索互联网信息',
            example: '查找最新新闻、资料',
            recommended: true
          },
          {
            id: 'text_analysis',
            name: '文本分析',
            icon: '📝',
            description: '分析和理解文本内容',
            example: '情感分析、关键词提取',
            recommended: true
          }
        ]
      },
      {
        id: 'advanced',
        name: '高级能力',
        icon: '⚡',
        description: '更强大的专业技能',
        capabilities: [
          {
            id: 'code_execution',
            name: '代码执行',
            icon: '💻',
            description: '执行编程代码',
            example: '数据处理、自动化脚本',
            recommended: false
          },
          {
            id: 'data_analysis',
            name: '数据分析',
            icon: '📊',
            description: '分析和处理数据',
            example: '生成图表、统计分析',
            recommended: false
          }
        ]
      }
    ]

    // 记忆选项
    const memoryOptions = [
      {
        id: 'short_term',
        name: '短期记忆',
        icon: '🧠',
        description: '记住当前对话的内容',
        example: '在一次对话中记住之前说过的话'
      },
      {
        id: 'long_term',
        name: '长期记忆',
        icon: '📚',
        description: '跨对话记住重要信息',
        example: '记住用户的偏好和历史互动'
      },
      {
        id: 'semantic',
        name: '知识记忆',
        icon: '🎓',
        description: '记住学到的知识和概念',
        example: '学习新知识并在后续对话中使用'
      }
    ]

    // 方法
    const selectTemplate = (templateKey) => {
      selectedTemplate.value = templateKey
      const template = agentTemplates[templateKey]
      if (template) {
        agentConfig.tools = [...template.tools]
        agentConfig.memory_types = [...template.memory_types]
      }
    }

    const goToStep = (stepIndex) => {
      if (stepIndex <= currentStep.value || canProceed()) {
        currentStep.value = stepIndex
      }
    }

    const nextStep = () => {
      if (canProceed() && currentStep.value < steps.length - 1) {
        currentStep.value++
      }
    }

    const previousStep = () => {
      if (currentStep.value > 0) {
        currentStep.value--
      }
    }

    const canProceed = () => {
      switch (currentStep.value) {
        case 0: return selectedTemplate.value !== ''
        case 1: return agentConfig.name.trim() !== ''
        case 2: return agentConfig.tools.length > 0
        case 3: return true
        default: return true
      }
    }

    const canCreate = () => {
      return agentConfig.name.trim() !== '' && selectedTemplate.value !== ''
    }

    const isCapabilitySelected = (capabilityId) => {
      return agentConfig.tools.includes(capabilityId)
    }

    const toggleCapability = (capabilityId) => {
      const index = agentConfig.tools.indexOf(capabilityId)
      if (index > -1) {
        agentConfig.tools.splice(index, 1)
      } else {
        agentConfig.tools.push(capabilityId)
      }
    }

    const isMemorySelected = (memoryId) => {
      return agentConfig.memory_types.includes(memoryId)
    }

    const toggleMemory = (memoryId) => {
      const index = agentConfig.memory_types.indexOf(memoryId)
      if (index > -1) {
        agentConfig.memory_types.splice(index, 1)
      } else {
        agentConfig.memory_types.push(memoryId)
      }
    }

    const getSelectedTemplateName = () => {
      return selectedTemplate.value ? agentTemplates[selectedTemplate.value].name : '未选择'
    }

    const getSelectedCapabilities = () => {
      const allCapabilities = capabilityCategories.flatMap(cat => cat.capabilities)
      return allCapabilities.filter(cap => agentConfig.tools.includes(cap.id))
    }

    const getSelectedMemories = () => {
      return memoryOptions.filter(mem => agentConfig.memory_types.includes(mem.id))
    }

    const createAgent = async () => {
      if (!canCreate()) {
        ElMessage.warning('请完善必填信息')
        return
      }

      try {
        const finalConfig = {
          name: agentConfig.name,
          description: agentConfig.description,
          agent_type: selectedTemplate.value || 'assistant',
          personality: agentConfig.personality,
          tools: agentConfig.tools,
          memory_config: {
            types: agentConfig.memory_types,
            workflow_enabled: agentConfig.workflow_enabled
          },
          avatar: agentConfig.avatar || '🤖'
        }

        const response = await trueAgentService.createAgent(finalConfig)

        if (response.success) {
          emit('agent-created', response.data || response.agent)
          ElMessage.success('智能体创建成功！')
        } else {
          ElMessage.error(response.message || '创建失败')
        }
      } catch (error) {
        console.error('创建智能体失败:', error)
        ElMessage.error('创建失败，请重试')
      }
    }

    return {
      currentStep,
      selectedTemplate,
      agentConfig,
      steps,
      agentTemplates,
      personalityOptions,
      capabilityCategories,
      memoryOptions,
      selectTemplate,
      goToStep,
      nextStep,
      previousStep,
      canProceed,
      canCreate,
      isCapabilitySelected,
      toggleCapability,
      isMemorySelected,
      toggleMemory,
      getSelectedTemplateName,
      getSelectedCapabilities,
      getSelectedMemories,
      createAgent
    }
  }
}
</script>

<style scoped>
.agent-config-wizard {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

/* 步骤指示器 */
.wizard-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: 40px;
  padding: 0 20px;
}

.step-item {
  display: flex;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 10px;
  border-radius: 8px;
  flex: 1;
  max-width: 200px;
}

.step-item:hover {
  background: #f8f9fa;
}

.step-item.active {
  background: #e3f2fd;
}

.step-item.completed .step-number {
  background: #4caf50;
  color: white;
}

.step-number {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #e0e0e0;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  flex-shrink: 0;
}

.step-item.active .step-number {
  background: #2196f3;
  color: white;
}

.step-info {
  flex: 1;
}

.step-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.step-desc {
  font-size: 12px;
  color: #666;
}

/* 向导内容 */
.wizard-content {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1);
  margin-bottom: 30px;
  min-height: 500px;
}

.step-header {
  text-align: center;
  margin-bottom: 40px;
}

.step-header h2 {
  color: #2196f3;
  margin-bottom: 10px;
  font-size: 28px;
}

.step-header p {
  color: #666;
  font-size: 16px;
  max-width: 600px;
  margin: 0 auto;
}

/* 智能体类型选择 */
.agent-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.agent-type-card {
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  padding: 24px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.agent-type-card:hover {
  border-color: #2196f3;
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(33,150,243,0.1);
}

.agent-type-card.selected {
  border-color: #2196f3;
  background: #f3f8ff;
}

.card-icon {
  font-size: 48px;
  text-align: center;
  margin-bottom: 16px;
}

.card-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  text-align: center;
  margin-bottom: 12px;
}

.card-description {
  color: #666;
  text-align: center;
  margin-bottom: 16px;
  line-height: 1.5;
}

.card-features {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
  margin-bottom: 16px;
}

.feature-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
}

.card-use-cases {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.use-case-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.card-use-cases ul {
  margin: 0;
  padding-left: 16px;
}

.card-use-cases li {
  color: #666;
  font-size: 13px;
  margin-bottom: 4px;
}

/* 表单样式 */
.form-section {
  max-width: 600px;
  margin: 0 auto;
}

.form-group {
  margin-bottom: 32px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #333;
}

.label-text {
  font-size: 16px;
}

.label-required {
  color: #f44336;
  margin-left: 4px;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.3s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #2196f3;
}

.form-hint {
  margin-top: 8px;
  font-size: 14px;
  color: #666;
}

/* 个性选项 */
.personality-options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
  gap: 16px;
  margin-top: 12px;
}

.personality-card {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.personality-card:hover {
  border-color: #2196f3;
}

.personality-card.selected {
  border-color: #2196f3;
  background: #f3f8ff;
}

.personality-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.personality-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.personality-desc {
  font-size: 12px;
  color: #666;
}

/* 能力配置 */
.capabilities-section {
  max-width: 800px;
  margin: 0 auto;
}

.capability-category {
  margin-bottom: 40px;
}

.category-header {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #f0f0f0;
}

.category-icon {
  font-size: 24px;
  margin-right: 12px;
}

.category-info h3 {
  margin: 0 0 4px 0;
  color: #333;
}

.category-info p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.capability-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.capability-card {
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.capability-card:hover {
  border-color: #2196f3;
}

.capability-card.selected {
  border-color: #2196f3;
  background: #f3f8ff;
}

.capability-card.recommended::before {
  content: '推荐';
  position: absolute;
  top: -8px;
  right: 12px;
  background: #ff9800;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.capability-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.capability-icon {
  font-size: 20px;
  margin-right: 8px;
}

.capability-name {
  font-weight: 600;
  color: #333;
  flex: 1;
}

.recommended-badge {
  background: #ff9800;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
}

.capability-desc {
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.capability-example {
  font-size: 13px;
  color: #888;
  font-style: italic;
}

/* 记忆选项 */
.memory-options {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.memory-card {
  display: flex;
  align-items: center;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.memory-card:hover {
  border-color: #2196f3;
}

.memory-card.selected {
  border-color: #2196f3;
  background: #f3f8ff;
}

.memory-icon {
  font-size: 24px;
  margin-right: 16px;
}

.memory-info {
  flex: 1;
}

.memory-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.memory-desc {
  color: #666;
  margin-bottom: 4px;
  font-size: 14px;
}

.memory-example {
  color: #888;
  font-size: 12px;
  font-style: italic;
}

/* 工作流开关 */
.workflow-toggle {
  display: flex;
  align-items: center;
  padding: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
  margin-right: 16px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 34px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: #2196f3;
}

input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

.toggle-info {
  flex: 1;
}

.toggle-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.toggle-desc {
  color: #666;
  font-size: 14px;
}

/* 预览样式 */
.config-preview {
  max-width: 600px;
  margin: 0 auto;
}

.preview-section {
  margin-bottom: 32px;
  padding: 24px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.preview-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 18px;
}

.preview-item {
  display: flex;
  margin-bottom: 12px;
}

.item-label {
  font-weight: 600;
  color: #666;
  min-width: 80px;
}

.item-value {
  color: #333;
  flex: 1;
}

.selected-capabilities,
.selected-memories {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-capability,
.selected-memory {
  display: flex;
  align-items: center;
  background: #e3f2fd;
  color: #1976d2;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 14px;
}

.selected-capability .capability-icon,
.selected-memory .memory-icon {
  margin-right: 6px;
}

/* 导航按钮 */
.wizard-navigation {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
}

.nav-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-btn-secondary {
  background: #f5f5f5;
  color: #666;
}

.nav-btn-secondary:hover:not(:disabled) {
  background: #e0e0e0;
}

.nav-btn-primary {
  background: #2196f3;
  color: white;
}

.nav-btn-primary:hover:not(:disabled) {
  background: #1976d2;
}

.nav-btn-success {
  background: #4caf50;
  color: white;
}

.nav-btn-success:hover:not(:disabled) {
  background: #388e3c;
}

.nav-spacer {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wizard-steps {
    flex-direction: column;
    gap: 10px;
  }

  .step-item {
    max-width: none;
  }

  .wizard-content {
    padding: 20px;
  }

  .agent-types-grid {
    grid-template-columns: 1fr;
  }

  .capability-grid {
    grid-template-columns: 1fr;
  }

  .personality-options {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
