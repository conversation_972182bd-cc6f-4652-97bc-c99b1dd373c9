# AI智能体模块文件结构

## 📁 模块组织结构

根据您的要求，所有智能体相关的页面和组件现在都统一放在 `frontend/src/modules/ai-agent/` 目录下，保持了良好的模块化结构。

```
frontend/src/modules/ai-agent/
├── components/                    # 智能体相关组件
│   ├── AgentConfigWizard.vue     # 🆕 智能体配置向导组件
│   ├── KnowledgeBaseExplanation.vue # 🆕 知识库说明组件
│   ├── AgentBanner.vue           # 智能体横幅组件
│   ├── AgentCard.vue             # 智能体卡片组件
│   ├── AgentCardPro.vue          # 专业版智能体卡片
│   ├── AgentLayout.vue           # 智能体布局组件
│   ├── AgentTypeSelector.vue     # 智能体类型选择器
│   ├── AgentsList.vue            # 智能体列表组件
│   ├── CozeAgentCard.vue         # Coze智能体卡片
│   └── CozeTutorialCard.vue      # Coze教程卡片
├── views/                        # 智能体相关页面
│   ├── AgentStudioNew.vue        # 🆕 新版智能体工作室
│   ├── ModelTraining.vue         # 🆕 模型训练中心
│   ├── AgentStudio.vue           # 原版智能体工作室
│   ├── AgentMarketplace.vue      # 智能体市场
│   ├── AgentChat.vue             # 智能体对话
│   ├── AgentDetail.vue           # 智能体详情
│   ├── AgentEditor.vue           # 智能体编辑器
│   ├── TrueAgentChat.vue         # 真实智能体对话
│   ├── DigitalHumanChat.vue      # 数字人对话
│   ├── LanguageLearningChat.vue  # 语言学习对话
│   └── ... (其他智能体相关页面)
├── services/                     # 智能体相关服务
│   ├── agentService.js           # 智能体服务
│   ├── trueAgentService.js       # 真实智能体服务
│   └── agentRuntime.js           # 智能体运行时
└── index.js                      # 模块路由配置
```

## 🆕 新增的核心文件

### 1. AgentConfigWizard.vue
**位置**: `frontend/src/modules/ai-agent/components/AgentConfigWizard.vue`
**功能**: 
- 5步向导式智能体创建流程
- 智能体类型选择（教师、客服、数据分析师、创意写手）
- 能力配置和记忆设置
- 实时预览和配置验证

### 2. AgentStudioNew.vue
**位置**: `frontend/src/modules/ai-agent/views/AgentStudioNew.vue`
**功能**:
- 新版智能体工作室主页面
- 创建模式选择（向导模式、高级模式、模板模式）
- 最近创建的智能体展示
- 内置帮助系统

### 3. KnowledgeBaseExplanation.vue
**位置**: `frontend/src/modules/ai-agent/components/KnowledgeBaseExplanation.vue`
**功能**:
- 知识库 vs 模型训练对比说明
- 详细的使用指导和最佳实践
- 结合使用建议

### 4. ModelTraining.vue
**位置**: `frontend/src/modules/ai-agent/views/ModelTraining.vue`
**功能**:
- 本地模型训练中心
- 专业智能体创建
- 自定义模型微调
- 模型管理和测试

## 🔄 路由配置更新

在 `frontend/src/modules/ai-agent/index.js` 中添加了新的路由：

```javascript
{
  path: 'agents/studio-new',
  name: 'AgentStudioNew',
  component: () => import('./views/AgentStudioNew.vue'),
  meta: { title: '智能体工作室 - 新版', module: 'ai-agent' }
},
{
  path: 'model-training',
  name: 'ModelTraining',
  component: () => import('./views/ModelTraining.vue'),
  meta: { title: '模型训练中心', module: 'ai-agent' }
}
```

## 🔗 访问路径

### 新版智能体工作室
- **URL**: `http://*************:9000/agents/studio-new`
- **入口**: 智能体市场页面 → "创建智能体"下拉菜单 → "向导模式"

### 模型训练中心
- **URL**: `http://*************:9000/model-training`
- **入口**: 智能体工作室页面 → "🎓 模型训练"按钮

### 原版智能体工作室（高级模式）
- **URL**: `http://*************:9000/agents/studio`
- **入口**: 智能体市场页面 → "创建智能体"下拉菜单 → "高级模式"

## 📦 组件依赖关系

```
AgentStudioNew.vue
├── AgentConfigWizard.vue         # 向导组件
└── (Element Plus 组件)

AgentStudio.vue (原版)
├── KnowledgeBaseExplanation.vue  # 知识库说明组件
└── (Element Plus 组件)

ModelTraining.vue
└── (Element Plus 组件)
```

## 🎯 模块化优势

### 1. **清晰的职责分离**
- `components/`: 可复用的UI组件
- `views/`: 页面级组件
- `services/`: 业务逻辑和API调用

### 2. **便于维护**
- 所有智能体相关代码集中在一个模块
- 组件间依赖关系清晰
- 易于定位和修改功能

### 3. **可扩展性**
- 新增智能体功能只需在此模块内添加
- 组件可以在模块内复用
- 路由配置统一管理

### 4. **团队协作**
- 不同开发者可以专注于不同的智能体功能
- 减少文件冲突
- 代码审查更加聚焦

## 🚀 使用建议

### 对于新用户
1. 访问 `/agents/studio-new`
2. 选择"向导模式"
3. 按步骤完成智能体创建

### 对于高级用户
1. 访问 `/agents/studio`
2. 使用完整的配置选项
3. 精细化控制智能体行为

### 对于模型训练
1. 访问 `/model-training`
2. 选择专业类型创建定制模型
3. 结合知识库使用获得最佳效果

## 📝 开发注意事项

1. **导入路径**: 使用相对路径导入模块内组件
   ```javascript
   import AgentConfigWizard from '../components/AgentConfigWizard.vue'
   ```

2. **路由配置**: 新增页面需要在 `index.js` 中注册路由

3. **组件复用**: 优先考虑在模块内复用现有组件

4. **API调用**: 统一使用 `services/` 目录下的服务文件

这样的模块化结构确保了智能体功能的集中管理，提高了代码的可维护性和可扩展性。
