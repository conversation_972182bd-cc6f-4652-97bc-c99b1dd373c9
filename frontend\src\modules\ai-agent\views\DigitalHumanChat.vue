<template>
  <div class="digital-human-chat">
    <!-- 数字人视频区域 -->
    <div class="digital-human-container">
      <div class="video-wrapper">
        <video
          ref="digitalHumanVideo"
          :src="currentVideoUrl"
          @ended="onVideoEnded"
          @loadstart="onVideoLoadStart"
          @canplay="onVideoCanPlay"
          @error="onVideoError"
          autoplay
          muted
          loop
          style="width: 100%; height: 100%; object-fit: cover;"
        ></video>

        <!-- 视频加载失败时的占位图 -->
        <div v-if="videoError" class="video-placeholder">
          <div class="placeholder-content">
            <div class="avatar-placeholder">👤</div>
            <p>数字人视频加载中...</p>
            <small>{{ videoError }}</small>
          </div>
        </div>
        
        <!-- 数字人状态指示器 -->
        <div class="status-indicator">
          <div :class="['status-dot', digitalHumanStatus]"></div>
          <span>{{ getStatusText() }}</span>
          <div :class="['connection-dot', connectionStatus]" :title="getConnectionStatusText()"></div>
        </div>
        
        <!-- 数字人控制面板 -->
        <div class="control-panel">
          <button @click="toggleMute" class="control-btn">
            {{ isMuted ? '🔇' : '🔊' }}
          </button>
          <button @click="showSettings = !showSettings" class="control-btn">
            ⚙️
          </button>
          <button @click="switchDigitalHuman" class="control-btn">
            👤
          </button>
        </div>
      </div>
      
      <!-- 数字人设置面板 -->
      <div v-if="showSettings" class="settings-panel">
        <h4>数字人设置</h4>
        <div class="setting-group">
          <label>选择数字人</label>
          <select v-model="selectedDigitalHuman" @change="onDigitalHumanChange">
            <option v-for="dh in availableDigitalHumans" :key="dh.id" :value="dh.id">
              {{ dh.name }}
            </option>
          </select>
        </div>
        <div class="setting-group">
          <label>表情强度</label>
          <input 
            type="range" 
            v-model="expressionScale" 
            min="0.5" 
            max="2" 
            step="0.1"
            @change="updateExpressionScale"
          />
          <span>{{ expressionScale }}</span>
        </div>
        <div class="setting-group">
          <label>语音设置</label>
          <select v-model="selectedVoice" @change="updateVoiceSettings">
            <option v-for="voice in availableVoices" :key="voice.id" :value="voice.id">
              {{ voice.name }} ({{ voice.language }})
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- 聊天区域 -->
    <div class="chat-container">
      <!-- 聊天消息 -->
      <div class="messages-container" ref="messagesContainer">
        <div 
          v-for="message in messages" 
          :key="message.id"
          :class="['message', message.type]"
        >
          <div class="message-content">
            <div v-html="formatMessage(message.content)"></div>
            <div class="message-time">{{ formatTime(message.timestamp) }}</div>
          </div>
        </div>
        
        <!-- 打字指示器 -->
        <div v-if="isTyping" class="typing-indicator">
          <div class="typing-dots">
            <span></span>
            <span></span>
            <span></span>
          </div>
          <span>数字人正在思考...</span>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-container">
        <div class="input-wrapper">
          <input
            v-model="inputMessage"
            @keydown="handleKeyDown"
            placeholder="与数字人对话..."
            class="message-input"
            :disabled="isTyping"
          />
          <div class="input-actions">
            <button @click="toggleVoiceInput" :class="['action-btn', { 'recording': isRecording }]">
              {{ isRecording ? '🔴' : '🎤' }}
            </button>
            <button @click="sendMessage" :disabled="!inputMessage.trim() || isTyping" class="send-btn">
              发送
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import trueAgentService from '../services/trueAgentService.js'

export default {
  name: 'DigitalHumanChat',
  setup() {
    const route = useRoute()
    
    // 响应式数据
    const messages = ref([])
    const inputMessage = ref('')
    const isTyping = ref(false)
    const isRecording = ref(false)
    const isMuted = ref(false)
    const showSettings = ref(false)
    const videoError = ref(null)

    // WebSocket相关
    const websocket = ref(null)
    const isConnected = ref(false)
    const connectionStatus = ref('disconnected') // disconnected, connecting, connected, error
    const digitalHumanSessionId = ref(null)
    
    // 数字人相关
    const digitalHumanVideo = ref(null)
    const digitalHumanStatus = ref('idle') // idle, speaking, listening, thinking
    const currentVideoUrl = ref('/assets/digital-human/sample_video.mp4') // 使用现有资源
    const selectedDigitalHuman = ref('default')
    const expressionScale = ref(1.0)
    const selectedVoice = ref('zh-CN-XiaoxiaoNeural')
    
    // 智能体相关
    const agentId = computed(() => route.query.agent_id)
    const currentAgent = ref(null)
    
    // 可用数字人列表
    const availableDigitalHumans = ref([
      { id: 'default', name: '默认数字人', avatar: '/static/avatars/default.jpg' },
      { id: 'teacher', name: '教师形象', avatar: '/static/avatars/teacher.jpg' },
      { id: 'assistant', name: '助手形象', avatar: '/static/avatars/assistant.jpg' }
    ])
    
    // 可用语音列表
    const availableVoices = ref([
      { id: 'zh-CN-XiaoxiaoNeural', name: '晓晓', language: 'zh-CN', gender: 'female' },
      { id: 'zh-CN-YunxiNeural', name: '云希', language: 'zh-CN', gender: 'male' },
      { id: 'en-US-JennyNeural', name: 'Jenny', language: 'en-US', gender: 'female' }
    ])
    
    // 方法
    const addMessage = (messageData) => {
      messages.value.push({
        id: Date.now() + Math.random(),
        ...messageData
      })
      nextTick(() => {
        scrollToBottom()
      })
    }
    
    const messagesContainer = ref(null)

    const scrollToBottom = () => {
      const container = messagesContainer.value
      if (container) {
        container.scrollTop = container.scrollHeight
      }
    }
    
    const formatMessage = (content) => {
      return content.replace(/\n/g, '<br>')
    }
    
    const formatTime = (timestamp) => {
      return new Date(timestamp).toLocaleTimeString('zh-CN', {
        hour: '2-digit',
        minute: '2-digit'
      })
    }
    
    const sendMessage = async () => {
      if (!inputMessage.value.trim() || isTyping.value) return

      const userMessage = inputMessage.value.trim()
      inputMessage.value = ''

      // 添加用户消息
      addMessage({
        type: 'user',
        content: userMessage,
        timestamp: new Date()
      })

      // 优先使用WebSocket发送消息
      if (isConnected.value && digitalHumanSessionId.value) {
        const success = sendMessageViaWebSocket(userMessage)
        if (success) {
          console.log('消息通过WebSocket发送成功')
          return
        }
      }

      // 回退到传统API方式
      console.log('使用传统API方式发送消息')
      digitalHumanStatus.value = 'thinking'
      isTyping.value = true

      try {
        // 调用智能体API
        const response = await trueAgentService.chatWithAgent(
          agentId.value,
          userMessage,
          null,
          'digital-human-user'
        )

        if (response.success) {
          // 添加智能体回复
          addMessage({
            type: 'agent',
            content: response.response,
            timestamp: new Date()
          })

          // 生成数字人说话视频
          await generateDigitalHumanResponse(response.response)
        }
      } catch (error) {
        console.error('对话失败:', error)
        addMessage({
          type: 'agent',
          content: '抱歉，我现在无法回复。请稍后再试。',
          timestamp: new Date()
        })
      } finally {
        isTyping.value = false
        digitalHumanStatus.value = 'idle'
      }
    }
    
    const generateDigitalHumanResponse = async (text) => {
      try {
        digitalHumanStatus.value = 'speaking'

        // 首先尝试使用新的数字人智能体API
        try {
          const response = await fetch(`/api/v1/agents/${agentId.value}/digital-human-chat`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
            },
            body: JSON.stringify({
              message: text,
              conversation_id: null
            })
          })

          const result = await response.json()

          if (result.success && result.session_id) {
            // 连接WebSocket获取实时数字人视频
            await connectToDigitalHumanWebSocket(result.session_id)
            return
          }
        } catch (apiError) {
          console.warn('新API失败，使用备用方案:', apiError)
        }

        // 备用方案：调用原有的数字人生成API
        const response = await fetch('/api/v1/digital-human-generation/create', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token') || ''}`
          },
          body: JSON.stringify({
            name: `${currentAgent.value?.name || '智能体'}数字人`,
            type: 'assistant',
            gender: 'female',
            description: `${currentAgent.value?.name || '智能体'}的数字人形象`,
            welcome_text: text,
            appearance_type: 'ai_generated',
            facial_expression: 'friendly',
            clothing_style: 'business',
            background: 'office',
            voice_type: 'standard',
            voice_speed: 1.0,
            voice_pitch: 1.0,
            selected_voice: selectedVoice.value
          })
        })

        const result = await response.json()

        if (result.success) {
          // 轮询任务状态
          await pollTaskStatus(result.task_id)
        } else {
          console.warn('数字人生成失败:', result.error)
          digitalHumanStatus.value = 'idle'
        }
      } catch (error) {
        console.error('生成数字人回复失败:', error)
        digitalHumanStatus.value = 'idle'
      }
    }

    const connectToDigitalHumanWebSocket = async (sessionId) => {
      try {
        digitalHumanSessionId.value = sessionId
        connectionStatus.value = 'connecting'

        const wsUrl = `ws://localhost:8001/api/v1/realtime/ws/conversation/${sessionId}`
        websocket.value = new WebSocket(wsUrl)

        websocket.value.onopen = () => {
          console.log('数字人WebSocket连接成功')
          isConnected.value = true
          connectionStatus.value = 'connected'

          // 发送连接确认
          sendWebSocketMessage({
            type: 'ping',
            timestamp: Date.now()
          })
        }

        websocket.value.onmessage = (event) => {
          const data = JSON.parse(event.data)
          console.log('WebSocket消息:', data)

          handleWebSocketMessage(data)
        }

        websocket.value.onerror = (error) => {
          console.error('数字人WebSocket错误:', error)
          connectionStatus.value = 'error'
          isConnected.value = false
          digitalHumanStatus.value = 'idle'
        }

        websocket.value.onclose = () => {
          console.log('数字人WebSocket连接关闭')
          isConnected.value = false
          connectionStatus.value = 'disconnected'
          digitalHumanStatus.value = 'idle'
        }

      } catch (error) {
        console.error('WebSocket连接失败:', error)
        connectionStatus.value = 'error'
        digitalHumanStatus.value = 'idle'
      }
    }

    const handleWebSocketMessage = (data) => {
      switch (data.type) {
        case 'connected':
          console.log('WebSocket连接确认:', data.message)
          break

        case 'user_speech':
          // 处理用户语音识别结果
          if (data.data.type === 'final') {
            addMessage({
              type: 'user',
              content: data.data.text,
              timestamp: new Date(data.data.timestamp)
            })
          }
          break

        case 'ai_response':
          // 处理AI回复
          addMessage({
            type: 'agent',
            content: data.data.text,
            timestamp: new Date(data.data.timestamp)
          })
          break

        case 'video_generated':
          // 播放生成的数字人视频
          const videoUrl = data.data.video_url
          if (videoUrl) {
            currentVideoUrl.value = videoUrl
            videoError.value = null

            if (digitalHumanVideo.value) {
              digitalHumanVideo.value.load()
              digitalHumanVideo.value.play().catch(console.warn)
            }

            console.log('数字人视频已生成:', videoUrl)
          }
          break

        case 'status':
          console.log('状态更新:', data.data.message)
          break

        case 'error':
          console.error('WebSocket错误:', data.data.message)
          digitalHumanStatus.value = 'idle'
          break

        case 'pong':
          // 心跳响应
          console.log('心跳响应收到')
          break

        default:
          console.log('未知WebSocket消息类型:', data.type)
      }
    }

    const sendWebSocketMessage = (message) => {
      if (websocket.value && websocket.value.readyState === WebSocket.OPEN) {
        websocket.value.send(JSON.stringify(message))
        return true
      } else {
        console.warn('WebSocket未连接，无法发送消息')
        return false
      }
    }

    const disconnectWebSocket = () => {
      if (websocket.value) {
        websocket.value.close()
        websocket.value = null
      }
      isConnected.value = false
      connectionStatus.value = 'disconnected'
      digitalHumanSessionId.value = null
    }

    const pollTaskStatus = async (taskId) => {
      try {
        const maxAttempts = 30
        let attempts = 0

        const poll = async () => {
          if (attempts >= maxAttempts) {
            digitalHumanStatus.value = 'idle'
            return
          }

          try {
            const response = await fetch(`/api/v1/task-monitor/status/${taskId}`)
            const result = await response.json()

            if (result.success) {
              if (result.status === 'completed') {
                // 任务完成，获取生成的视频
                if (result.result && result.result.preview_url) {
                  currentVideoUrl.value = result.result.preview_url
                  videoError.value = null

                  if (digitalHumanVideo.value) {
                    digitalHumanVideo.value.load()
                    digitalHumanVideo.value.play().catch(console.warn)
                  }
                }
                digitalHumanStatus.value = 'idle'
                return
              } else if (result.status === 'failed') {
                console.error('数字人生成失败:', result.error)
                digitalHumanStatus.value = 'idle'
                return
              }
            }

            attempts++
            setTimeout(poll, 1000) // 1秒后重试

          } catch (error) {
            console.error('轮询任务状态失败:', error)
            attempts++
            setTimeout(poll, 1000)
          }
        }

        poll()

      } catch (error) {
        console.error('轮询任务失败:', error)
        digitalHumanStatus.value = 'idle'
      }
    }
    
    const handleKeyDown = (event) => {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        sendMessage()
      }
    }
    
    const toggleVoiceInput = () => {
      isRecording.value = !isRecording.value
      // 实现语音输入逻辑
      ElMessage.info(isRecording.value ? '开始录音' : '停止录音')
    }
    
    const toggleMute = () => {
      isMuted.value = !isMuted.value
      if (digitalHumanVideo.value) {
        digitalHumanVideo.value.muted = isMuted.value
      }
    }
    
    const switchDigitalHuman = () => {
      // 切换数字人逻辑
      showSettings.value = true
    }
    
    const onDigitalHumanChange = () => {
      // 切换数字人时重新加载
      currentVideoUrl.value = `/assets/digital-human/sample_video.mp4`
      console.log('切换数字人:', selectedDigitalHuman.value)
    }
    
    const updateExpressionScale = () => {
      // 更新表情强度
      console.log('表情强度更新为:', expressionScale.value)
    }
    
    const updateVoiceSettings = () => {
      // 更新语音设置
      console.log('语音设置更新为:', selectedVoice.value)
    }
    
    const getStatusText = () => {
      const statusMap = {
        idle: '待机中',
        speaking: '说话中',
        listening: '聆听中',
        thinking: '思考中'
      }
      return statusMap[digitalHumanStatus.value] || '未知状态'
    }

    const getConnectionStatusText = () => {
      const statusMap = {
        disconnected: '未连接',
        connecting: '连接中',
        connected: '已连接',
        error: '连接错误'
      }
      return statusMap[connectionStatus.value] || '未知状态'
    }

    const sendMessageViaWebSocket = (message) => {
      if (isConnected.value) {
        const success = sendWebSocketMessage({
          type: 'send_text',
          text: message
        })

        if (success) {
          digitalHumanStatus.value = 'thinking'
          return true
        }
      }
      return false
    }
    
    const onVideoEnded = () => {
      digitalHumanStatus.value = 'idle'
      // 视频结束后保持当前视频，或者可以切换到待机视频
      console.log('数字人视频播放结束')
    }
    
    const onVideoLoadStart = () => {
      console.log('视频开始加载')
    }
    
    const onVideoCanPlay = () => {
      console.log('视频可以播放')
      videoError.value = null // 清除错误状态
    }

    const onVideoError = (event) => {
      console.error('视频加载失败:', event)
      videoError.value = '数字人视频暂时无法加载，请稍后再试'
      digitalHumanStatus.value = 'idle'
    }
    
    // 初始化
    const initializeChat = async () => {
      // 加载智能体信息
      if (agentId.value) {
        try {
          const response = await trueAgentService.getAgent(agentId.value)
          if (response.success) {
            currentAgent.value = response.agent
            
            // 添加欢迎消息
            addMessage({
              type: 'agent',
              content: `你好！我是${response.agent.name}，很高兴与你对话！`,
              timestamp: new Date()
            })
          }
        } catch (error) {
          console.error('加载智能体失败:', error)
        }
      }
    }
    
    onMounted(() => {
      initializeChat()
    })
    
    return {
      messages,
      inputMessage,
      isTyping,
      isRecording,
      isMuted,
      showSettings,
      videoError,
      digitalHumanVideo,
      digitalHumanStatus,
      connectionStatus,
      currentVideoUrl,
      selectedDigitalHuman,
      expressionScale,
      selectedVoice,
      availableDigitalHumans,
      availableVoices,
      messagesContainer,
      formatMessage,
      formatTime,
      sendMessage,
      handleKeyDown,
      toggleVoiceInput,
      toggleMute,
      switchDigitalHuman,
      onDigitalHumanChange,
      updateExpressionScale,
      updateVoiceSettings,
      getStatusText,
      getConnectionStatusText,
      onVideoEnded,
      onVideoLoadStart,
      onVideoCanPlay,
      onVideoError
    }
  }
}
</script>

<style scoped>
.digital-human-chat {
  display: flex;
  height: 100vh;
  background: #f5f5f5;
}

.digital-human-container {
  width: 400px;
  background: #000;
  position: relative;
  display: flex;
  flex-direction: column;
}

.video-wrapper {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-wrapper video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.status-indicator {
  position: absolute;
  top: 20px;
  left: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 14px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.idle { background: #52c41a; }
.status-dot.speaking { background: #1890ff; animation: pulse 1s infinite; }
.status-dot.listening { background: #faad14; }
.status-dot.thinking { background: #722ed1; animation: pulse 1s infinite; }

.connection-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
  cursor: help;
}

.connection-dot.disconnected {
  background-color: #d9d9d9;
}

.connection-dot.connecting {
  background-color: #faad14;
  animation: pulse 1.5s infinite;
}

.connection-dot.connected {
  background-color: #52c41a;
}

.connection-dot.error {
  background-color: #ff4d4f;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.control-panel {
  position: absolute;
  bottom: 20px;
  right: 20px;
  display: flex;
  gap: 8px;
}

.control-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: white;
  transform: scale(1.1);
}

.settings-panel {
  background: white;
  padding: 20px;
  border-top: 1px solid #e8e8e8;
}

.settings-panel h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
}

.setting-group {
  margin-bottom: 16px;
}

.setting-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
}

.setting-group select,
.setting-group input {
  width: 100%;
  padding: 8px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
}

.messages-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
}

.message {
  margin-bottom: 16px;
  display: flex;
}

.message.user {
  justify-content: flex-end;
}

.message.agent {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
}

.message.user .message-content {
  background: #1890ff;
  color: white;
}

.message.agent .message-content {
  background: #f0f0f0;
  color: #333;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 4px;
}

.typing-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #999;
  font-size: 14px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 6px;
  height: 6px;
  background: #999;
  border-radius: 50%;
  animation: typing 1.4s infinite;
}

.typing-dots span:nth-child(2) { animation-delay: 0.2s; }
.typing-dots span:nth-child(3) { animation-delay: 0.4s; }

@keyframes typing {
  0%, 60%, 100% { transform: translateY(0); }
  30% { transform: translateY(-10px); }
}

.input-container {
  padding: 20px;
  border-top: 1px solid #e8e8e8;
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
}

.message-input:focus {
  border-color: #1890ff;
}

.input-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 50%;
  background: #f0f0f0;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e6f7ff;
  transform: scale(1.1);
}

.action-btn.recording {
  background: #ff4d4f;
  color: white;
  animation: pulse 1s infinite;
}

.send-btn {
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  transition: background 0.2s ease;
}

.send-btn:hover:not(:disabled) {
  background: #40a9ff;
}

.send-btn:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.video-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.placeholder-content {
  text-align: center;
}

.avatar-placeholder {
  font-size: 80px;
  margin-bottom: 20px;
  opacity: 0.8;
}

.placeholder-content p {
  font-size: 18px;
  margin: 10px 0;
  font-weight: 500;
}

.placeholder-content small {
  font-size: 14px;
  opacity: 0.7;
}
</style>
