"""
视频生成GPU优化服务
专门用于优化Wan2.1模型的GPU使用和内存管理
"""
import os
import sys
import logging
import subprocess
import psutil
from typing import Dict, Any, Optional, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class VideoGenerationOptimizer:
    """视频生成GPU优化器"""
    
    def __init__(self):
        self.gpu_available = False
        self.gpu_info = {}
        self.recommended_settings = {}
        self.check_gpu_environment()
    
    def check_gpu_environment(self) -> Dict[str, Any]:
        """检查GPU环境和配置"""
        try:
            import torch
            
            # 基本GPU检查
            if not torch.cuda.is_available():
                logger.warning("❌ CUDA不可用，将使用CPU模式（速度较慢）")
                return self._get_cpu_fallback_config()
            
            # 获取GPU信息
            device_count = torch.cuda.device_count()
            current_device = torch.cuda.current_device()
            device_name = torch.cuda.get_device_name(0)
            
            # 获取内存信息
            memory_total = torch.cuda.get_device_properties(0).total_memory
            memory_allocated = torch.cuda.memory_allocated(0)
            memory_cached = torch.cuda.memory_reserved(0)
            memory_free = memory_total - memory_allocated
            
            self.gpu_info = {
                "available": True,
                "device_count": device_count,
                "current_device": current_device,
                "device_name": device_name,
                "memory_total_gb": memory_total / 1024**3,
                "memory_allocated_gb": memory_allocated / 1024**3,
                "memory_cached_gb": memory_cached / 1024**3,
                "memory_free_gb": memory_free / 1024**3,
                "compute_capability": torch.cuda.get_device_capability(0)
            }
            
            self.gpu_available = True
            logger.info(f"✅ GPU可用: {device_name}")
            logger.info(f"📊 GPU内存: {memory_free/1024**3:.1f}GB 可用 / {memory_total/1024**3:.1f}GB 总计")
            
            # 生成推荐设置
            self.recommended_settings = self._generate_gpu_recommendations()
            
            return self.gpu_info
            
        except ImportError:
            logger.error("❌ PyTorch未安装，无法使用GPU加速")
            return self._get_cpu_fallback_config()
        except Exception as e:
            logger.error(f"❌ GPU检查失败: {e}")
            return self._get_cpu_fallback_config()
    
    def _get_cpu_fallback_config(self) -> Dict[str, Any]:
        """CPU回退配置"""
        return {
            "available": False,
            "device": "cpu",
            "recommended_settings": {
                "batch_size": 1,
                "num_workers": min(4, psutil.cpu_count()),
                "memory_efficient": True,
                "offload_model": True,
                "use_fp16": False,
                "max_frames": 32,
                "resolution": "512x512"
            }
        }
    
    def _generate_gpu_recommendations(self) -> Dict[str, Any]:
        """根据GPU配置生成推荐设置"""
        memory_gb = self.gpu_info["memory_free_gb"]
        device_name = self.gpu_info["device_name"].lower()
        
        # 根据GPU内存推荐设置
        if memory_gb >= 24:  # 高端GPU (RTX 4090, A100等)
            settings = {
                "batch_size": 2,
                "use_fp16": True,
                "offload_model": False,
                "max_frames": 81,
                "resolution": "1280x720",
                "num_inference_steps": 50,
                "memory_efficient": False,
                "gradient_checkpointing": False
            }
        elif memory_gb >= 12:  # 中高端GPU (RTX 4070Ti, RTX 3080等)
            settings = {
                "batch_size": 1,
                "use_fp16": True,
                "offload_model": False,
                "max_frames": 65,
                "resolution": "768x512",
                "num_inference_steps": 40,
                "memory_efficient": True,
                "gradient_checkpointing": True
            }
        elif memory_gb >= 8:   # 中端GPU (RTX 4060Ti, RTX 3070等)
            settings = {
                "batch_size": 1,
                "use_fp16": True,
                "offload_model": True,
                "max_frames": 49,
                "resolution": "768x512",
                "num_inference_steps": 30,
                "memory_efficient": True,
                "gradient_checkpointing": True
            }
        elif memory_gb >= 6:   # 入门GPU (RTX 4060, GTX 1660等)
            settings = {
                "batch_size": 1,
                "use_fp16": True,
                "offload_model": True,
                "max_frames": 33,
                "resolution": "512x512",
                "num_inference_steps": 25,
                "memory_efficient": True,
                "gradient_checkpointing": True
            }
        else:  # 低端GPU或内存不足
            settings = {
                "batch_size": 1,
                "use_fp16": True,
                "offload_model": True,
                "max_frames": 33,
                "resolution": "512x512",
                "num_inference_steps": 20,
                "memory_efficient": True,
                "gradient_checkpointing": True,
                "cpu_fallback": True
            }
        
        # 特定GPU优化
        if "rtx" in device_name:
            settings["use_tensorrt"] = True
        if "4090" in device_name or "4080" in device_name:
            settings["use_flash_attention"] = True
        
        return settings
    
    def optimize_wan_args(self, base_args: Dict[str, Any]) -> Dict[str, Any]:
        """优化Wan2.1模型参数"""
        if not self.gpu_available:
            logger.info("🔄 使用CPU优化配置")
            return self._apply_cpu_optimizations(base_args)
        
        logger.info("🚀 应用GPU优化配置")
        optimized_args = base_args.copy()
        settings = self.recommended_settings
        
        # 应用推荐设置
        optimized_args.update({
            "device": "cuda",
            "offload_model": settings.get("offload_model", True),
            "frame_num": min(base_args.get("frame_num", 81), settings.get("max_frames", 81)),
            "sample_steps": settings.get("num_inference_steps", 40),
            "size": self._validate_resolution(base_args.get("size", "768x512"), settings.get("resolution", "768x512")),
            "t5_cpu": settings.get("offload_model", True),  # T5模型放CPU可节省GPU内存
        })
        
        # 内存优化
        if settings.get("memory_efficient", True):
            optimized_args.update({
                "gradient_checkpointing": True,
                "attention_slicing": True,
                "cpu_offload": True
            })
        
        logger.info(f"📊 优化后配置: 帧数={optimized_args['frame_num']}, 分辨率={optimized_args['size']}, 步数={optimized_args['sample_steps']}")
        return optimized_args
    
    def _apply_cpu_optimizations(self, base_args: Dict[str, Any]) -> Dict[str, Any]:
        """应用CPU优化"""
        optimized_args = base_args.copy()
        optimized_args.update({
            "device": "cpu",
            "offload_model": True,
            "frame_num": min(base_args.get("frame_num", 33), 33),  # CPU模式限制帧数
            "sample_steps": 20,  # 减少推理步数
            "size": "512x512",   # 降低分辨率
            "t5_cpu": True,
            "use_fp16": False    # CPU不支持FP16
        })
        return optimized_args
    
    def _validate_resolution(self, requested: str, recommended: str) -> str:
        """验证并调整分辨率"""
        # 支持的分辨率列表
        supported_resolutions = [
            "512x512", "768x512", "512x768", 
            "1280x720", "720x1280", "832x480", "480x832"
        ]
        
        if requested in supported_resolutions:
            return requested
        else:
            logger.warning(f"⚠️  不支持的分辨率 {requested}，使用推荐分辨率 {recommended}")
            return recommended
    
    def monitor_gpu_usage(self) -> Dict[str, Any]:
        """监控GPU使用情况"""
        if not self.gpu_available:
            return {"available": False}
        
        try:
            import torch
            
            # 清理GPU缓存
            torch.cuda.empty_cache()
            
            # 获取当前使用情况
            memory_allocated = torch.cuda.memory_allocated(0)
            memory_cached = torch.cuda.memory_reserved(0)
            memory_total = torch.cuda.get_device_properties(0).total_memory
            memory_free = memory_total - memory_allocated
            
            usage_info = {
                "available": True,
                "memory_allocated_gb": memory_allocated / 1024**3,
                "memory_cached_gb": memory_cached / 1024**3,
                "memory_free_gb": memory_free / 1024**3,
                "memory_total_gb": memory_total / 1024**3,
                "memory_usage_percent": (memory_allocated / memory_total) * 100,
                "temperature": self._get_gpu_temperature(),
                "utilization": self._get_gpu_utilization()
            }
            
            # 内存警告
            if usage_info["memory_usage_percent"] > 90:
                logger.warning("⚠️  GPU内存使用率超过90%，可能导致OOM错误")
            elif usage_info["memory_usage_percent"] > 80:
                logger.info("📊 GPU内存使用率超过80%，建议启用内存优化")
            
            return usage_info
            
        except Exception as e:
            logger.error(f"❌ GPU监控失败: {e}")
            return {"available": False, "error": str(e)}
    
    def _get_gpu_temperature(self) -> Optional[float]:
        """获取GPU温度"""
        try:
            result = subprocess.run(
                ["nvidia-smi", "--query-gpu=temperature.gpu", "--format=csv,noheader,nounits"],
                capture_output=True, text=True, timeout=5
            )
            if result.returncode == 0:
                return float(result.stdout.strip())
        except:
            pass
        return None
    
    def _get_gpu_utilization(self) -> Optional[float]:
        """获取GPU利用率"""
        try:
            result = subprocess.run(
                ["nvidia-smi", "--query-gpu=utilization.gpu", "--format=csv,noheader,nounits"],
                capture_output=True, text=True, timeout=5
            )
            if result.returncode == 0:
                return float(result.stdout.strip())
        except:
            pass
        return None
    
    def cleanup_gpu_memory(self):
        """清理GPU内存"""
        if self.gpu_available:
            try:
                import torch
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.info("🧹 GPU内存已清理")
            except Exception as e:
                logger.error(f"❌ GPU内存清理失败: {e}")
    
    def get_optimization_report(self) -> Dict[str, Any]:
        """生成优化报告"""
        report = {
            "gpu_info": self.gpu_info,
            "recommended_settings": self.recommended_settings,
            "current_usage": self.monitor_gpu_usage(),
            "optimization_tips": []
        }
        
        # 生成优化建议
        if self.gpu_available:
            memory_gb = self.gpu_info["memory_free_gb"]
            
            if memory_gb < 6:
                report["optimization_tips"].extend([
                    "GPU内存不足6GB，建议启用模型卸载(offload_model=True)",
                    "降低分辨率到512x512以减少内存使用",
                    "减少推理步数到20-25步",
                    "考虑使用CPU模式以避免OOM错误"
                ])
            elif memory_gb < 12:
                report["optimization_tips"].extend([
                    "中等GPU内存，建议启用内存优化选项",
                    "使用FP16精度以节省内存",
                    "限制最大帧数到49帧"
                ])
            else:
                report["optimization_tips"].extend([
                    "GPU内存充足，可以使用较高的分辨率和帧数",
                    "可以禁用模型卸载以提高速度",
                    "建议使用FP16精度以提高性能"
                ])
        else:
            report["optimization_tips"].extend([
                "GPU不可用，使用CPU模式",
                "CPU模式下建议降低所有参数以提高速度",
                "考虑安装CUDA和PyTorch GPU版本"
            ])
        
        return report

# 全局优化器实例
video_optimizer = VideoGenerationOptimizer()
