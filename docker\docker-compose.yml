version: '3.8'

services:
  wanx-service:
    build:
      context: ./wanx
      dockerfile: Dockerfile
    container_name: wanx-video-generator
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
      - NVIDIA_DRIVER_CAPABILITIES=compute,utility
      - MODE=server
    volumes:
      # 挂载Wanx模型目录
      - ../backend/storage/models/video_generation/wan/Wan2.1:/app/models:ro
      # 挂载输出目录
      - ../backend/storage/videos:/app/output:rw
      # 挂载请求目录
      - ./requests:/app/requests:rw
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import torch; print(torch.cuda.is_available())"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 可选：Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: wanx-redis
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes

volumes:
  redis_data:
