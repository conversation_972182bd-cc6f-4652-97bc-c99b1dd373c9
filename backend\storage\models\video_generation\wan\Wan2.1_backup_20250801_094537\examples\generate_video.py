#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
示例脚本: 如何使用WAN 2.1生成视频
"""

import os
import sys
import argparse
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, 
                   format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('WAN-Example')

def main():
    # 获取当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 获取WAN根目录
    root_dir = os.path.dirname(current_dir)
    
    # 添加到路径
    if root_dir not in sys.path:
        sys.path.insert(0, root_dir)
    
    # 更改工作目录
    os.chdir(root_dir)
    
    # 打印示例命令
    logger.info("WAN 2.1 视频生成示例")
    logger.info("以下是一些示例命令:")
    
    # 示例1: 基本用法
    logger.info("\n示例1: 基本用法")
    logger.info("python generate.py --text \"美丽的花园里，花朵绽放\" --output output.mp4")
    
    # 示例2: 指定尺寸和帧数
    logger.info("\n示例2: 指定尺寸和帧数")
    logger.info("python generate.py --text \"海边日落的景色\" --width 1024 --height 576 --length 24 --output sunset.mp4")
    
    # 示例3: 使用高质量设置
    logger.info("\n示例3: 使用高质量设置")
    logger.info("python generate.py --text \"森林中的小溪\" --fps 24 --quality 18 --enhance --output forest_stream.mp4")
    
    # 示例4: 使用中文提示词和指定种子
    logger.info("\n示例4: 使用中文提示词和指定种子")
    logger.info("python generate.py --text \"城市夜景，霓虹灯闪烁\" --chinese --seed 42 --output city_night.mp4")
    
    # 示例5: 调整高级参数
    logger.info("\n示例5: 调整高级参数")
    logger.info("python generate.py --text \"太空中的行星\" --cfg 10.0 --steps 100 --output space.mp4")
    
    # 是否立即运行示例
    run_example = input("\n是否要立即运行一个示例? (y/n): ")
    if run_example.lower() == 'y':
        import subprocess
        
        # 运行基本示例
        cmd = [
            sys.executable,
            os.path.join(root_dir, "generate.py"),
            "--text", "美丽的花园里，花朵绽放",
            "--output", "example_output.mp4"
        ]
        
        logger.info(f"运行命令: {' '.join(cmd)}")
        subprocess.run(cmd)
        
        logger.info("示例已完成，请检查当前目录下的 example_output.mp4 文件")
    else:
        logger.info("退出示例，你可以手动运行上述命令来测试")

if __name__ == "__main__":
    main() 