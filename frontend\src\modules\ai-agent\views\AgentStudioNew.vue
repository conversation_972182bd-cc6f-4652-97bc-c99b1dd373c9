<template>
  <div class="agent-studio-new">
    <!-- 页面头部 -->
    <div class="studio-header">
      <div class="header-left">
        <button @click="goBack" class="back-btn">
          ← 返回
        </button>
        <div class="header-info">
          <h1>🤖 智能体工作室</h1>
          <p>通过简单的步骤创建您的专属智能体</p>
        </div>
      </div>
      
      <div class="header-right">
        <button @click="openModelTraining" class="btn-training">🎓 模型训练</button>
        <button @click="showHelp = true" class="btn-help">❓ 帮助</button>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="studio-main">
      <!-- 创建模式选择 -->
      <div v-if="!showWizard" class="creation-modes">
        <div class="modes-header">
          <h2>选择创建方式</h2>
          <p>选择最适合您的智能体创建方式</p>
        </div>

        <div class="modes-grid">
          <!-- 向导模式 -->
          <div class="mode-card recommended" @click="startWizard">
            <div class="mode-badge">推荐</div>
            <div class="mode-icon">🧙‍♂️</div>
            <div class="mode-title">向导模式</div>
            <div class="mode-description">
              通过简单的步骤指导，轻松创建专业的智能体。适合新手用户。
            </div>
            <div class="mode-features">
              <div class="feature">✅ 步骤清晰</div>
              <div class="feature">✅ 预设模板</div>
              <div class="feature">✅ 智能推荐</div>
            </div>
            <div class="mode-action">
              <button class="action-btn primary">开始创建</button>
            </div>
          </div>

          <!-- 高级模式 -->
          <div class="mode-card" @click="startAdvanced">
            <div class="mode-icon">⚙️</div>
            <div class="mode-title">高级模式</div>
            <div class="mode-description">
              完全自定义配置，适合有经验的用户进行精细化设置。
            </div>
            <div class="mode-features">
              <div class="feature">🔧 完全自定义</div>
              <div class="feature">🎯 精细控制</div>
              <div class="feature">🚀 高级功能</div>
            </div>
            <div class="mode-action">
              <button class="action-btn secondary">高级配置</button>
            </div>
          </div>

          <!-- 模板模式 -->
          <div class="mode-card" @click="showTemplates = true">
            <div class="mode-icon">📋</div>
            <div class="mode-title">模板模式</div>
            <div class="mode-description">
              从预设模板快速开始，一键创建常用类型的智能体。
            </div>
            <div class="mode-features">
              <div class="feature">⚡ 快速创建</div>
              <div class="feature">📚 丰富模板</div>
              <div class="feature">🎨 可定制化</div>
            </div>
            <div class="mode-action">
              <button class="action-btn secondary">选择模板</button>
            </div>
          </div>
        </div>

        <!-- 最近创建的智能体 -->
        <div class="recent-agents" v-if="recentAgents.length > 0">
          <div class="section-header">
            <h3>最近创建</h3>
            <button @click="viewAllAgents" class="view-all-btn">查看全部</button>
          </div>
          
          <div class="agents-grid">
            <div 
              v-for="agent in recentAgents" 
              :key="agent.id"
              class="agent-card"
              @click="editAgent(agent.id)"
            >
              <div class="agent-avatar">{{ getAgentIcon(agent.type) }}</div>
              <div class="agent-info">
                <div class="agent-name">{{ agent.name }}</div>
                <div class="agent-type">{{ agent.type_name }}</div>
                <div class="agent-date">{{ formatDate(agent.created_at) }}</div>
              </div>
              <div class="agent-actions">
                <button @click.stop="testAgent(agent)" class="test-btn">测试</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 向导界面 -->
      <div v-if="showWizard" class="wizard-container">
        <AgentConfigWizard @agent-created="onAgentCreated" />
      </div>
    </div>

    <!-- 帮助对话框 -->
    <el-dialog v-model="showHelp" title="智能体创建帮助" width="600px">
      <div class="help-content">
        <div class="help-section">
          <h3>🤔 什么是智能体？</h3>
          <p>智能体是具有特定能力和个性的AI助手，可以帮助您完成各种任务，如教学、客服、数据分析等。</p>
        </div>

        <div class="help-section">
          <h3>🎯 如何选择创建方式？</h3>
          <ul>
            <li><strong>向导模式</strong>：适合新手，通过简单步骤创建</li>
            <li><strong>高级模式</strong>：适合专家，可以精细化配置</li>
            <li><strong>模板模式</strong>：快速开始，从预设模板选择</li>
          </ul>
        </div>

        <div class="help-section">
          <h3>🛠️ 智能体的核心组成</h3>
          <ul>
            <li><strong>基本信息</strong>：名称、描述、个性设置</li>
            <li><strong>能力配置</strong>：智能体可以使用的工具和技能</li>
            <li><strong>知识库</strong>：智能体的专业知识来源</li>
            <li><strong>记忆系统</strong>：如何记住和使用历史信息</li>
          </ul>
        </div>

        <div class="help-section">
          <h3>💡 最佳实践建议</h3>
          <ul>
            <li>明确智能体的用途和目标用户</li>
            <li>选择合适的能力，避免功能过于复杂</li>
            <li>上传相关的知识文档提高专业性</li>
            <li>通过测试不断优化配置</li>
          </ul>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showHelp = false">关闭</el-button>
          <el-button type="primary" @click="startWizard; showHelp = false">开始创建</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 模板选择对话框 -->
    <el-dialog v-model="showTemplates" title="选择智能体模板" width="800px">
      <div class="templates-grid">
        <div 
          v-for="(template, key) in agentTemplates" 
          :key="key"
          class="template-card"
          @click="createFromTemplate(key)"
        >
          <div class="template-icon">{{ template.icon }}</div>
          <div class="template-name">{{ template.name }}</div>
          <div class="template-desc">{{ template.description }}</div>
          <div class="template-features">
            <span v-for="feature in template.features" :key="feature" class="feature-tag">
              {{ feature }}
            </span>
          </div>
        </div>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showTemplates = false">取消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import AgentConfigWizard from '../components/AgentConfigWizard.vue'

export default {
  name: 'AgentStudioNew',
  components: {
    AgentConfigWizard
  },
  
  setup() {
    const router = useRouter()
    const showWizard = ref(false)
    const showHelp = ref(false)
    const showTemplates = ref(false)
    const recentAgents = ref([])

    // 智能体模板
    const agentTemplates = {
      teacher: {
        name: '教师助手',
        icon: '👨‍🏫',
        description: '专业的教育助手，擅长教学和答疑',
        features: ['耐心教学', '个性化指导', '知识问答']
      },
      customer_service: {
        name: '客服助手',
        icon: '🎧',
        description: '友好的客户服务助手，提供专业支持',
        features: ['24小时服务', '多语言支持', '问题解决']
      },
      data_analyst: {
        name: '数据分析师',
        icon: '📊',
        description: '专业的数据分析助手，擅长数据处理和可视化',
        features: ['数据处理', '图表生成', '趋势分析']
      },
      creative_writer: {
        name: '创意写手',
        icon: '✍️',
        description: '富有创意的写作助手，擅长各种文体创作',
        features: ['创意写作', '文案策划', '内容优化']
      }
    }

    // 方法
    const goBack = () => {
      router.push('/agents')
    }

    const openModelTraining = () => {
      window.open('/model-training', '_blank')
    }

    const startWizard = () => {
      showWizard.value = true
    }

    const startAdvanced = () => {
      router.push('/agents/studio')
    }

    const createFromTemplate = (templateKey) => {
      showTemplates.value = false
      // 这里可以预填充模板数据到向导中
      startWizard()
    }

    const onAgentCreated = (agentConfig) => {
      console.log('智能体创建成功:', agentConfig)
      ElMessage.success('智能体创建成功！')
      // 可以跳转到智能体详情页或列表页
      setTimeout(() => {
        router.push('/agents')
      }, 1500)
    }

    const editAgent = (agentId) => {
      router.push(`/agents/studio?mode=edit&agent_id=${agentId}`)
    }

    const testAgent = (agent) => {
      ElMessage.info(`测试智能体: ${agent.name}`)
      // 这里可以打开测试对话框
    }

    const viewAllAgents = () => {
      router.push('/agents')
    }

    const getAgentIcon = (type) => {
      const icons = {
        teacher: '👨‍🏫',
        customer_service: '🎧',
        data_analyst: '📊',
        creative_writer: '✍️',
        default: '🤖'
      }
      return icons[type] || icons.default
    }

    const formatDate = (dateString) => {
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    }

    // 加载最近的智能体
    const loadRecentAgents = async () => {
      try {
        // 这里应该调用API获取最近创建的智能体
        // const response = await api.get('/agents/recent')
        // recentAgents.value = response.data.agents
        
        // 模拟数据
        recentAgents.value = [
          {
            id: '1',
            name: '数学老师小明',
            type: 'teacher',
            type_name: '教师助手',
            created_at: '2024-01-15T10:30:00Z'
          },
          {
            id: '2',
            name: '客服小助手',
            type: 'customer_service',
            type_name: '客服助手',
            created_at: '2024-01-14T15:20:00Z'
          }
        ]
      } catch (error) {
        console.error('加载最近智能体失败:', error)
      }
    }

    onMounted(() => {
      loadRecentAgents()
    })

    return {
      showWizard,
      showHelp,
      showTemplates,
      recentAgents,
      agentTemplates,
      goBack,
      openModelTraining,
      startWizard,
      startAdvanced,
      createFromTemplate,
      onAgentCreated,
      editAgent,
      testAgent,
      viewAllAgents,
      getAgentIcon,
      formatDate
    }
  }
}
</script>

<style scoped>
.agent-studio-new {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

/* 页面头部 */
.studio-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 20px 30px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.header-info h1 {
  color: white;
  margin: 0 0 5px 0;
  font-size: 28px;
  font-weight: 600;
}

.header-info p {
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
  font-size: 16px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.btn-training,
.btn-help {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
}

.btn-training:hover,
.btn-help:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

/* 主要内容 */
.studio-main {
  max-width: 1200px;
  margin: 0 auto;
}

/* 创建模式选择 */
.creation-modes {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}

.modes-header {
  text-align: center;
  margin-bottom: 40px;
}

.modes-header h2 {
  color: #333;
  font-size: 32px;
  margin-bottom: 10px;
  font-weight: 600;
}

.modes-header p {
  color: #666;
  font-size: 18px;
  margin: 0;
}

.modes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
  margin-bottom: 50px;
}

.mode-card {
  border: 2px solid #f0f0f0;
  border-radius: 16px;
  padding: 32px 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  background: white;
}

.mode-card:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(102, 126, 234, 0.15);
}

.mode-card.recommended {
  border-color: #667eea;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f4ff 100%);
}

.mode-badge {
  position: absolute;
  top: -8px;
  right: 16px;
  background: #ff6b35;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.mode-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.mode-title {
  font-size: 24px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
}

.mode-description {
  color: #666;
  line-height: 1.6;
  margin-bottom: 20px;
  font-size: 16px;
}

.mode-features {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 24px;
}

.feature {
  color: #4caf50;
  font-size: 14px;
  font-weight: 500;
}

.mode-action {
  margin-top: auto;
}

.action-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.action-btn.primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.action-btn.primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #333;
  border: 2px solid #e9ecef;
}

.action-btn.secondary:hover {
  background: #e9ecef;
  border-color: #667eea;
}

/* 最近创建的智能体 */
.recent-agents {
  margin-top: 50px;
  padding-top: 30px;
  border-top: 1px solid #f0f0f0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.section-header h3 {
  color: #333;
  font-size: 20px;
  margin: 0;
}

.view-all-btn {
  background: none;
  border: none;
  color: #667eea;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
}

.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.agent-card {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.agent-card:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.agent-avatar {
  font-size: 32px;
  margin-right: 12px;
}

.agent-info {
  flex: 1;
}

.agent-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.agent-type {
  color: #666;
  font-size: 14px;
  margin-bottom: 2px;
}

.agent-date {
  color: #999;
  font-size: 12px;
}

.agent-actions {
  margin-left: 12px;
}

.test-btn {
  background: #4caf50;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.test-btn:hover {
  background: #45a049;
}

/* 向导容器 */
.wizard-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* 帮助内容 */
.help-content {
  max-height: 60vh;
  overflow-y: auto;
}

.help-section {
  margin-bottom: 24px;
}

.help-section h3 {
  color: #333;
  margin-bottom: 12px;
  font-size: 18px;
}

.help-section p {
  color: #666;
  line-height: 1.6;
  margin-bottom: 12px;
}

.help-section ul {
  margin: 0;
  padding-left: 20px;
}

.help-section li {
  color: #666;
  line-height: 1.6;
  margin-bottom: 8px;
}

/* 模板网格 */
.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  max-height: 60vh;
  overflow-y: auto;
}

.template-card {
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.template-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(102, 126, 234, 0.1);
}

.template-icon {
  font-size: 40px;
  margin-bottom: 12px;
}

.template-name {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 8px;
}

.template-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
  margin-bottom: 12px;
}

.template-features {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  justify-content: center;
}

.feature-tag {
  background: #e3f2fd;
  color: #1976d2;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .agent-studio-new {
    padding: 10px;
  }

  .studio-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-left {
    flex-direction: column;
    gap: 12px;
  }

  .creation-modes {
    padding: 20px;
  }

  .modes-grid {
    grid-template-columns: 1fr;
  }

  .agents-grid {
    grid-template-columns: 1fr;
  }

  .templates-grid {
    grid-template-columns: 1fr;
  }
}
</style>
