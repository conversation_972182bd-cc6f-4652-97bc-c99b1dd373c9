# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from __future__ import annotations

from typing import Union
from typing_extensions import TypeAlias

from .beta_text_block_param import BetaTextBlockParam
from .beta_image_block_param import <PERSON><PERSON>mage<PERSON><PERSON>Param
from .beta_thinking_block_param import <PERSON><PERSON><PERSON>king<PERSON><PERSON>Param
from .beta_tool_use_block_param import <PERSON>Tool<PERSON>se<PERSON>lockParam
from .beta_tool_result_block_param import <PERSON>ToolResultBlockParam
from .beta_mcp_tool_use_block_param import <PERSON>MCP<PERSON>oolUse<PERSON>lockParam
from .beta_search_result_block_param import BetaSearchResult<PERSON>lockParam
from .beta_server_tool_use_block_param import <PERSON>S<PERSON>r<PERSON>ool<PERSON>se<PERSON>lockParam
from .beta_container_upload_block_param import <PERSON>ContainerUploadBlockParam
from .beta_request_document_block_param import BetaRequestDocumentBlockParam
from .beta_redacted_thinking_block_param import BetaRedacted<PERSON>hinking<PERSON>lockParam
from .beta_web_search_tool_result_block_param import <PERSON>Web<PERSON>earchToolResult<PERSON>lockParam
from .beta_request_mcp_tool_result_block_param import <PERSON>RequestMCPToolResultBlockParam
from .beta_code_execution_tool_result_block_param import BetaCodeExecutionToolResultBlockParam

__all__ = ["BetaContentBlockParam"]

BetaContentBlockParam: TypeAlias = Union[
    BetaTextBlockParam,
    BetaImageBlockParam,
    BetaRequestDocumentBlockParam,
    BetaSearchResultBlockParam,
    BetaThinkingBlockParam,
    BetaRedactedThinkingBlockParam,
    BetaToolUseBlockParam,
    BetaToolResultBlockParam,
    BetaServerToolUseBlockParam,
    BetaWebSearchToolResultBlockParam,
    BetaCodeExecutionToolResultBlockParam,
    BetaMCPToolUseBlockParam,
    BetaRequestMCPToolResultBlockParam,
    BetaContainerUploadBlockParam,
]
