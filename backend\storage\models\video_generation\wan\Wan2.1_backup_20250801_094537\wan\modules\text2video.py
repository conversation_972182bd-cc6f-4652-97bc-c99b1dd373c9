def _shape_matching_tensor(self, noise_pred, latents):
    """
    匹配形状的处理函数，处理noise_pred和latents形状不匹配的情况
    """
    import logging
    import torch
    import torch.nn.functional as F
    import numpy as np
    
    logger = logging.getLogger("root")
    
    # 记录输入张量的形状和类型
    logger.info(f"指导缩放后的noise_pred形状: {noise_pred.shape}, 数据类型: {noise_pred.dtype}")
    logger.info(f"latents[0]形状: {latents[0].shape}, 数据类型: {latents[0].dtype}")
    
    # 记录维度数量
    noise_pred_ndim = len(noise_pred.shape)
    latents_ndim = len(latents[0].shape)
    logger.info(f"张量维度数量: noise_pred={noise_pred_ndim}, latents[0]={latents_ndim}")
    
    # 检查形状是否匹配
    if noise_pred.shape != latents[0].shape:
        logger.info(f"检测到形状不匹配: noise_pred={noise_pred.shape}, latents[0]={latents[0].shape}")
        logger.info("使用改进的形状匹配算法")
        
        try:
            # 检查维度数量是否一致
            if noise_pred_ndim != latents_ndim:
                logger.info(f"维度数量不匹配: noise_pred={noise_pred_ndim}D, latents[0]={latents_ndim}D")
                
                # 尝试重塑张量
                noise_pred_size = np.prod(noise_pred.shape)
                latents_size = np.prod(latents[0].shape)
                
                if noise_pred_size == latents_size:
                    # 元素数量相同，可以重塑
                    noise_pred = noise_pred.reshape(latents[0].shape)
                    logger.info(f"重塑后noise_pred形状: {noise_pred.shape}")
                else:
                    # 元素数量不同，需要插值或裁剪
                    logger.info(f"无法精确重塑，元素数量不同: noise_pred={noise_pred_size}, latents={latents_size}")
                    
                    # 根据维度数量判断处理方法
                    if noise_pred_ndim == 5 and latents_ndim == 4:
                        # 尝试处理5D -> 4D (可能是视频模型)
                        try:
                            # 重新排列维度并展平前两个维度
                            b, c, t, h, w = noise_pred.shape
                            noise_pred = noise_pred.permute(0, 2, 1, 3, 4).reshape(b, t*c, h, w)
                            
                            # 如果维度还是不匹配，使用插值
                            if noise_pred.shape != latents[0].shape:
                                _, c_tgt, h_tgt, w_tgt = latents[0].shape
                                noise_pred = F.interpolate(
                                    noise_pred, 
                                    size=(h_tgt, w_tgt),
                                    mode='bilinear', 
                                    align_corners=False
                                )
                                
                                # 调整通道数
                                if noise_pred.shape[1] != c_tgt:
                                    # 如果通道数较多，截取前c_tgt个通道
                                    if noise_pred.shape[1] > c_tgt:
                                        noise_pred = noise_pred[:, :c_tgt, :, :]
                                    # 如果通道数较少，补充零通道
                                    else:
                                        padding = torch.zeros(
                                            (noise_pred.shape[0], c_tgt - noise_pred.shape[1], *noise_pred.shape[2:]),
                                            device=noise_pred.device,
                                            dtype=noise_pred.dtype
                                        )
                                        noise_pred = torch.cat([noise_pred, padding], dim=1)
                            
                            logger.info(f"5D->4D处理后noise_pred形状: {noise_pred.shape}")
                        except Exception as e:
                            logger.error(f"形状调整失败: {str(e)}")
                            # 创建随机噪声作为替代
                            noise_pred = torch.randn_like(latents[0])
                            logger.warning(f"使用随机噪声作为noise_pred，形状: {noise_pred.shape}")
                    else:
                        # 其他维度不匹配情况
                        # 创建随机噪声作为替代
                        noise_pred = torch.randn_like(latents[0])
                        logger.warning(f"使用随机噪声作为noise_pred，形状: {noise_pred.shape}")
            else:
                # 维度数量相同但形状不同
                try:
                    # 检查是否可以通过插值调整
                    if noise_pred_ndim == 4:  # 对于4D张量，可以用空间插值
                        _, c_tgt, h_tgt, w_tgt = latents[0].shape
                        
                        # 调整空间尺寸
                        noise_pred = F.interpolate(
                            noise_pred, 
                            size=(h_tgt, w_tgt),
                            mode='bilinear', 
                            align_corners=False
                        )
                        
                        # 调整通道数
                        if noise_pred.shape[1] != c_tgt:
                            # 如果通道数较多，截取前c_tgt个通道
                            if noise_pred.shape[1] > c_tgt:
                                noise_pred = noise_pred[:, :c_tgt, :, :]
                            # 如果通道数较少，补充零通道
                            else:
                                padding = torch.zeros(
                                    (noise_pred.shape[0], c_tgt - noise_pred.shape[1], *noise_pred.shape[2:]),
                                    device=noise_pred.device,
                                    dtype=noise_pred.dtype
                                )
                                noise_pred = torch.cat([noise_pred, padding], dim=1)
                        
                        logger.info(f"插值后noise_pred形状: {noise_pred.shape}")
                    else:
                        # 不支持的维度，使用随机噪声
                        noise_pred = torch.randn_like(latents[0])
                        logger.warning(f"不支持的维度插值，使用随机噪声，形状: {noise_pred.shape}")
                except Exception as e:
                    logger.error(f"形状调整失败: {str(e)}")
                    # 创建随机噪声作为替代
                    noise_pred = torch.randn_like(latents[0])
                    logger.warning(f"使用随机噪声作为noise_pred，形状: {noise_pred.shape}")
        except Exception as e:
            logger.error(f"形状匹配过程中发生错误: {str(e)}")
            # 失败时使用随机噪声
            noise_pred = torch.randn_like(latents[0])
            logger.warning(f"使用随机噪声作为noise_pred，形状: {noise_pred.shape}")
    
    # 记录最终准备传入scheduler的张量形状
    logger.info(f"准备传入scheduler的张量形状: noise_pred={noise_pred.shape}, latents[0]={latents[0].shape}")
    
    # 确保张量维度一致
    if len(noise_pred.shape) != len(latents[0].shape):
        logger.warning(f"最终维度仍不匹配: noise_pred={len(noise_pred.shape)}D, latents[0]={len(latents[0].shape)}D")
        # 紧急情况下使用随机噪声
        noise_pred = torch.randn_like(latents[0])
        logger.warning(f"使用随机噪声作为最终结果，形状: {noise_pred.shape}")
    
    # 添加批量维度以匹配scheduler要求的形状
    unsqueezed_noise_pred = noise_pred.unsqueeze(0)
    unsqueezed_latents = latents[0].unsqueeze(0)
    logger.info(f"Unsqueezed张量形状: noise_pred={unsqueezed_noise_pred.shape}, latents[0]={unsqueezed_latents.shape}")
    
    return unsqueezed_noise_pred, unsqueezed_latents

def decode_latents(self, latents):
    """
    解码潜在变量为视频帧
    """
    import logging
    import torch
    import torch.nn.functional as F
    import numpy as np
    
    logger = logging.getLogger("root")
    
    try:
        # 如果latents是列表，取第一个元素
        if isinstance(latents, list):
            logger.info(f"解码前x0的类型: {type(latents)}")
            logger.info(f"从列表中提取张量，原始x0长度: {len(latents)}")
            x0_tensor = latents[0]
            logger.info(f"提取的x0_tensor形状: {x0_tensor.shape}")
        else:
            x0_tensor = latents
        
        # 记录输入范围
        min_val = x0_tensor.min().item()
        max_val = x0_tensor.max().item()
        logger.info(f"VAE decode input shape: {x0_tensor.shape}, range: [{min_val:.4f}, {max_val:.4f}]")
        
        # 使用VAE解码
        video = self.vae.decode(x0_tensor)
        
        # 记录输出范围
        min_val = video.min().item()
        max_val = video.max().item()
        logger.info(f"VAE decoded video range: [{min_val:.4f}, {max_val:.4f}]")
        logger.info(f"VAE解码成功，输出videos形状: {video.shape}")
        
        return video
        
    except Exception as e:
        logger.error(f"Error in VAE decode: {str(e)}")
        
        # 记录异常情况的输入形状
        if isinstance(latents, list):
            shapes = [x.shape if hasattr(x, 'shape') else type(x) for x in latents]
            logger.error(f"Error with latent list shapes: {shapes}")
        else:
            shape_info = latents.shape if hasattr(latents, 'shape') else type(latents)
            logger.error(f"Error with latent shape: {shape_info}")
        
        # 创建随机噪声作为替代输出
        if hasattr(latents, 'shape') and len(latents.shape) >= 4:
            # 基于潜在空间形状创建随机视频帧
            b = latents.shape[0] if isinstance(latents, torch.Tensor) else 1
            h = 720  # 根据需要调整
            w = 1280 # 根据需要调整
            c = 3    # RGB通道
            
            random_video = torch.rand((b, c, h, w), device=self.device) - 0.5
            logger.warning(f"Using random noise as VAE output, shape: {random_video.shape}")
            return random_video
        else:
            # 创建一个默认形状的随机视频
            random_video = torch.rand((1, 3, 720, 1280), device=self.device) - 0.5
            logger.warning(f"Using default random noise as VAE output, shape: {random_video.shape}")
            return random_video 