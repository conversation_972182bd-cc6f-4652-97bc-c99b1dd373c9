<template>
  <div class="knowledge-explanation">
    <el-card class="explanation-card">
      <template #header>
        <div class="card-header">
          <span>💡 知识库 vs 模型训练</span>
          <el-button @click="showDialog = true" type="primary" size="small">详细说明</el-button>
        </div>
      </template>
      
      <div class="quick-comparison">
        <div class="comparison-item">
          <div class="item-icon">📚</div>
          <div class="item-content">
            <h4>知识库（RAG）</h4>
            <p>实时检索相关文档，动态提供上下文信息</p>
            <div class="tags">
              <el-tag size="small">实时更新</el-tag>
              <el-tag size="small" type="success">事实性信息</el-tag>
            </div>
          </div>
        </div>
        
        <div class="comparison-item">
          <div class="item-icon">🎓</div>
          <div class="item-content">
            <h4>模型训练</h4>
            <p>改变模型回答风格和专业能力，形成专业人格</p>
            <div class="tags">
              <el-tag size="small" type="warning">深度定制</el-tag>
              <el-tag size="small" type="info">专业角色</el-tag>
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 详细说明对话框 -->
    <el-dialog v-model="showDialog" title="知识库 vs 模型训练 详细对比" width="800px">
      <div class="detailed-explanation">
        <el-tabs v-model="activeTab">
          <el-tab-pane label="知识库（RAG）" name="knowledge">
            <div class="tab-content">
              <h3>🔍 检索增强生成（RAG）</h3>
              
              <div class="feature-section">
                <h4>工作原理：</h4>
                <ol>
                  <li>用户提问时，系统自动搜索相关文档</li>
                  <li>将找到的相关信息作为上下文提供给模型</li>
                  <li>模型基于检索到的信息生成回答</li>
                </ol>
              </div>

              <div class="feature-section">
                <h4>优势：</h4>
                <ul>
                  <li>✅ 可以实时更新知识，无需重新训练</li>
                  <li>✅ 适合处理大量文档和事实性信息</li>
                  <li>✅ 可以引用具体的文档来源</li>
                  <li>✅ 成本低，响应快</li>
                </ul>
              </div>

              <div class="feature-section">
                <h4>适用场景：</h4>
                <ul>
                  <li>📋 企业内部文档查询</li>
                  <li>📖 产品手册和说明书</li>
                  <li>📰 新闻资讯和时事信息</li>
                  <li>🔬 研究论文和学术资料</li>
                </ul>
              </div>

              <div class="example-section">
                <h4>使用示例：</h4>
                <div class="example-box">
                  <div class="user-message">用户：公司的请假政策是什么？</div>
                  <div class="system-process">系统：搜索"请假政策"相关文档 → 找到《员工手册》第3章</div>
                  <div class="ai-response">AI：根据《员工手册》第3章，公司请假政策如下：年假15天，病假30天...</div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="模型训练" name="training">
            <div class="tab-content">
              <h3>🎓 模型微调/训练</h3>
              
              <div class="feature-section">
                <h4>工作原理：</h4>
                <ol>
                  <li>使用专业对话数据训练模型</li>
                  <li>调整模型的参数和权重</li>
                  <li>形成特定的回答风格和专业能力</li>
                </ol>
              </div>

              <div class="feature-section">
                <h4>优势：</h4>
                <ul>
                  <li>✅ 深度定制模型行为和人格</li>
                  <li>✅ 形成专业的回答风格</li>
                  <li>✅ 提升特定领域的推理能力</li>
                  <li>✅ 无需外部文档即可展现专业性</li>
                </ul>
              </div>

              <div class="feature-section">
                <h4>适用场景：</h4>
                <ul>
                  <li>👨‍🏫 专业教师：耐心教学，因材施教</li>
                  <li>👨‍⚕️ 医疗顾问：专业严谨，关怀体贴</li>
                  <li>👨‍💼 法律顾问：逻辑清晰，风险提醒</li>
                  <li>🎨 创意助手：富有想象力和创造性</li>
                </ul>
              </div>

              <div class="example-section">
                <h4>使用示例：</h4>
                <div class="example-box">
                  <div class="user-message">用户：我不理解这个数学概念</div>
                  <div class="system-process">训练后的教师模型：展现耐心教学的人格特质</div>
                  <div class="ai-response">AI：没关系，让我们一步步来理解。首先，你能告诉我你已经知道的部分吗？这样我可以从你熟悉的地方开始解释...</div>
                </div>
              </div>
            </div>
          </el-tab-pane>

          <el-tab-pane label="结合使用" name="combination">
            <div class="tab-content">
              <h3>🚀 最佳实践：结合使用</h3>
              
              <div class="combination-diagram">
                <div class="step">
                  <div class="step-number">1</div>
                  <div class="step-content">
                    <h4>模型训练</h4>
                    <p>创建专业教师模型，具备耐心教学的人格</p>
                  </div>
                </div>
                <div class="arrow">→</div>
                <div class="step">
                  <div class="step-number">2</div>
                  <div class="step-content">
                    <h4>知识库</h4>
                    <p>上传最新的教学材料、课程内容</p>
                  </div>
                </div>
                <div class="arrow">→</div>
                <div class="step">
                  <div class="step-number">3</div>
                  <div class="step-content">
                    <h4>完美结合</h4>
                    <p>专业的教师人格 + 最新的教学内容</p>
                  </div>
                </div>
              </div>

              <div class="feature-section">
                <h4>结合使用的优势：</h4>
                <ul>
                  <li>🎯 专业的回答风格 + 准确的事实信息</li>
                  <li>🔄 稳定的人格特质 + 动态的知识更新</li>
                  <li>💡 深度的专业理解 + 广泛的信息覆盖</li>
                  <li>⚡ 高质量的用户体验</li>
                </ul>
              </div>

              <div class="recommendation">
                <h4>💡 推荐方案：</h4>
                <div class="recommendation-item">
                  <strong>教师智能体：</strong>
                  <p>训练专业教师模型 + 上传教学大纲、习题库、参考资料</p>
                </div>
                <div class="recommendation-item">
                  <strong>客服智能体：</strong>
                  <p>训练友好客服模型 + 上传产品手册、FAQ、政策文档</p>
                </div>
                <div class="recommendation-item">
                  <strong>技术顾问：</strong>
                  <p>训练专业技术模型 + 上传技术文档、API文档、最佳实践</p>
                </div>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
      
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showDialog = false">关闭</el-button>
          <el-button type="primary" @click="goToTraining">开始模型训练</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'KnowledgeBaseExplanation',
  setup() {
    const router = useRouter()
    const showDialog = ref(false)
    const activeTab = ref('knowledge')

    const goToTraining = () => {
      showDialog.value = false
      window.open('/model-training', '_blank')
    }

    return {
      showDialog,
      activeTab,
      goToTraining
    }
  }
}
</script>

<style scoped>
.knowledge-explanation {
  margin-bottom: 20px;
}

.explanation-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
  color: #409EFF;
}

.quick-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.comparison-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 15px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  background: #fafafa;
}

.item-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.item-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.item-content p {
  margin: 0 0 10px 0;
  color: #606266;
  font-size: 14px;
  line-height: 1.4;
}

.tags {
  display: flex;
  gap: 6px;
}

.detailed-explanation {
  max-height: 60vh;
  overflow-y: auto;
}

.tab-content {
  padding: 20px 0;
}

.tab-content h3 {
  color: #409EFF;
  margin-bottom: 20px;
}

.feature-section {
  margin-bottom: 25px;
}

.feature-section h4 {
  color: #303133;
  margin-bottom: 10px;
  font-size: 16px;
}

.feature-section ul,
.feature-section ol {
  margin: 0;
  padding-left: 20px;
}

.feature-section li {
  margin-bottom: 8px;
  color: #606266;
  line-height: 1.5;
}

.example-section {
  margin-top: 25px;
}

.example-box {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 15px;
  margin-top: 10px;
}

.user-message {
  background: #e3f2fd;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  color: #1976d2;
}

.system-process {
  background: #fff3e0;
  padding: 8px 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  color: #f57c00;
  font-style: italic;
}

.ai-response {
  background: #e8f5e8;
  padding: 8px 12px;
  border-radius: 4px;
  color: #388e3c;
}

.combination-diagram {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 30px 0;
  flex-wrap: wrap;
  gap: 20px;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 200px;
}

.step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #409EFF;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-bottom: 10px;
}

.step-content h4 {
  margin: 0 0 8px 0;
  color: #303133;
}

.step-content p {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.arrow {
  font-size: 24px;
  color: #409EFF;
  font-weight: bold;
}

.recommendation {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 20px;
  margin-top: 20px;
}

.recommendation h4 {
  color: #0369a1;
  margin-bottom: 15px;
}

.recommendation-item {
  margin-bottom: 15px;
  padding: 10px;
  background: white;
  border-radius: 4px;
}

.recommendation-item strong {
  color: #0369a1;
}

.recommendation-item p {
  margin: 5px 0 0 0;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

@media (max-width: 768px) {
  .quick-comparison {
    grid-template-columns: 1fr;
  }
  
  .combination-diagram {
    flex-direction: column;
  }
  
  .arrow {
    transform: rotate(90deg);
  }
}
</style>
