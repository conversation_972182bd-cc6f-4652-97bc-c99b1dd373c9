<template>
  <div class="knowledge-base-manager">
    <div class="manager-header">
      <h3>📚 知识库管理</h3>
      <p>管理专业知识文档，支持智能检索和问答</p>
    </div>

    <!-- 知识库选择 -->
    <div class="kb-selection">
      <div class="selection-header">
        <h4>选择知识库类型</h4>
        <el-radio-group v-model="kbType" @change="onKbTypeChange">
          <el-radio label="translation">翻译知识库</el-radio>
          <el-radio label="qa">问答知识库</el-radio>
          <el-radio label="document">文档知识库</el-radio>
          <el-radio label="custom">自定义知识库</el-radio>
        </el-radio-group>
      </div>
      
      <div class="type-description">
        <div v-if="kbType === 'translation'" class="type-info">
          <h5>🌐 翻译知识库</h5>
          <p>专门用于存储翻译对照数据，支持多语言翻译查询</p>
          <ul>
            <li>支持Excel批量导入翻译对照表</li>
            <li>自动建立双向索引（中→英，英→中）</li>
            <li>支持专业术语翻译</li>
            <li>适合：专业翻译、术语管理</li>
          </ul>
        </div>
        
        <div v-if="kbType === 'qa'" class="type-info">
          <h5>❓ 问答知识库</h5>
          <p>存储常见问题和标准答案，支持智能问答匹配</p>
          <ul>
            <li>智能问题相似度匹配</li>
            <li>支持一问多答</li>
            <li>自动问题分类</li>
            <li>适合：客服、FAQ、专业咨询</li>
          </ul>
        </div>
        
        <div v-if="kbType === 'document'" class="type-info">
          <h5>📄 文档知识库</h5>
          <p>存储长文档，支持文档检索和内容问答</p>
          <ul>
            <li>支持PDF、Word、TXT文档</li>
            <li>自动文档分段和索引</li>
            <li>基于文档内容的智能问答</li>
            <li>适合：手册、规范、教材</li>
          </ul>
        </div>
        
        <div v-if="kbType === 'custom'" class="type-info">
          <h5>🔧 自定义知识库</h5>
          <p>灵活的知识存储，支持自定义结构</p>
          <ul>
            <li>自定义字段结构</li>
            <li>支持多种数据格式</li>
            <li>灵活的检索方式</li>
            <li>适合：特殊业务需求</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- 翻译知识库管理 -->
    <div v-if="kbType === 'translation'" class="kb-content">
      <div class="content-header">
        <h4>🌐 翻译知识库</h4>
        <div class="header-actions">
          <el-button @click="showImportDialog = true" type="primary">
            <el-icon><upload /></el-icon>
            导入Excel
          </el-button>
          <el-button @click="exportTranslations">
            <el-icon><download /></el-icon>
            导出数据
          </el-button>
        </div>
      </div>
      
      <!-- 翻译数据表格 -->
      <div class="translation-table">
        <el-table :data="translations" max-height="400">
          <el-table-column prop="source" label="原文" min-width="200" show-overflow-tooltip />
          <el-table-column prop="target" label="译文" min-width="200" show-overflow-tooltip />
          <el-table-column prop="category" label="分类" width="120" />
          <el-table-column prop="domain" label="领域" width="120" />
          <el-table-column label="操作" width="120">
            <template #default="{ row, $index }">
              <el-button @click="editTranslation($index)" size="small">编辑</el-button>
              <el-button @click="deleteTranslation($index)" size="small" type="danger">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      
      <!-- 翻译统计 -->
      <div class="translation-stats">
        <div class="stat-item">
          <span class="stat-label">总条目：</span>
          <span class="stat-value">{{ translations.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">分类数：</span>
          <span class="stat-value">{{ uniqueCategories.length }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">领域数：</span>
          <span class="stat-value">{{ uniqueDomains.length }}</span>
        </div>
      </div>
    </div>

    <!-- 问答知识库管理 -->
    <div v-if="kbType === 'qa'" class="kb-content">
      <div class="content-header">
        <h4>❓ 问答知识库</h4>
        <div class="header-actions">
          <el-button @click="addQA" type="primary">
            <el-icon><plus /></el-icon>
            添加问答
          </el-button>
          <el-button @click="importQAFromExcel">
            <el-icon><upload /></el-icon>
            Excel导入
          </el-button>
        </div>
      </div>
      
      <!-- 问答列表 -->
      <div class="qa-list">
        <div v-for="(qa, index) in qaList" :key="index" class="qa-item">
          <div class="qa-question">
            <strong>问：</strong>{{ qa.question }}
          </div>
          <div class="qa-answer">
            <strong>答：</strong>{{ qa.answer }}
          </div>
          <div class="qa-meta">
            <span v-if="qa.category" class="qa-category">{{ qa.category }}</span>
            <span class="qa-actions">
              <el-button @click="editQA(index)" size="small">编辑</el-button>
              <el-button @click="deleteQA(index)" size="small" type="danger">删除</el-button>
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 文档知识库管理 -->
    <div v-if="kbType === 'document'" class="kb-content">
      <div class="content-header">
        <h4>📄 文档知识库</h4>
        <div class="header-actions">
          <el-upload
            :auto-upload="false"
            :on-change="handleDocumentUpload"
            accept=".pdf,.doc,.docx,.txt"
            :show-file-list="false"
          >
            <el-button type="primary">
              <el-icon><upload /></el-icon>
              上传文档
            </el-button>
          </el-upload>
        </div>
      </div>
      
      <!-- 文档列表 -->
      <div class="document-list">
        <div v-for="(doc, index) in documents" :key="index" class="document-item">
          <div class="doc-icon">📄</div>
          <div class="doc-info">
            <div class="doc-name">{{ doc.name }}</div>
            <div class="doc-meta">
              <span>大小：{{ formatFileSize(doc.size) }}</span>
              <span>类型：{{ doc.type }}</span>
              <span>上传时间：{{ formatDate(doc.uploadTime) }}</span>
            </div>
          </div>
          <div class="doc-actions">
            <el-button @click="processDocument(index)" size="small" type="primary">处理</el-button>
            <el-button @click="deleteDocument(index)" size="small" type="danger">删除</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- Excel导入对话框 -->
    <el-dialog v-model="showImportDialog" title="Excel数据导入" width="600px">
      <div class="import-dialog-content">
        <el-upload
          ref="excelUpload"
          :auto-upload="false"
          :on-change="handleExcelImport"
          accept=".xlsx,.xls"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将Excel文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              <p>翻译知识库Excel格式要求：</p>
              <ul>
                <li>第一列：原文</li>
                <li>第二列：译文</li>
                <li>第三列：分类（可选）</li>
                <li>第四列：领域（可选）</li>
              </ul>
            </div>
          </template>
        </el-upload>
        
        <!-- 导入预览 -->
        <div v-if="importPreview.length > 0" class="import-preview">
          <h4>导入预览 (前10条)</h4>
          <el-table :data="importPreview.slice(0, 10)" max-height="300">
            <el-table-column prop="source" label="原文" />
            <el-table-column prop="target" label="译文" />
            <el-table-column prop="category" label="分类" />
            <el-table-column prop="domain" label="领域" />
          </el-table>
          <p>共{{ importPreview.length }}条数据</p>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="showImportDialog = false">取消</el-button>
        <el-button @click="confirmImport" type="primary" :disabled="importPreview.length === 0">
          导入{{ importPreview.length }}条数据
        </el-button>
      </template>
    </el-dialog>

    <!-- 知识库vs模型训练对比 -->
    <div class="comparison-section">
      <h4>🤔 知识库 vs 模型训练 - 如何选择？</h4>
      <div class="comparison-grid">
        <div class="comparison-item">
          <h5>📚 选择知识库的情况</h5>
          <ul>
            <li><strong>大量结构化数据</strong>：如您的Excel翻译表</li>
            <li><strong>需要精确匹配</strong>：专业术语翻译</li>
            <li><strong>数据经常更新</strong>：可随时添加新内容</li>
            <li><strong>多种查询方式</strong>：关键词、分类检索</li>
            <li><strong>数据可追溯</strong>：知道答案来源</li>
          </ul>
          <div class="recommendation">
            <strong>推荐用于：</strong>专业翻译、术语管理、FAQ、文档检索
          </div>
        </div>
        
        <div class="comparison-item">
          <h5>🤖 选择模型训练的情况</h5>
          <ul>
            <li><strong>对话式交互</strong>：自然语言对话</li>
            <li><strong>创造性回答</strong>：需要灵活表达</li>
            <li><strong>上下文理解</strong>：理解对话历史</li>
            <li><strong>个性化风格</strong>：特定的回答风格</li>
            <li><strong>推理能力</strong>：基于知识推理</li>
          </ul>
          <div class="recommendation">
            <strong>推荐用于：</strong>智能客服、教学助手、创意写作、专业咨询
          </div>
        </div>
      </div>
      
      <div class="hybrid-approach">
        <h5>🔄 混合方案（推荐）</h5>
        <p>对于您的翻译场景，建议采用<strong>知识库 + 模型训练</strong>的混合方案：</p>
        <ol>
          <li><strong>知识库</strong>：存储Excel中的精确翻译对照</li>
          <li><strong>模型训练</strong>：训练翻译风格和表达方式</li>
          <li><strong>智能路由</strong>：精确匹配用知识库，创意翻译用模型</li>
        </ol>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Upload, Download, Plus, UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'

export default {
  name: 'KnowledgeBaseManager',
  components: {
    Upload,
    Download,
    Plus,
    UploadFilled
  },
  setup() {
    const kbType = ref('translation')
    const showImportDialog = ref(false)
    
    // 翻译知识库数据
    const translations = ref([])
    const importPreview = ref([])
    
    // 问答知识库数据
    const qaList = ref([])
    
    // 文档知识库数据
    const documents = ref([])
    
    // 计算属性
    const uniqueCategories = computed(() => {
      return [...new Set(translations.value.map(t => t.category).filter(Boolean))]
    })
    
    const uniqueDomains = computed(() => {
      return [...new Set(translations.value.map(t => t.domain).filter(Boolean))]
    })
    
    // 知识库类型切换
    const onKbTypeChange = (type) => {
      console.log('切换到知识库类型:', type)
    }
    
    // Excel导入处理
    const handleExcelImport = (file) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result)
          const workbook = XLSX.read(data, { type: 'array' })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          const jsonData = XLSX.utils.sheet_to_json(worksheet)
          
          // 转换为翻译格式
          importPreview.value = jsonData.map(row => {
            const keys = Object.keys(row)
            return {
              source: row[keys[0]] || '',
              target: row[keys[1]] || '',
              category: row[keys[2]] || '',
              domain: row[keys[3]] || ''
            }
          }).filter(item => item.source && item.target)
          
          ElMessage.success(`解析成功，共${importPreview.value.length}条翻译数据`)
        } catch (error) {
          ElMessage.error('Excel文件解析失败')
          console.error(error)
        }
      }
      reader.readAsArrayBuffer(file.raw)
    }
    
    // 确认导入
    const confirmImport = () => {
      translations.value.push(...importPreview.value)
      ElMessage.success(`成功导入${importPreview.value.length}条翻译数据`)
      showImportDialog.value = false
      importPreview.value = []
    }
    
    // 导出翻译数据
    const exportTranslations = () => {
      const ws = XLSX.utils.json_to_sheet(translations.value)
      const wb = XLSX.utils.book_new()
      XLSX.utils.book_append_sheet(wb, ws, '翻译数据')
      XLSX.writeFile(wb, '翻译知识库.xlsx')
    }
    
    // 翻译管理
    const editTranslation = (index) => {
      console.log('编辑翻译:', index)
    }
    
    const deleteTranslation = (index) => {
      translations.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
    
    // 问答管理
    const addQA = () => {
      qaList.value.push({
        question: '',
        answer: '',
        category: ''
      })
    }
    
    const editQA = (index) => {
      console.log('编辑问答:', index)
    }
    
    const deleteQA = (index) => {
      qaList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
    
    const importQAFromExcel = () => {
      console.log('从Excel导入问答')
    }
    
    // 文档管理
    const handleDocumentUpload = (file) => {
      documents.value.push({
        name: file.name,
        size: file.size,
        type: file.name.split('.').pop(),
        uploadTime: new Date(),
        file: file.raw
      })
      ElMessage.success('文档上传成功')
    }
    
    const processDocument = (index) => {
      console.log('处理文档:', index)
      ElMessage.info('文档处理功能开发中...')
    }
    
    const deleteDocument = (index) => {
      documents.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
    
    // 工具函数
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
    
    const formatDate = (date) => {
      return date.toLocaleDateString('zh-CN')
    }
    
    return {
      kbType,
      showImportDialog,
      translations,
      importPreview,
      qaList,
      documents,
      uniqueCategories,
      uniqueDomains,
      onKbTypeChange,
      handleExcelImport,
      confirmImport,
      exportTranslations,
      editTranslation,
      deleteTranslation,
      addQA,
      editQA,
      deleteQA,
      importQAFromExcel,
      handleDocumentUpload,
      processDocument,
      deleteDocument,
      formatFileSize,
      formatDate
    }
  }
}
</script>

<style scoped>
.knowledge-base-manager {
  max-width: 1000px;
  margin: 0 auto;
}

.manager-header h3 {
  margin-bottom: 0.5rem;
  color: #1a202c;
}

.manager-header p {
  color: #64748b;
  margin-bottom: 2rem;
}

.kb-selection {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
}

.selection-header h4 {
  margin-bottom: 1rem;
  color: #1a202c;
}

.type-description {
  margin-top: 1rem;
}

.type-info {
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.type-info h5 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.type-info p {
  margin: 0 0 1rem 0;
  color: #4a5568;
}

.type-info ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #64748b;
}

.kb-content {
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  margin-bottom: 2rem;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.content-header h4 {
  margin: 0;
  color: #1a202c;
}

.translation-stats {
  display: flex;
  gap: 2rem;
  margin-top: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.stat-label {
  color: #64748b;
}

.stat-value {
  font-weight: 600;
  color: #1a202c;
}

.qa-item {
  margin-bottom: 1rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
}

.qa-question {
  margin-bottom: 0.5rem;
  color: #1a202c;
}

.qa-answer {
  margin-bottom: 0.5rem;
  color: #4a5568;
}

.qa-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.qa-category {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.document-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.doc-icon {
  font-size: 2rem;
  margin-right: 1rem;
}

.doc-info {
  flex: 1;
}

.doc-name {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.doc-meta {
  font-size: 0.9rem;
  color: #64748b;
}

.doc-meta span {
  margin-right: 1rem;
}

.comparison-section {
  margin-top: 2rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
}

.comparison-section h4 {
  margin-bottom: 1rem;
  color: #1a202c;
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

.comparison-item {
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.comparison-item h5 {
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.comparison-item ul {
  margin: 0 0 1rem 0;
  padding-left: 1.5rem;
  color: #4a5568;
}

.recommendation {
  padding: 0.75rem;
  background: #f0f9ff;
  border-radius: 6px;
  color: #0369a1;
  font-size: 0.9rem;
}

.hybrid-approach {
  padding: 1rem;
  background: #f0fdf4;
  border-radius: 8px;
  border: 1px solid #bbf7d0;
}

.hybrid-approach h5 {
  margin: 0 0 0.5rem 0;
  color: #166534;
}

.hybrid-approach p {
  margin: 0 0 1rem 0;
  color: #166534;
}

.hybrid-approach ol {
  margin: 0;
  padding-left: 1.5rem;
  color: #166534;
}
</style>
