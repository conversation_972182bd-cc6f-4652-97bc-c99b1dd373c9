<template>
  <div class="gpu-diagnostic">
    <div class="page-header">
      <h1>🔧 GPU诊断与优化</h1>
      <p>检查GPU状态，诊断视频生成问题，获取优化建议</p>
    </div>

    <!-- 快速状态卡片 -->
    <div class="status-cards">
      <div class="status-card" :class="{ 'status-error': !gpuStatus.available, 'status-success': gpuStatus.available }">
        <div class="card-icon">
          {{ gpuStatus.available ? '✅' : '❌' }}
        </div>
        <div class="card-content">
          <h3>GPU状态</h3>
          <p>{{ gpuStatus.available ? 'GPU可用' : 'GPU不可用' }}</p>
          <div v-if="gpuStatus.available && gpuStatus.device_name" class="gpu-name">
            {{ gpuStatus.device_name }}
          </div>
        </div>
      </div>

      <div class="status-card">
        <div class="card-icon">📊</div>
        <div class="card-content">
          <h3>内存使用</h3>
          <div v-if="gpuStatus.available">
            <div class="memory-bar">
              <div class="memory-used" :style="{ width: memoryUsagePercent + '%' }"></div>
            </div>
            <p>{{ gpuStatus.memory_free_gb?.toFixed(1) }}GB 可用 / {{ gpuStatus.memory_total_gb?.toFixed(1) }}GB 总计</p>
          </div>
          <p v-else>GPU不可用</p>
        </div>
      </div>

      <div class="status-card">
        <div class="card-icon">🌡️</div>
        <div class="card-content">
          <h3>GPU温度</h3>
          <p v-if="gpuUsage.temperature">{{ gpuUsage.temperature }}°C</p>
          <p v-else>无法获取</p>
        </div>
      </div>

      <div class="status-card">
        <div class="card-icon">⚡</div>
        <div class="card-content">
          <h3>GPU利用率</h3>
          <p v-if="gpuUsage.utilization">{{ gpuUsage.utilization }}%</p>
          <p v-else>无法获取</p>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons">
      <el-button @click="refreshStatus" :loading="loading" type="primary">
        <el-icon><refresh /></el-icon>
        刷新状态
      </el-button>
      <el-button @click="runDiagnostic" :loading="diagnosing" type="warning">
        <el-icon><search /></el-icon>
        完整诊断
      </el-button>
      <el-button @click="testGPU" :loading="testing">
        <el-icon><cpu /></el-icon>
        GPU功能测试
      </el-button>
      <el-button @click="cleanupMemory" :loading="cleaning">
        <el-icon><delete /></el-icon>
        清理内存
      </el-button>
    </div>

    <!-- 诊断结果 -->
    <div v-if="diagnosticResults" class="diagnostic-results">
      <h2>🔍 诊断结果</h2>
      
      <!-- 问题列表 -->
      <div v-if="diagnosticResults.issues && diagnosticResults.issues.length > 0" class="issues-section">
        <h3>发现的问题</h3>
        <div class="issues-list">
          <div 
            v-for="(issue, index) in diagnosticResults.issues" 
            :key="index"
            :class="['issue-item', `issue-${issue.severity}`]"
          >
            <div class="issue-icon">
              {{ issue.severity === 'critical' ? '🚨' : issue.severity === 'warning' ? '⚠️' : 'ℹ️' }}
            </div>
            <div class="issue-content">
              <div class="issue-type">{{ issue.type }}</div>
              <div class="issue-message">{{ issue.message }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 推荐建议 -->
      <div v-if="diagnosticResults.recommendations" class="recommendations-section">
        <h3>优化建议</h3>
        <div class="recommendations-list">
          <div 
            v-for="(rec, index) in diagnosticResults.recommendations" 
            :key="index"
            class="recommendation-item"
          >
            <div class="rec-header">
              <span class="rec-priority" :class="`priority-${rec.priority}`">
                {{ rec.priority === 'high' ? '高优先级' : rec.priority === 'medium' ? '中优先级' : '低优先级' }}
              </span>
              <h4>{{ rec.title }}</h4>
            </div>
            <div class="rec-actions">
              <ul>
                <li v-for="(action, actionIndex) in rec.actions" :key="actionIndex">
                  {{ action }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速修复 -->
      <div v-if="quickFixes && quickFixes.length > 0" class="quick-fixes-section">
        <h3>快速修复命令</h3>
        <div class="fixes-list">
          <div v-for="(fix, index) in quickFixes" :key="index" class="fix-item">
            <code>{{ fix }}</code>
            <el-button @click="copyToClipboard(fix)" size="small">复制</el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- GPU测试结果 -->
    <div v-if="testResults" class="test-results">
      <h2>🧪 GPU功能测试结果</h2>
      <div class="test-status" :class="testResults.test_results.basic_operations ? 'test-success' : 'test-error'">
        {{ testResults.status }}
      </div>
      
      <div class="test-details">
        <div class="test-item">
          <span class="test-label">CUDA可用:</span>
          <span :class="testResults.test_results.cuda_available ? 'test-pass' : 'test-fail'">
            {{ testResults.test_results.cuda_available ? '✅ 是' : '❌ 否' }}
          </span>
        </div>
        <div class="test-item">
          <span class="test-label">内存分配:</span>
          <span :class="testResults.test_results.memory_allocation ? 'test-pass' : 'test-fail'">
            {{ testResults.test_results.memory_allocation ? '✅ 通过' : '❌ 失败' }}
          </span>
        </div>
        <div class="test-item">
          <span class="test-label">张量运算:</span>
          <span :class="testResults.test_results.tensor_operations ? 'test-pass' : 'test-fail'">
            {{ testResults.test_results.tensor_operations ? '✅ 通过' : '❌ 失败' }}
          </span>
        </div>
      </div>

      <div v-if="testResults.test_results.error_messages.length > 0" class="test-errors">
        <h4>错误信息:</h4>
        <ul>
          <li v-for="(error, index) in testResults.test_results.error_messages" :key="index">
            {{ error }}
          </li>
        </ul>
      </div>
    </div>

    <!-- 优化建议 -->
    <div class="optimization-tips">
      <h2>💡 常见问题解决方案</h2>
      
      <div class="tips-grid">
        <div class="tip-card">
          <h3>🚨 GPU卡死问题</h3>
          <ul>
            <li>检查GPU内存是否不足</li>
            <li>降低视频分辨率和帧数</li>
            <li>启用内存优化选项</li>
            <li>重启系统释放GPU资源</li>
          </ul>
        </div>

        <div class="tip-card">
          <h3>⚠️ 内存不足(OOM)</h3>
          <ul>
            <li>使用--offload_model参数</li>
            <li>启用--t5_cpu选项</li>
            <li>减少帧数到33帧以下</li>
            <li>使用512x512分辨率</li>
          </ul>
        </div>

        <div class="tip-card">
          <h3>🐌 生成速度慢</h3>
          <ul>
            <li>确认使用GPU而非CPU</li>
            <li>启用FP16精度</li>
            <li>减少推理步数</li>
            <li>关闭不必要的后台程序</li>
          </ul>
        </div>

        <div class="tip-card">
          <h3>🔧 驱动问题</h3>
          <ul>
            <li>更新NVIDIA驱动到最新版本</li>
            <li>安装对应的CUDA版本</li>
            <li>重新安装PyTorch GPU版本</li>
            <li>检查硬件连接</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Refresh, Search, Cpu, Delete } from '@element-plus/icons-vue'
import axios from 'axios'

export default {
  name: 'GPUDiagnostic',
  components: {
    Refresh,
    Search,
    Cpu,
    Delete
  },
  setup() {
    const loading = ref(false)
    const diagnosing = ref(false)
    const testing = ref(false)
    const cleaning = ref(false)
    
    const gpuStatus = ref({})
    const gpuUsage = ref({})
    const diagnosticResults = ref(null)
    const testResults = ref(null)
    const quickFixes = ref([])
    
    // 计算内存使用百分比
    const memoryUsagePercent = computed(() => {
      if (!gpuStatus.value.memory_total_gb || !gpuStatus.value.memory_free_gb) {
        return 0
      }
      const used = gpuStatus.value.memory_total_gb - gpuStatus.value.memory_free_gb
      return (used / gpuStatus.value.memory_total_gb) * 100
    })
    
    // 刷新GPU状态
    const refreshStatus = async () => {
      loading.value = true
      try {
        const response = await axios.get('/api/v1/gpu-diagnostic/status')
        if (response.data.success) {
          gpuStatus.value = response.data.gpu_info
          gpuUsage.value = response.data.usage_info
          ElMessage.success('GPU状态已更新')
        }
      } catch (error) {
        console.error('获取GPU状态失败:', error)
        ElMessage.error('获取GPU状态失败')
      } finally {
        loading.value = false
      }
    }
    
    // 运行完整诊断
    const runDiagnostic = async () => {
      diagnosing.value = true
      try {
        const response = await axios.post('/api/v1/gpu-diagnostic/diagnostic')
        if (response.data.success) {
          diagnosticResults.value = response.data.diagnostic_results
          quickFixes.value = response.data.quick_fixes
          ElMessage.success('诊断完成')
        }
      } catch (error) {
        console.error('GPU诊断失败:', error)
        ElMessage.error('GPU诊断失败')
      } finally {
        diagnosing.value = false
      }
    }
    
    // GPU功能测试
    const testGPU = async () => {
      testing.value = true
      try {
        const response = await axios.get('/api/v1/gpu-diagnostic/test')
        if (response.data.success) {
          testResults.value = response.data
          ElMessage.success('GPU测试完成')
        }
      } catch (error) {
        console.error('GPU测试失败:', error)
        ElMessage.error('GPU测试失败')
      } finally {
        testing.value = false
      }
    }
    
    // 清理GPU内存
    const cleanupMemory = async () => {
      cleaning.value = true
      try {
        const response = await axios.post('/api/v1/gpu-diagnostic/cleanup')
        if (response.data.success) {
          ElMessage.success('GPU内存清理完成')
          // 刷新状态
          await refreshStatus()
        }
      } catch (error) {
        console.error('GPU内存清理失败:', error)
        ElMessage.error('GPU内存清理失败')
      } finally {
        cleaning.value = false
      }
    }
    
    // 复制到剪贴板
    const copyToClipboard = async (text) => {
      try {
        await navigator.clipboard.writeText(text)
        ElMessage.success('已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败')
      }
    }
    
    // 页面加载时获取状态
    onMounted(() => {
      refreshStatus()
    })
    
    return {
      loading,
      diagnosing,
      testing,
      cleaning,
      gpuStatus,
      gpuUsage,
      diagnosticResults,
      testResults,
      quickFixes,
      memoryUsagePercent,
      refreshStatus,
      runDiagnostic,
      testGPU,
      cleanupMemory,
      copyToClipboard
    }
  }
}
</script>

<style scoped>
.gpu-diagnostic {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.page-header p {
  color: #64748b;
  margin: 0;
}

.status-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.status-card {
  display: flex;
  align-items: center;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
}

.status-card.status-success {
  border-color: #10b981;
  background: #f0fdf4;
}

.status-card.status-error {
  border-color: #ef4444;
  background: #fef2f2;
}

.card-icon {
  font-size: 2rem;
  margin-right: 1rem;
}

.card-content h3 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.card-content p {
  margin: 0;
  color: #64748b;
}

.gpu-name {
  font-size: 0.9rem;
  color: #059669;
  font-weight: 500;
}

.memory-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  margin: 0.5rem 0;
  overflow: hidden;
}

.memory-used {
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  transition: width 0.3s ease;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.diagnostic-results,
.test-results,
.optimization-tips {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
}

.issues-list,
.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.issue-item {
  display: flex;
  align-items: flex-start;
  padding: 1rem;
  border-radius: 8px;
}

.issue-item.issue-critical {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.issue-item.issue-warning {
  background: #fffbeb;
  border: 1px solid #fed7aa;
}

.issue-item.issue-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.issue-icon {
  font-size: 1.5rem;
  margin-right: 1rem;
}

.issue-type {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.issue-message {
  color: #4a5568;
}

.recommendation-item {
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
}

.rec-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.rec-priority {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 500;
}

.priority-high {
  background: #fef2f2;
  color: #dc2626;
}

.priority-medium {
  background: #fffbeb;
  color: #d97706;
}

.priority-low {
  background: #f0f9ff;
  color: #0369a1;
}

.rec-header h4 {
  margin: 0;
  color: #1a202c;
}

.rec-actions ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #4a5568;
}

.fixes-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.fix-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background: #f1f5f9;
  border-radius: 4px;
}

.fix-item code {
  flex: 1;
  background: #1e293b;
  color: #f1f5f9;
  padding: 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
}

.test-status {
  font-size: 1.2rem;
  font-weight: 600;
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  margin-bottom: 1rem;
}

.test-success {
  background: #f0fdf4;
  color: #166534;
  border: 1px solid #bbf7d0;
}

.test-error {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.test-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.test-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 6px;
}

.test-label {
  font-weight: 500;
  color: #374151;
}

.test-pass {
  color: #059669;
  font-weight: 500;
}

.test-fail {
  color: #dc2626;
  font-weight: 500;
}

.test-errors {
  padding: 1rem;
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 8px;
}

.test-errors h4 {
  margin: 0 0 0.5rem 0;
  color: #dc2626;
}

.test-errors ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #991b1b;
}

.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.tip-card {
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
}

.tip-card h3 {
  margin: 0 0 1rem 0;
  color: #1a202c;
}

.tip-card ul {
  margin: 0;
  padding-left: 1.5rem;
  color: #4a5568;
}

.tip-card li {
  margin-bottom: 0.5rem;
}

@media (max-width: 768px) {
  .gpu-diagnostic {
    padding: 1rem;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .status-cards {
    grid-template-columns: 1fr;
  }
  
  .tips-grid {
    grid-template-columns: 1fr;
  }
}
</style>
