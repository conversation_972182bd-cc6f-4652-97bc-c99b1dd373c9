#!/usr/bin/env python3
"""
视频生成系统监控脚本
实时监控各个引擎的状态和性能
"""

import os
import sys
import time
import json
import logging
from datetime import datetime, timedelta
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root / "backend"))

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class VideoSystemMonitor:
    """视频生成系统监控器"""
    
    def __init__(self):
        self.monitor_interval = 30  # 30秒检查一次
        self.test_interval = 300    # 5分钟测试一次
        self.last_test_time = {}
        
    def get_system_status(self):
        """获取系统状态"""
        try:
            from backend.app.tasks.video_generation_manager import video_manager
            return video_manager.get_engine_status()
        except Exception as e:
            logger.error(f"获取系统状态失败: {e}")
            return {}
    
    def test_engine_health(self, engine_name):
        """测试引擎健康状态"""
        try:
            from backend.app.tasks.video_generation_manager import video_manager
            
            # 检查是否需要测试
            now = datetime.now()
            last_test = self.last_test_time.get(engine_name)
            
            if last_test and (now - last_test).total_seconds() < self.test_interval:
                return None  # 还不需要测试
            
            logger.info(f"测试引擎健康状态: {engine_name}")
            
            # 执行健康检查
            success = video_manager._test_engine(engine_name, timeout=30)
            
            self.last_test_time[engine_name] = now
            
            return {
                "engine": engine_name,
                "healthy": success,
                "test_time": now.isoformat(),
                "message": "健康检查通过" if success else "健康检查失败"
            }
            
        except Exception as e:
            logger.error(f"引擎健康检查异常 {engine_name}: {e}")
            return {
                "engine": engine_name,
                "healthy": False,
                "test_time": datetime.now().isoformat(),
                "message": f"检查异常: {e}"
            }
    
    def check_disk_space(self):
        """检查磁盘空间"""
        try:
            import shutil
            
            # 检查输出目录空间
            output_dir = project_root / "backend" / "storage" / "videos"
            if output_dir.exists():
                total, used, free = shutil.disk_usage(output_dir)
                free_gb = free / (1024**3)
                total_gb = total / (1024**3)
                usage_percent = (used / total) * 100
                
                return {
                    "free_gb": round(free_gb, 2),
                    "total_gb": round(total_gb, 2),
                    "usage_percent": round(usage_percent, 2),
                    "warning": free_gb < 5.0,  # 少于5GB警告
                    "critical": free_gb < 1.0   # 少于1GB严重
                }
            else:
                return {"error": "输出目录不存在"}
                
        except Exception as e:
            logger.error(f"磁盘空间检查失败: {e}")
            return {"error": str(e)}
    
    def check_gpu_status(self):
        """检查GPU状态"""
        try:
            import torch
            
            if not torch.cuda.is_available():
                return {"available": False, "message": "CUDA不可用"}
            
            gpu_info = []
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / (1024**3)
                
                # 获取内存使用情况
                torch.cuda.set_device(i)
                allocated = torch.cuda.memory_allocated() / (1024**3)
                cached = torch.cuda.memory_reserved() / (1024**3)
                
                gpu_info.append({
                    "id": i,
                    "name": gpu_name,
                    "total_memory_gb": round(gpu_memory, 2),
                    "allocated_gb": round(allocated, 2),
                    "cached_gb": round(cached, 2),
                    "free_gb": round(gpu_memory - cached, 2),
                    "usage_percent": round((cached / gpu_memory) * 100, 2)
                })
            
            return {
                "available": True,
                "count": len(gpu_info),
                "gpus": gpu_info
            }
            
        except Exception as e:
            logger.error(f"GPU状态检查失败: {e}")
            return {"available": False, "error": str(e)}
    
    def generate_report(self):
        """生成监控报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "system_status": self.get_system_status(),
            "disk_space": self.check_disk_space(),
            "gpu_status": self.check_gpu_status(),
            "health_checks": []
        }
        
        # 执行健康检查
        for engine_name in ["wanx", "animatediff", "fallback"]:
            health_result = self.test_engine_health(engine_name)
            if health_result:
                report["health_checks"].append(health_result)
        
        return report
    
    def print_status_summary(self, report):
        """打印状态摘要"""
        print("\n" + "="*60)
        print(f"视频生成系统状态 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("="*60)
        
        # 引擎状态
        print("\n🔧 引擎状态:")
        for name, status in report["system_status"].items():
            status_icon = "✅" if status["status"] == "available" else "❌" if status["status"] == "failed" else "⚠️"
            success_rate = status["success_rate"] * 100
            print(f"  {status_icon} {status['name']}: {status['status']} (成功率: {success_rate:.1f}%)")
        
        # GPU状态
        print("\n🖥️ GPU状态:")
        gpu_status = report["gpu_status"]
        if gpu_status["available"]:
            for gpu in gpu_status["gpus"]:
                print(f"  GPU {gpu['id']}: {gpu['name']}")
                print(f"    内存: {gpu['allocated_gb']:.1f}GB / {gpu['total_memory_gb']:.1f}GB ({gpu['usage_percent']:.1f}%)")
        else:
            print(f"  ❌ GPU不可用: {gpu_status.get('message', 'Unknown')}")
        
        # 磁盘空间
        print("\n💾 磁盘空间:")
        disk_status = report["disk_space"]
        if "error" not in disk_status:
            icon = "🔴" if disk_status["critical"] else "🟡" if disk_status["warning"] else "🟢"
            print(f"  {icon} 可用空间: {disk_status['free_gb']:.1f}GB / {disk_status['total_gb']:.1f}GB")
            print(f"    使用率: {disk_status['usage_percent']:.1f}%")
        else:
            print(f"  ❌ 检查失败: {disk_status['error']}")
        
        # 健康检查
        if report["health_checks"]:
            print("\n🏥 健康检查:")
            for check in report["health_checks"]:
                icon = "✅" if check["healthy"] else "❌"
                print(f"  {icon} {check['engine']}: {check['message']}")
        
        print("\n" + "="*60)
    
    def save_report(self, report):
        """保存报告到文件"""
        try:
            reports_dir = project_root / "logs" / "monitoring"
            reports_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存详细报告
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = reports_dir / f"video_system_report_{timestamp}.json"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            # 保存最新状态
            latest_file = reports_dir / "latest_status.json"
            with open(latest_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            logger.info(f"监控报告已保存: {report_file}")
            
        except Exception as e:
            logger.error(f"保存报告失败: {e}")
    
    def run_continuous_monitoring(self):
        """运行连续监控"""
        logger.info("开始连续监控视频生成系统...")
        
        try:
            while True:
                # 生成报告
                report = self.generate_report()
                
                # 显示状态
                self.print_status_summary(report)
                
                # 保存报告
                self.save_report(report)
                
                # 检查警告条件
                self.check_warnings(report)
                
                # 等待下次检查
                logger.info(f"等待 {self.monitor_interval} 秒后进行下次检查...")
                time.sleep(self.monitor_interval)
                
        except KeyboardInterrupt:
            logger.info("监控停止")
        except Exception as e:
            logger.error(f"监控异常: {e}")
    
    def check_warnings(self, report):
        """检查警告条件"""
        warnings = []
        
        # 检查磁盘空间
        disk_status = report["disk_space"]
        if "critical" in disk_status and disk_status["critical"]:
            warnings.append("🔴 严重：磁盘空间不足1GB")
        elif "warning" in disk_status and disk_status["warning"]:
            warnings.append("🟡 警告：磁盘空间不足5GB")
        
        # 检查引擎状态
        failed_engines = [
            name for name, status in report["system_status"].items()
            if status["status"] == "failed"
        ]
        if failed_engines:
            warnings.append(f"🟡 警告：引擎失败 - {', '.join(failed_engines)}")
        
        # 检查GPU状态
        if not report["gpu_status"]["available"]:
            warnings.append("🔴 严重：GPU不可用")
        
        # 输出警告
        if warnings:
            print("\n⚠️ 系统警告:")
            for warning in warnings:
                print(f"  {warning}")
                logger.warning(warning)
    
    def run_single_check(self):
        """运行单次检查"""
        logger.info("执行单次系统检查...")
        
        report = self.generate_report()
        self.print_status_summary(report)
        self.save_report(report)
        self.check_warnings(report)
        
        return report

def main():
    monitor = VideoSystemMonitor()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--continuous":
        monitor.run_continuous_monitoring()
    else:
        monitor.run_single_check()

if __name__ == "__main__":
    main()
