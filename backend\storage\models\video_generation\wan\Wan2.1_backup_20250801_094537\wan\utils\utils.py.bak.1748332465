# Copyright 2024-2025 The Alibaba Wan Team Authors. All rights reserved.
import argparse
import binascii
import os
import os.path as osp
import shutil
import logging  # 添加logging导入
import tempfile
import subprocess
import numpy as np
import torch
import torchvision
import random
import cv2
import time
from pathlib import Path

# 配置基本日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# 创建logger实例
logger = logging.getLogger("WAN-Utils")

import imageio

__all__ = ['cache_video', 'cache_image', 'str2bool']


def rand_name(length=8, suffix=''):
    name = binascii.b2a_hex(os.urandom(length)).decode('utf-8')
    if suffix:
        if not suffix.startswith('.'):
            suffix = '.' + suffix
        name += suffix
    return name


def cache_video(video, path, h=None, w=None):
    """Video caching function, enhanced version: handles zero values and supports multiple saving methods"""
    logger = logging.getLogger("WAN-Utils")
    
    logger.info(f"最终视频帧形状: {video.shape}")
    
    # 确保视频在CPU上
    if isinstance(video, torch.Tensor):
        logger.info(f"将张量从GPU移至CPU")
        video = video.detach().cpu()
    
    # 检查视频数据类型和形状
    if isinstance(video, torch.Tensor):
        # 检查数据范围
        min_val = float(video.min())
        max_val = float(video.max())
        logger.info(f"自动检测数据范围: [{min_val}, {max_val}]")
        
        # 检查是否所有值都是相同的（可能是全黑或全白）
        if min_val == max_val:
            logger.warning(f"警告: 视频帧数据可能无效，所有值相同: {min_val}")
            print(f"警告: 视频帧数据可能无效，所有值相同: {min_val}")
            print(f"尝试添加小噪声以避免全黑视频")
            
            # 添加随机噪声
            video = video + torch.rand_like(video) * 0.05
            
            # 重新检查数据范围
            min_val = float(video.min())
            max_val = float(video.max())
        
        # 根据值范围进行归一化处理
        if 0 <= min_val and max_val <= 1:
            logger.info(f"检测到数据范围为0-1")
            # 转换到0-255范围
            logger.info(f"检测到视频数据范围为0-1，转换为0-255")
            video = (video * 255).clamp(0, 255).to(torch.uint8)
        elif -1 <= min_val < 0 and 0 < max_val <= 1:
            logger.info(f"检测到数据范围为-1到1，归一化到0-1再转换为0-255")
            video = ((video + 1) / 2 * 255).clamp(0, 255).to(torch.uint8)
        elif 0 <= min_val and max_val <= 255:
            logger.info(f"检测到数据范围可能为0-255，归一化到0-1")
            video = (video / 255.0)
            logger.info(f"检测到视频数据范围为0-1，转换为0-255")
            video = (video * 255).clamp(0, 255).to(torch.uint8)
    
    # 扩展帧数，确保有足够帧数用于生成有效视频
    num_frames = video.shape[0]
    if num_frames < 16:
        logger.warning(f"视频帧数过少 ({num_frames}), 增加到至少16帧")
        # 重复现有帧以达到至少16帧
        repeats = max(1, int(np.ceil(16 / num_frames)))
        video = video.repeat_interleave(repeats, dim=0)
        # 如果超出16帧，裁剪回16帧
        video = video[:16]
    
    logger.info(f"最终视频形状: {video.shape} - 准备保存到 {path}")
    
    try:
        # 创建临时目录保存帧
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"创建临时目录保存视频帧: {temp_dir}")
            
            # 保存每一帧为PNG图像
            for i in range(video.shape[0]):
                frame = video[i].numpy() if isinstance(video, torch.Tensor) else video[i]
                frame_path = os.path.join(temp_dir, f"frame_{i:04d}.png")
                import cv2
                cv2.imwrite(frame_path, frame)
            
            # 使用FFmpeg组合帧
            framerate = 8  # 设置合适的帧率
            ffmpeg_cmd = f"ffmpeg -y -framerate {framerate} -i {temp_dir}/frame_%04d.png -c:v libx264 -preset slow -crf 18 -pix_fmt yuv420p {path}"
            import subprocess
            subprocess.run(ffmpeg_cmd, shell=True, check=True)
            
            # 验证文件是否成功创建
            if os.path.exists(path) and os.path.getsize(path) > 0:
                logger.info(f"使用FFmpeg成功保存视频: {path}, 大小: {os.path.getsize(path)} 字节")
                return True
            else:
                logger.error(f"FFmpeg生成的视频文件无效: {path}")
                return False
    
    except Exception as e:
        logger.error(f"使用FFmpeg保存视频失败: {str(e)}")
        return False
def cache_image(tensor,
                save_file,
                nrow=8,
                normalize=True,
                value_range=(-1, 1),
                retry=5):
    # cache file
    suffix = osp.splitext(save_file)[1]
    if suffix.lower() not in [
            '.jpg', '.jpeg', '.png', '.tiff', '.gif', '.webp'
    ]:
        suffix = '.png'

    # save to cache
    error = None
    for attempt in range(retry):
        try:
            tensor = tensor.clamp(min(value_range), max(value_range))
            torchvision.utils.save_image(
                tensor,
                save_file,
                nrow=nrow,
                normalize=normalize,
                value_range=value_range)
            return save_file
        except Exception as e:
            error = e
            continue


def str2bool(v):
    """
    Convert a string to a boolean.

    Supported true values: 'yes', 'true', 't', 'y', '1'
    Supported false values: 'no', 'false', 'f', 'n', '0'

    Args:
        v (str): String to convert.

    Returns:
        bool: Converted boolean value.

    Raises:
        argparse.ArgumentTypeError: If the value cannot be converted to boolean.
    """
    if isinstance(v, bool):
        return v
    v_lower = v.lower()
    if v_lower in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v_lower in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected (True/False)')
