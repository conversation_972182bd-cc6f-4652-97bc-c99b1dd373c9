#!/usr/bin/env python3
"""
Wanx 2.1 环境测试脚本
验证新环境是否能解决假死问题
"""

import os
import sys
import time
import subprocess
import threading
import signal
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WanxEnvironmentTester:
    """Wanx环境测试器"""
    
    def __init__(self):
        self.base_dir = Path(__file__).parent.parent
        self.wanx_dir = self.base_dir / "backend" / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
        self.venv_python = self.base_dir / "wanx_env" / "Scripts" / "python.exe"
        
    def test_environment_setup(self):
        """测试环境设置"""
        logger.info("=== 测试环境设置 ===")
        
        # 检查虚拟环境
        if not self.venv_python.exists():
            logger.error(f"虚拟环境Python不存在: {self.venv_python}")
            return False
        
        # 检查Wanx目录
        if not self.wanx_dir.exists():
            logger.error(f"Wanx目录不存在: {self.wanx_dir}")
            return False
        
        # 测试Python环境
        try:
            result = subprocess.run([
                str(self.venv_python), "-c",
                "import torch, diffusers, transformers, cv2; "
                "print(f'PyTorch: {torch.__version__}'); "
                "print(f'CUDA: {torch.cuda.is_available()}'); "
                "print(f'Diffusers: {diffusers.__version__}'); "
                "print(f'Transformers: {transformers.__version__}'); "
                "print(f'OpenCV: {cv2.__version__}')"
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode == 0:
                logger.info("环境检查通过:")
                for line in result.stdout.strip().split('\n'):
                    logger.info(f"  {line}")
                return True
            else:
                logger.error(f"环境检查失败: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"环境检查异常: {e}")
            return False
    
    def test_wanx_minimal(self):
        """测试最小Wanx生成"""
        logger.info("=== 测试最小Wanx生成 ===")
        
        # 准备测试命令
        output_file = self.wanx_dir / "test_env.mp4"
        if output_file.exists():
            output_file.unlink()
        
        cmd = [
            str(self.venv_python),
            "generate.py",
            "--task", "t2v-1.3B",
            "--size", "832*480",
            "--ckpt_dir", "./Wan2.1-T2V-1.3B",
            "--prompt", "cat",
            "--offload_model", "True",
            "--t5_cpu",
            "--sample_shift", "8",
            "--sample_guide_scale", "5.0",
            "--sample_steps", "8",  # 更少步数
            "--frame_num", "5",     # 更少帧数
            "--base_seed", "12345",
            "--save_file", "./test_env.mp4"
        ]
        
        logger.info(f"执行命令: {' '.join(cmd)}")
        logger.info(f"工作目录: {self.wanx_dir}")
        
        # 执行测试
        start_time = time.time()
        try:
            # 使用更短的超时时间
            result = subprocess.run(
                cmd,
                cwd=str(self.wanx_dir),
                capture_output=True,
                text=True,
                timeout=120  # 2分钟超时
            )
            
            elapsed_time = time.time() - start_time
            logger.info(f"执行完成，耗时: {elapsed_time:.1f}秒")
            
            if result.returncode == 0:
                # 检查输出文件
                if output_file.exists() and output_file.stat().st_size > 0:
                    logger.info(f"✅ 测试成功！生成文件: {output_file}")
                    logger.info(f"文件大小: {output_file.stat().st_size} bytes")
                    return True
                else:
                    logger.error("❌ 进程成功但文件未生成")
                    return False
            else:
                logger.error(f"❌ 进程失败，返回码: {result.returncode}")
                logger.error(f"错误输出: {result.stderr}")
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("❌ 测试超时（2分钟）")
            return False
        except Exception as e:
            logger.error(f"❌ 测试异常: {e}")
            return False
    
    def test_progressive_complexity(self):
        """渐进式复杂度测试"""
        logger.info("=== 渐进式复杂度测试 ===")
        
        test_cases = [
            {
                "name": "超简单测试",
                "steps": 5,
                "frames": 3,
                "prompt": "cat",
                "guide_scale": 4.0
            },
            {
                "name": "简单测试", 
                "steps": 8,
                "frames": 5,
                "prompt": "cat running",
                "guide_scale": 5.0
            },
            {
                "name": "标准测试",
                "steps": 12,
                "frames": 9,
                "prompt": "a cat running in garden",
                "guide_scale": 6.0
            }
        ]
        
        for i, test_case in enumerate(test_cases):
            logger.info(f"--- {test_case['name']} ---")
            
            output_file = self.wanx_dir / f"test_progressive_{i}.mp4"
            if output_file.exists():
                output_file.unlink()
            
            cmd = [
                str(self.venv_python),
                "generate.py",
                "--task", "t2v-1.3B",
                "--size", "832*480",
                "--ckpt_dir", "./Wan2.1-T2V-1.3B",
                "--prompt", test_case["prompt"],
                "--offload_model", "True",
                "--t5_cpu",
                "--sample_shift", "8",
                "--sample_guide_scale", str(test_case["guide_scale"]),
                "--sample_steps", str(test_case["steps"]),
                "--frame_num", str(test_case["frames"]),
                "--base_seed", "12345",
                "--save_file", f"./test_progressive_{i}.mp4"
            ]
            
            start_time = time.time()
            try:
                result = subprocess.run(
                    cmd,
                    cwd=str(self.wanx_dir),
                    capture_output=True,
                    text=True,
                    timeout=180  # 3分钟超时
                )
                
                elapsed_time = time.time() - start_time
                
                if result.returncode == 0 and output_file.exists() and output_file.stat().st_size > 0:
                    logger.info(f"✅ {test_case['name']} 成功！耗时: {elapsed_time:.1f}秒")
                else:
                    logger.error(f"❌ {test_case['name']} 失败")
                    return False
                    
            except subprocess.TimeoutExpired:
                logger.error(f"❌ {test_case['name']} 超时")
                return False
            except Exception as e:
                logger.error(f"❌ {test_case['name']} 异常: {e}")
                return False
        
        logger.info("🎉 所有渐进式测试通过！")
        return True
    
    def run_full_test(self):
        """运行完整测试"""
        logger.info("开始Wanx环境完整测试...")
        
        # 1. 环境设置测试
        if not self.test_environment_setup():
            logger.error("环境设置测试失败")
            return False
        
        # 2. 最小生成测试
        if not self.test_wanx_minimal():
            logger.error("最小生成测试失败")
            return False
        
        # 3. 渐进式复杂度测试
        if not self.test_progressive_complexity():
            logger.error("渐进式复杂度测试失败")
            return False
        
        logger.info("🎊 所有测试通过！Wanx 2.1环境配置成功！")
        return True

if __name__ == "__main__":
    tester = WanxEnvironmentTester()
    success = tester.run_full_test()
    
    if success:
        print("\n" + "="*50)
        print("🎉 Wanx 2.1环境测试成功！")
        print("现在可以在生产环境中使用新的虚拟环境")
        print("="*50)
    else:
        print("\n" + "="*50)
        print("❌ Wanx 2.1环境测试失败")
        print("建议尝试策略2或策略3")
        print("="*50)
