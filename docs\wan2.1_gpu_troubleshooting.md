# Wan2.1 GPU问题诊断与解决方案

## 🎯 您的问题：GPU卡死

您提到的问题："**一用gpu就卡死，是我的配置不行，还是没有设置好？**"

这是一个常见的GPU内存和配置问题，我已经为您创建了完整的诊断和解决方案。

## 🔍 问题诊断

### 1. 立即检查GPU状态

访问GPU诊断页面：
```
http://*************:9000/gpu-diagnostic
```

这个页面会显示：
- ✅ GPU是否可用
- 📊 内存使用情况
- 🌡️ GPU温度
- ⚡ GPU利用率
- 🔧 具体问题和解决建议

### 2. 常见的GPU卡死原因

#### 🚨 内存不足 (最常见)
- **现象**：程序启动后立即卡死或OOM错误
- **原因**：Wan2.1模型需要大量GPU内存
- **检查**：GPU内存是否小于8GB

#### ⚠️ 驱动问题
- **现象**：CUDA错误或驱动崩溃
- **原因**：NVIDIA驱动版本过旧或不兼容
- **检查**：驱动版本是否低于470

#### 🔧 配置问题
- **现象**：参数设置不当导致资源耗尽
- **原因**：分辨率、帧数、推理步数设置过高
- **检查**：是否使用了默认的高配置参数

## ✅ 解决方案

### 方案1：自动优化配置（推荐）

我已经为您优化了Wan2.1生成脚本，现在支持自动检测和优化：

```bash
# 使用自动优化模式
python generate.py \
  --text "您的提示词" \
  --output "output.mp4" \
  --device auto \
  --fallback_to_cpu \
  --enable_memory_optimization
```

**新增的优化参数**：
- `--device auto`：自动检测最佳设备
- `--fallback_to_cpu`：GPU失败时自动回退CPU
- `--enable_memory_optimization`：启用内存优化
- `--gpu_memory_limit 6`：设置GPU内存限制

### 方案2：根据GPU内存手动配置

#### 🔴 GPU内存 < 6GB（入门级显卡）
```bash
python generate.py \
  --text "您的提示词" \
  --output "output.mp4" \
  --device cuda \
  --offload_model \
  --t5_cpu \
  --size "512x512" \
  --length 33 \
  --steps 20
```

#### 🟡 GPU内存 6-12GB（中端显卡）
```bash
python generate.py \
  --text "您的提示词" \
  --output "output.mp4" \
  --device cuda \
  --offload_model \
  --t5_cpu \
  --size "768x512" \
  --length 49 \
  --steps 30
```

#### 🟢 GPU内存 > 12GB（高端显卡）
```bash
python generate.py \
  --text "您的提示词" \
  --output "output.mp4" \
  --device cuda \
  --size "1280x720" \
  --length 81 \
  --steps 50
```

### 方案3：CPU回退模式（最稳定）

如果GPU持续出问题，可以使用CPU模式：

```bash
python generate.py \
  --text "您的提示词" \
  --output "output.mp4" \
  --device cpu \
  --size "512x512" \
  --length 33 \
  --steps 20
```

## 🛠️ 具体修复步骤

### 步骤1：检查GPU配置

运行GPU诊断：
```bash
# 在后端目录执行
python -c "
from app.services.gpu_diagnostic import gpu_diagnostic
result = gpu_diagnostic.run_full_diagnostic()
print('GPU可用:', result['cuda_status']['available'])
print('问题数量:', len(result['issues']))
for issue in result['issues']:
    print(f'- {issue[\"severity\"]}: {issue[\"message\"]}')
"
```

### 步骤2：应用推荐配置

根据诊断结果，系统会自动推荐最佳配置：

```python
from app.services.video_generation_optimizer import video_optimizer

# 获取优化建议
base_args = {
    "task": "t2v-1.3B",
    "size": "768x512", 
    "frame_num": 81,
    "sample_steps": 50
}

optimized_args = video_optimizer.optimize_wan_args(base_args)
print("优化后配置:", optimized_args)
```

### 步骤3：测试生成

使用优化后的配置测试：

```bash
# 测试小参数生成
python generate.py \
  --text "a cat sitting on a chair" \
  --output "test.mp4" \
  --device auto \
  --size "512x512" \
  --length 33 \
  --steps 25 \
  --fallback_to_cpu
```

## 🔧 常见问题快速修复

### Q1: "CUDA out of memory" 错误
**解决方案**：
```bash
# 清理GPU内存
python -c "import torch; torch.cuda.empty_cache()"

# 使用内存优化模式
python generate.py --enable_memory_optimization --offload_model --t5_cpu
```

### Q2: "nvidia-smi" 命令失败
**解决方案**：
```bash
# 检查驱动状态
nvidia-smi

# 如果失败，重新安装驱动
# Windows: 从NVIDIA官网下载最新驱动
# Linux: sudo apt update && sudo apt install nvidia-driver-470
```

### Q3: PyTorch无法识别GPU
**解决方案**：
```bash
# 重新安装GPU版本的PyTorch
pip uninstall torch torchvision torchaudio
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118

# 验证安装
python -c "import torch; print('CUDA可用:', torch.cuda.is_available())"
```

### Q4: 生成的视频是黑屏
**解决方案**：
```bash
# 增加帧数（确保>=33帧）
python generate.py --length 33

# 使用更好的采样器
python generate.py --solver "euler"

# 增加引导强度
python generate.py --cfg 7.5
```

## 📊 性能优化建议

### 硬件配置推荐

| GPU内存 | 推荐配置 | 预期性能 |
|---------|----------|----------|
| 4-6GB | 512x512, 33帧, CPU卸载 | 慢但稳定 |
| 8-12GB | 768x512, 49帧, 部分卸载 | 中等速度 |
| 16GB+ | 1280x720, 81帧, 全GPU | 最佳性能 |

### 软件环境推荐

- **NVIDIA驱动**: 470+ (推荐最新版)
- **CUDA版本**: 11.8 或 12.1
- **PyTorch版本**: 2.0+ GPU版本
- **Python版本**: 3.8-3.11

## 🚀 使用新的优化系统

### 1. 访问GPU诊断页面
```
http://*************:9000/gpu-diagnostic
```

### 2. 运行完整诊断
- 点击"完整诊断"按钮
- 查看发现的问题
- 按照建议进行修复

### 3. 应用优化配置
- 系统会根据您的硬件自动推荐配置
- 使用推荐的参数进行视频生成
- 监控GPU使用情况

### 4. 问题持续？使用CPU模式
如果GPU问题无法解决，CPU模式仍然可以生成视频：
- 速度较慢但非常稳定
- 不会出现内存问题
- 适合测试和小规模使用

## 📞 获取帮助

如果问题仍然存在：

1. **查看诊断报告**：访问GPU诊断页面获取详细报告
2. **检查日志**：查看生成过程中的错误日志
3. **尝试CPU模式**：确认模型本身是否正常工作
4. **硬件检查**：确认GPU硬件和驱动是否正常

## 🎯 总结

您的GPU卡死问题很可能是：
1. **GPU内存不足** - 使用内存优化配置
2. **参数设置过高** - 降低分辨率和帧数
3. **驱动问题** - 更新NVIDIA驱动

现在系统已经具备：
- ✅ 自动GPU检测和优化
- ✅ 智能参数调整
- ✅ CPU回退机制
- ✅ 完整的诊断工具
- ✅ 实时监控和建议

**立即尝试**：访问 `http://*************:9000/gpu-diagnostic` 获取针对您硬件的具体优化建议！
