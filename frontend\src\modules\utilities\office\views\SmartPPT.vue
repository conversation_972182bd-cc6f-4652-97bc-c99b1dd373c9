﻿<template>
  <div class="smart-ppt-container">
    <PageHeaderGradient 
      title="智能PPT生成"
      description="使用对话式AI快速创建专业演示文稿"
    />
    
    <div class="smart-ppt-content">
      <a-row :gutter="[24, 24]">
        <!-- 主要区域：对话界面或PPT预览/生成界面 -->
        <a-col :xs="24" :lg="16">
          <a-card :bordered="false" class="main-card">
            <!-- 对话界面 -->
            <div v-if="currentStep === 0" class="chat-wrapper">
              <chat-interface
                ref="chatInterfaceRef"
                :loading="chatLoading"
                :canPreview="canPreviewPPT"
                :canGenerate="canGeneratePPT"
                @user-message="handleUserMessage"
                @preview="handlePreviewPPT"
                @generate="handleGeneratePPT"
                @reset="handleResetChat"
              />
            </div>
            
            <!-- 生成步骤 -->
            <div v-if="currentStep === 1">
              <h3>生成PPT</h3>
              <p class="step-description">正在智能生成您的PPT，请稍候</p>
              
              <ppt-generation-progress
                ref="pptGenerationProgressRef"
                :status="generationStatus"
                :progress="generationProgress"
                :stage="generationStage"
                :taskId="taskId"
                :error="generationError"
                @retry="handleRetryGeneration"
                @cancel="handleCancelGeneration"
              />
            </div>
            
            <!-- 预览下载步骤 -->
            <div v-if="currentStep === 2">
              <h3>PPT预览和下载</h3>
              <p class="step-description">您的PPT已生成，可以预览和下载</p>
              
              <ppt-preview
                ref="pptPreviewRef"
                :pptUrl="pptUrl"
                :fileName="outputFileName"
                @download="handleDownloadComplete"
              />
              
              <div class="action-buttons">
                <a-button @click="returnToChat">
                  <template #icon><comment-outlined /></template>
                  返回对话
                </a-button>
                <a-button @click="handleNewGeneration" type="primary">
                  <template #icon><file-outlined /></template>
                  新建PPT
                </a-button>
              </div>
            </div>
          </a-card>
        </a-col>
        
        <!-- 侧边区域：信息和历史 -->
        <a-col :xs="24" :lg="8">
          <a-card :bordered="false" title="关于对话式PPT生成" class="info-card">
            <p>我们的智能PPT生成服务可以帮您：</p>
            
            <ul class="feature-list">
              <li>
                <div class="feature-icon-wrapper">
                  <check-circle-outlined class="feature-icon" />
                </div>
                <span>通过自然对话创建专业演示文稿</span>
              </li>
              <li>
                <div class="feature-icon-wrapper">
                  <bulb-outlined class="feature-icon" />
                </div>
                <span>AI智能理解您的需求和内容</span>
              </li>
              <li>
                <div class="feature-icon-wrapper">
                  <file-outlined class="feature-icon" />
                </div>
                <span>根据对话内容自动提取PPT结构</span>
              </li>
              <li>
                <div class="feature-icon-wrapper">
                  <picture-outlined class="feature-icon" />
                </div>
                <span>提供精美排版和多种设计风格</span>
              </li>
              <li>
                <div class="feature-icon-wrapper">
                  <bar-chart-outlined class="feature-icon" />
                </div>
                <span>自动添加相关图片和生成图表</span>
              </li>
            </ul>
            
            <a-divider />
            
            <h4>使用提示</h4>
            <p>与AI助手自然交流，描述您的PPT需求和内容。AI会引导您提供必要信息，并根据您的回答生成个性化PPT。</p>
          </a-card>
          
          <!-- 最近生成任务 -->
          <a-card :bordered="false" title="最近的PPT任务" class="recent-tasks-card" v-if="recentTasks.length > 0">
            <a-list
              size="small"
              :dataSource="recentTasks"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta>
                    <template #title>
                      <div class="task-item-title">
                        <span>{{ item.fileName }}</span>
                        <a-tag :color="item.status === 'success' ? 'success' : item.status === 'error' ? 'error' : 'processing'">
                          {{ item.status === 'success' ? '完成' : item.status === 'error' ? '失败' : '处理中' }}
                        </a-tag>
                      </div>
                    </template>
                    <template #description>
                      <div class="task-item-desc">
                        <span>{{ item.theme }}</span>
                        <span class="task-time">{{ formatTime(item.createdAt) }}</span>
                      </div>
                    </template>
                  </a-list-item-meta>
                  <template #actions>
                    <a-button 
                      v-if="item.status === 'success'" 
                      type="link" 
                      size="small" 
                      @click="downloadRecentTask(item)"
                    >
                      <template #icon><download-outlined /></template>
                      下载
                    </a-button>
                  </template>
                </a-list-item>
              </template>
            </a-list>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, computed } from 'vue';
import {
  CircleCheck,
  Document,
  Lightbulb,
  Picture,
  BarChart,
  Monitor,
  ChatDotRound,
  Download
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import pptGenerationService from '../services/pptGenerationService.js';
import chatService from '../services/chatService.js';

// 导入组件
import ChatInterface from '@/components/chat/ChatInterface.vue';
import PptGenerationProgress from '@/components/utilities/office/PPTGenerationProgress.vue';
import PptPreview from '@/components/utilities/office/PPTPreview.vue';
import PageHeaderGradient from '@/components/PageHeaderGradient.vue';

export default defineComponent({
  name: 'SmartPPT',
  components: {
    ChatInterface,
    PptGenerationProgress,
    PptPreview,
    CheckCircleOutlined,
    FileOutlined,
    BulbOutlined,
    PictureOutlined,
    BarChartOutlined,
    DesktopOutlined,
    CommentOutlined,
    DownloadOutlined,
    PageHeaderGradient
  },
  
  setup() {
    // 当前步骤
    const currentStep = ref(0);
    
    // 聊天状态
    const chatInterfaceRef = ref(null);
    const chatLoading = ref(false);
    const conversationId = ref('');
    const messageCount = ref(0);
    const canPreviewPPT = ref(false);
    const canGeneratePPT = ref(false);
    
    // PPT生成状态
    const generationStatus = ref('waiting');
    const generationProgress = ref(0);
    const generationStage = ref('');
    const generationError = ref('');
    const taskId = ref('');
    
    // PPT信息
    const pptUrl = ref('');
    const outputFileName = ref('智能PPT');
    const pptContent = ref(null);
    
    // 最近任务
    const recentTasks = ref([]);
    
    // 组件引用
    const pptGenerationProgressRef = ref(null);
    const pptPreviewRef = ref(null);
    
    // 定时检查生成状态
    let statusCheckInterval = null;
    
    // 加载最近任务
    const loadRecentTasks = () => {
      try {
        const tasks = pptGenerationService.getRecentTasks();
        recentTasks.value = tasks.slice(0, 5); // 只显示最近5个任务
      } catch (error) {
        console.error('加载最近任务失败:', error);
      }
    };
    
    // 格式化时间
    const formatTime = (timestamp) => {
      if (!timestamp) return '';
      const date = new Date(timestamp);
      return `${date.getMonth() + 1}/${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`;
    };
    
    // 下载最近任务
    const downloadRecentTask = (task) => {
      if (task.status !== 'success') return;
      pptGenerationService.downloadPPT(task.taskId, task.fileName)
        .then(() => {
          ElMessage.success('PPT下载成功');
        })
        .catch(error => {
          ElMessage.error('下载失败: ' + error.message);
        });
    };
    
    // 处理用户消息
    const handleUserMessage = async (message) => {
      try {
        chatLoading.value = true;
        
        // 确保对话已创建
        if (!conversationId.value) {
          conversationId.value = chatService.createConversation();
        }
        
        // 发送消息并获取回复
        const response = await chatService.sendMessage(ElMessage.content, conversationId.value);
        
        // 显示AI回复
        chatInterfaceRef.value.receiveAssistantMessage(response.content);
        
        // 更新消息计数并设置能否生成PPT的状态
        messageCount.value += 2; // 用户消息和AI回复各算一条
        
        // 当对话达到一定程度，允许预览和生成
        if (messageCount.value >= 4) { // 至少两轮对话后可以预览
          canPreviewPPT.value = true;
        }
        
        if (messageCount.value >= 8) { // 至少四轮对话后可以生成
          canGeneratePPT.value = true;
        }
      } catch (error) {
        console.error('发送消息失败:', error);
        ElMessage.error('发送消息失败: ' + error.message);
      } finally {
        chatLoading.value = false;
      }
    };
    
    // 预览PPT
    const handlePreviewPPT = async () => {
      if (!canPreviewPPT.value || !conversationId.value) return;
      
      try {
        chatLoading.value = true;
        chatInterfaceRef.value.receiveAssistantMessage('正在基于我们的对话生成PPT预览，请稍候...');
        
        // 从对话中提取PPT内容
        const extractedContent = await chatService.extractPPTContent(conversationId.value);
        
        // 保存提取的内容用于生成
        pptContent.value = extractedContent;
        outputFileName.value = extractedContent.title || '智能PPT';
        
        // 显示预览内容给用户
        let previewMessage = `## PPT预览\n\n`;
        previewMessage += `**标题**: ${extractedContent.title}\n\n`;
        previewMessage += `**主要内容**:\n`;
        
        if (extractedContent.slides && extractedContent.slides.length > 0) {
          extractedContent.slides.forEach((slide, index) => {
            if (slide.type === 'title') {
              // 标题页不需要额外添加
            } else if (slide.title) {
              previewMessage += `- ${slide.title}\n`;
            }
          });
        }
        
        previewMessage += `\n**设计风格**: ${extractedContent.template || '商务专业'}\n\n`;
        previewMessage += `如果您满意这个PPT结构，请点击"生成PPT"按钮开始制作。\n如果您希望调整，可以继续与我对话，告诉我需要修改的地方。`;
        
        chatInterfaceRef.value.receiveAssistantMessage(previewMessage);
        canGeneratePPT.value = true;
      } catch (error) {
        console.error('获取预览失败:', error);
        chatInterfaceRef.value.receiveAssistantMessage('生成预览时出现错误，请稍后重试。');
      } finally {
        chatLoading.value = false;
      }
    };
    
    // 生成PPT
    const handleGeneratePPT = async () => {
      if (!canGeneratePPT.value || !conversationId.value) return;

      try {
        // 从对话中提取内容
        if (!pptContent.value) {
          chatLoading.value = true;
          chatInterfaceRef.value.receiveAssistantMessage('正在分析我们的对话并提取PPT内容...');
          
          const extractedContent = await chatService.extractPPTContent(conversationId.value);
          pptContent.value = extractedContent;
          outputFileName.value = extractedContent.title || '智能PPT';
          
          chatLoading.value = false;
          chatInterfaceRef.value.receiveAssistantMessage('内容提取完成，准备生成PPT...');
        }
        
        // 确认生成
        currentStep.value = 1;
        startGeneration();
      } catch (error) {
        console.error('启动生成失败:', error);
        chatLoading.value = false;
        chatInterfaceRef.value.receiveAssistantMessage('准备生成PPT时出现错误，请稍后重试。');
      }
    };
    
    // 重置对话
    const handleResetChat = () => {
      conversationId.value = chatService.createConversation();
      messageCount.value = 0;
      canPreviewPPT.value = false;
      canGeneratePPT.value = false;
      pptContent.value = null;
    };
    
    // 开始生成
    const startGeneration = async () => {
      try {
        generationStatus.value = 'processing';
        generationProgress.value = 5;
        generationStage.value = 'initialize';
        
        console.log('开始PPT生成，参数:', pptContent.value);
        
        // 发送转换请求
        const result = await pptGenerationService.generatePPT({
          title: pptContent.value.title,
          theme: pptContent.value.theme || pptContent.value.title,
          outline: pptContent.value.slides?.map(slide => {
            if (slide.type === 'bullets' && slide.bullets) {
              return { title: slide.title, points: slide.bullets };
            } else if (slide.content) {
              return { title: slide.title, content: slide.content };
            } else {
              return { title: slide.title };
            }
          }),
          style: pptContent.value.style || 'professional',
          includeImages: true,
          useAI: true,
          template: pptContent.value.template || 'business',
          includeCharts: true,
          outputFormat: 'pptx',
          conversationId: conversationId.value // 传递对话ID以便后端可以使用完整对话内容
        });
        
        console.log('PPT生成任务已创建，结果:', result);
        
        // 保存任务ID
        taskId.value = result.taskId || result.task_id;
        
        if (!taskId.value) {
          ElMessage.warning('未获得有效的任务ID，将使用本地模拟模式');
          taskId.value = `local-${Date.now()}`;
          
          // 手动创建本地模拟任务
          const mockTask = {
            taskId: taskId.value,
            status: 'processing',
            progress: 10,
            message: '本地模拟生成中...',
            createdAt: new Date().toISOString()
          };
          
          // 保存到本地
          localStorage.setItem(`ppt_task_${taskId.value}`, JSON.stringify(mockTask));
        }
        
        // 开始检查状态
        startStatusCheck();
        ElMessage.info('PPT生成任务已开始，请稍候...');
      } catch (error) {
        console.error('开始生成失败:', error);
        generationStatus.value = 'error';
        generationError.value = error.message || '启动生成失败';
        ElMessage.error('启动生成失败: ' + (error.message || '未知错误'));
      }
    };
    
    // 开始定时检查状态
    const startStatusCheck = () => {
      // 清除可能存在的旧定时器
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
      }
      
      // 设置新的定时器，每2秒检查一次
      statusCheckInterval = setInterval(checkGenerationStatus, 2000);
    };
    
    // 检查生成状态
    const checkGenerationStatus = async () => {
      if (!taskId.value) return;
      
      try {
        console.log('检查任务状态:', taskId.value);
        const status = await pptGenerationService.getTaskStatus(taskId.value);
        console.log('获取到任务状态:', status);
        
        // 更新状态和进度
        generationStatus.value = status.status;
        generationProgress.value = status.progress || 0;
        generationStage.value = status.stage || '';
        
        // 处理完成状态
        if (status.status === 'success') {
          // 清除状态检查
          clearInterval(statusCheckInterval);
          
          // 设置下载URL
          pptUrl.value = status.download_url || status.url || '';
          
          // 添加到最近任务
          const newTask = {
            taskId: taskId.value,
            fileName: outputFileName.value,
            theme: pptContent.value.theme || pptContent.value.title,
            status: 'success',
            url: pptUrl.value,
            createdAt: new Date().toISOString()
          };
          pptGenerationService.addRecentTask(newTask);
          
          // 重新加载最近任务
          loadRecentTasks();
          
          // 跳转到预览步骤
          currentStep.value = 2;
          
          ElMessage.success('PPT生成成功！');
        }
        // 处理错误状态
        else if (status.status === 'error') {
          // 清除状态检查
          clearInterval(statusCheckInterval);
          
          // 设置错误信息
          generationError.value = status.message || '生成过程中出错';
          
          // 添加到最近任务
          const newTask = {
            taskId: taskId.value,
            fileName: outputFileName.value,
            theme: pptContent.value.theme || pptContent.value.title,
            status: 'error',
            createdAt: new Date().toISOString()
          };
          pptGenerationService.addRecentTask(newTask);
          
          // 重新加载最近任务
          loadRecentTasks();
          
          ElMessage.error('PPT生成失败: ' + generationError.value);
        }
      } catch (error) {
        console.error('检查任务状态失败:', error);
      }
    };
    
    // 重试生成
    const handleRetryGeneration = () => {
      // 重置状态
      generationError.value = '';
      generationStatus.value = 'waiting';
      generationProgress.value = 0;
      
      // 重新开始生成
      startGeneration();
    };
    
    // 取消生成
    const handleCancelGeneration = () => {
      // 清除状态检查
      if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
      }
      
      // 取消任务
      if (taskId.value) {
        pptGenerationService.cancelTask(taskId.value)
          .catch(error => {
            console.error('取消任务失败:', error);
          });
      }
      
      // 重置状态
      generationStatus.value = 'waiting';
      generationProgress.value = 0;
      generationStage.value = '';
      generationError.value = '';
      taskId.value = '';
      
      // 返回到对话步骤
      currentStep.value = 0;
    };
    
    // 下载完成
    const handleDownloadComplete = () => {
      ElMessage.success('PPT下载成功');
    };
    
    // 返回对话
    const returnToChat = () => {
      currentStep.value = 0;
      
      // 向用户提供反馈
      chatInterfaceRef.value.receiveAssistantMessage(
        '您的PPT已生成完成！您可以继续与我对话，创建新的PPT或修改当前内容。'
      );
    };
    
    // 新建生成
    const handleNewGeneration = () => {
      // 重置状态
      generationStatus.value = 'waiting';
      generationProgress.value = 0;
      generationStage.value = '';
      generationError.value = '';
      taskId.value = '';
      pptUrl.value = '';
      outputFileName.value = '智能PPT';
      pptContent.value = null;
      
      // 重置对话
      handleResetChat();
      
      // 返回到对话步骤
      currentStep.value = 0;
    };
    
    // 组件挂载时
    onMounted(() => {
      // 创建新对话
      conversationId.value = chatService.createConversation();
      
      // 加载最近任务
      loadRecentTasks();
    });
    
    return {
      currentStep,
      chatInterfaceRef,
      chatLoading,
      canPreviewPPT,
      canGeneratePPT,
      generationStatus,
      generationProgress,
      generationStage,
      generationError,
      taskId,
      pptUrl,
      outputFileName,
      recentTasks,
      pptGenerationProgressRef,
      pptPreviewRef,
      formatTime,
      downloadRecentTask,
      handleUserMessage,
      handlePreviewPPT,
      handleGeneratePPT,
      handleResetChat,
      handleRetryGeneration,
      handleCancelGeneration,
      handleDownloadComplete,
      returnToChat,
      handleNewGeneration
    };
  }
});
</script>

<style scoped>
.smart-ppt-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40px;
}

.smart-ppt-content {
  padding: 0 24px;
  margin-top: 24px;
}

.main-card {
  min-height: 600px;
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.chat-wrapper {
  height: 600px;
  display: flex;
  flex-direction: column;
}

.conversion-steps {
  margin-bottom: 32px;
}

.step-description {
  color: rgba(0, 0, 0, 0.45);
  margin-bottom: 24px;
}

.info-card, .recent-tasks-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.feature-list {
  padding-left: 0;
  list-style: none;
}

.feature-list li {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
}

.feature-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: rgba(82, 196, 26, 0.1);
  border-radius: 50%;
  margin-right: 10px;
}

.feature-icon {
  color: #52c41a;
  font-size: 16px;
}

.task-item-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-item-desc {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.task-time {
  color: #999;
}

.action-buttons {
  margin-top: 24px;
  display: flex;
  justify-content: space-between;
}

/* 添加一些美化样式 */
:deep(.ant-page-header) {
  background-color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  margin-bottom: 24px;
}

:deep(.ant-card-head) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
  padding: 16px 24px;
}

:deep(.ant-card-body) {
  padding: 20px 24px;
}

:deep(.ant-list-item) {
  padding: 12px 0;
  transition: all 0.3s;
}

:deep(.ant-list-item:hover) {
  background-color: #f9f9f9;
  border-radius: 8px;
}

:deep(.ant-tag) {
  border-radius: 4px;
  font-weight: 500;
}

:deep(.anticon) {
  vertical-align: -0.125em;
}
</style> 
