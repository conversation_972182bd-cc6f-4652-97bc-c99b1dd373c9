# Copyright 2024-2025 The Alibaba Wan Team Authors. All rights reserved.
import tempfile
import argparse
from datetime import datetime
import logging
import os
import sys
import warnings
import time
# import tempfile 已在文件开头导入

warnings.filterwarnings('ignore')

import torch, random
import torch.distributed as dist
from PIL import Image

import wan
from wan.configs import WAN_CONFIGS, SIZE_CONFIGS, MAX_AREA_CONFIGS, SUPPORTED_SIZES
from wan.utils.prompt_extend import DashScopePromptExpander, QwenPromptExpander
from wan.utils.utils import cache_video, cache_image, str2bool

EXAMPLE_PROMPT = {
    "t2v-1.3B": {
        "prompt": "Two anthropomorphic cats in comfy boxing gear and bright gloves fight intensely on a spotlighted stage.",
    },
    "t2v-14B": {
        "prompt": "Two anthropomorphic cats in comfy boxing gear and bright gloves fight intensely on a spotlighted stage.",
    },
    "t2i-14B": {
        "prompt": "一个朴素端庄的美人",
    },
    "i2v-14B": {
        "prompt":
            "Summer beach vacation style, a white cat wearing sunglasses sits on a surfboard. The fluffy-furred feline gazes directly at the camera with a relaxed expression. Blurred beach scenery forms the background featuring crystal-clear waters, distant green hills, and a blue sky dotted with white clouds. The cat assumes a naturally relaxed posture, as if savoring the sea breeze and warm sunlight. A close-up shot highlights the feline's intricate details and the refreshing atmosphere of the seaside.",
        "image":
            "examples/i2v_input.JPG",
    },
    "flf2v-14B": {
            "prompt":
                "CG动画风格，一只蓝色的小鸟从地面起飞，煽动翅膀。小鸟羽毛细腻，胸前有独特的花纹，背景是蓝天白云，阳光明媚。镜跟随小鸟向上移动，展现出小鸟飞翔的姿态和天空的广阔。近景，仰视视角。",
            "first_frame":
                "examples/flf2v_input_first_frame.png",
            "last_frame":
                "examples/flf2v_input_last_frame.png",
    },
}


def _validate_args(args):
    # Basic check
    assert args.ckpt_dir is not None, "Please specify the checkpoint directory."
    assert args.task in WAN_CONFIGS, f"Unsupport task: {args.task}"
    assert args.task in EXAMPLE_PROMPT, f"Unsupport task: {args.task}"

    # The default sampling steps are 40 for image-to-video tasks and 50 for text-to-video tasks.
    if args.sample_steps is None:
        args.sample_steps = 40 if "i2v" in args.task else 50

    if args.sample_shift is None:
        args.sample_shift = 5.0
        if "i2v" in args.task and args.size in ["832*480", "480*832"]:
            args.sample_shift = 3.0
        if "flf2v" in args.task:
            args.sample_shift = 16

    # 确保视频至少有32帧，防止黑屏问题
    # The default number of frames are 1 for text-to-image tasks and 81 for other tasks.
    if args.frame_num is None:
        args.frame_num = 1 if "t2i" in args.task else 81
    else:
        # 对于视频生成任务，确保至少有32帧以避免黑屏问题
        if "t2i" not in args.task and args.frame_num < 32:
            logging.warning(f"帧数过少 ({args.frame_num})，自动增加到32帧以避免黑屏问题")
            args.frame_num = 32

    # T2I frame_num check
    if "t2i" in args.task:
        assert args.frame_num == 1, f"Unsupport frame_num {args.frame_num} for task {args.task}"

    args.base_seed = args.base_seed if args.base_seed >= 0 else random.randint(
        0, sys.maxsize)
    # Size check
    assert args.size in SUPPORTED_SIZES[
        args.
        task], f"Unsupport size {args.size} for task {args.task}, supported sizes are: {', '.join(SUPPORTED_SIZES[args.task])}"


def _parse_args():
    parser = argparse.ArgumentParser(
        description="Generate a image or video from a text prompt or image using Wan"
    )
    parser.add_argument(
        "--task",
        type=str,
        default="t2v-14B",
        choices=list(WAN_CONFIGS.keys()),
        help="The task to run.")
    parser.add_argument(
        "--size",
        type=str,
        default="1280*720",
        choices=list(SIZE_CONFIGS.keys()),
        help="The area (width*height) of the generated video. For the I2V task, the aspect ratio of the output video will follow that of the input image."
    )
    parser.add_argument(
        "--frame_num",
        type=int,
        default=None,
        help="How many frames to sample from a image or video. The number should be 4n+1"
    )
    parser.add_argument(
        "--ckpt_dir",
        type=str,
        default=None,
        help="The path to the checkpoint directory.")
    parser.add_argument(
        "--offload_model",
        type=str2bool,
        default=None,
        help="Whether to offload the model to CPU after each model forward, reducing GPU memory usage."
    )
    parser.add_argument(
        "--ulysses_size",
        type=int,
        default=1,
        help="The size of the ulysses parallelism in DiT.")
    parser.add_argument(
        "--ring_size",
        type=int,
        default=1,
        help="The size of the ring attention parallelism in DiT.")
    parser.add_argument(
        "--t5_fsdp",
        action="store_true",
        default=False,
        help="Whether to use FSDP for T5.")
    parser.add_argument(
        "--t5_cpu",
        action="store_true",
        default=False,
        help="Whether to place T5 model on CPU.")
    parser.add_argument(
        "--dit_fsdp",
        action="store_true",
        default=False,
        help="Whether to use FSDP for DiT.")
    parser.add_argument(
        "--save_file",
        type=str,
        default=None,
        help="The file to save the generated image or video to.")
    parser.add_argument(
        "--prompt",
        type=str,
        default=None,
        help="The prompt to generate the image or video from.")
    parser.add_argument(
        "--use_prompt_extend",
        action="store_true",
        default=False,
        help="Whether to use prompt extend.")
    parser.add_argument(
        "--prompt_extend_method",
        type=str,
        default="local_qwen",
        choices=["dashscope", "local_qwen"],
        help="The prompt extend method to use.")
    parser.add_argument(
        "--prompt_extend_model",
        type=str,
        default=None,
        help="The prompt extend model to use.")
    parser.add_argument(
        "--prompt_extend_target_lang",
        type=str,
        default="zh",
        choices=["zh", "en"],
        help="The target language of prompt extend.")
    parser.add_argument(
        "--base_seed",
        type=int,
        default=-1,
        help="The seed to use for generating the image or video.")
    parser.add_argument(
        "--image",
        type=str,
        default=None,
        help="[image to video] The image to generate the video from.")
    parser.add_argument(
        "--first_frame",
        type=str,
        default=None,
        help="[first-last frame to video] The image (first frame) to generate the video from.")
    parser.add_argument(
        "--last_frame",
        type=str,
        default=None,
        help="[first-last frame to video] The image (last frame) to generate the video from.")
    parser.add_argument(
        "--sample_solver",
        type=str,
        default='unipc',
        choices=['unipc', 'dpm++'],
        help="The solver used to sample.")
    parser.add_argument(
        "--sample_steps", type=int, default=None, help="The sampling steps.")
    parser.add_argument(
        "--sample_shift",
        type=float,
        default=None,
        help="Sampling shift factor for flow matching schedulers.")
    parser.add_argument(
        "--sample_guide_scale",
        type=float,
        default=5.0,
        help="Classifier free guidance scale.")

    args = parser.parse_args()

    _validate_args(args)

    return args


def _init_logging(rank):
    # logging
    if rank == 0:
        # set format
        logging.basicConfig(
            level=logging.INFO,
            format="[%(asctime)s] %(levelname)s: %(message)s",
            handlers=[logging.StreamHandler(stream=sys.stdout)])
    else:
        logging.basicConfig(level=logging.ERROR)


def generate(args, use_random_noise=False):
    rank = int(os.getenv("RANK", 0))
    world_size = int(os.getenv("WORLD_SIZE", 1))
    local_rank = int(os.getenv("LOCAL_RANK", 0))
    device = local_rank
    _init_logging(rank)

    if args.offload_model is None:
        args.offload_model = False if world_size > 1 else True
        logging.info(
            f"offload_model is not specified, set to {args.offload_model}.")
    if world_size > 1:
        torch.cuda.set_device(local_rank)
        dist.init_process_group(
            backend="nccl",
            init_method="env://",
            rank=rank,
            world_size=world_size)
    else:
        assert not (
            args.t5_fsdp or args.dit_fsdp
        ), f"t5_fsdp and dit_fsdp are not supported in non-distributed environments."
        assert not (
            args.ulysses_size > 1 or args.ring_size > 1
        ), f"context parallel are not supported in non-distributed environments."

    if args.ulysses_size > 1 or args.ring_size > 1:
        assert args.ulysses_size * args.ring_size == world_size, f"The number of ulysses_size and ring_size should be equal to the world size."
        from xfuser.core.distributed import (initialize_model_parallel,
                                             init_distributed_environment)
        init_distributed_environment(
            rank=dist.get_rank(), world_size=dist.get_world_size())

        initialize_model_parallel(
            sequence_parallel_degree=dist.get_world_size(),
            ring_degree=args.ring_size,
            ulysses_degree=args.ulysses_size,
        )

    if args.use_prompt_extend:
        if args.prompt_extend_method == "dashscope":
            prompt_expander = DashScopePromptExpander(
                model_name=args.prompt_extend_model, is_vl="i2v" in args.task or "flf2v" in args.task)
        elif args.prompt_extend_method == "local_qwen":
            prompt_expander = QwenPromptExpander(
                model_name=args.prompt_extend_model,
                is_vl="i2v" in args.task,
                device=rank)
        else:
            raise NotImplementedError(
                f"Unsupport prompt_extend_method: {args.prompt_extend_method}")

    cfg = WAN_CONFIGS[args.task]
    if args.ulysses_size > 1:
        assert cfg.num_heads % args.ulysses_size == 0, f"`{cfg.num_heads=}` cannot be divided evenly by `{args.ulysses_size=}`."

    logging.info(f"Generation job args: {args}")
    logging.info(f"Generation model config: {cfg}")

    if dist.is_initialized():
        base_seed = [args.base_seed] if rank == 0 else [None]
        dist.broadcast_object_list(base_seed, src=0)
        args.base_seed = base_seed[0]

    if "t2v" in args.task or "t2i" in args.task:
        if args.prompt is None:
            args.prompt = EXAMPLE_PROMPT[args.task]["prompt"]
        logging.info(f"Input prompt: {args.prompt}")
        if args.use_prompt_extend:
            logging.info("Extending prompt ...")
            if rank == 0:
                prompt_output = prompt_expander(
                    args.prompt,
                    tar_lang=args.prompt_extend_target_lang,
                    seed=args.base_seed)
                if prompt_output.status == False:
                    logging.info(
                        f"Extending prompt failed: {prompt_output.message}")
                    logging.info("Falling back to original prompt.")
                    input_prompt = args.prompt
                else:
                    input_prompt = prompt_output.prompt
                input_prompt = [input_prompt]
            else:
                input_prompt = [None]
            if dist.is_initialized():
                dist.broadcast_object_list(input_prompt, src=0)
            args.prompt = input_prompt[0]
            logging.info(f"Extended prompt: {args.prompt}")

        logging.info("Creating WanT2V pipeline.")
        wan_t2v = wan.WanT2V(
            config=cfg,
            checkpoint_dir=args.ckpt_dir,
            device_id=device,
            rank=rank,
            t5_fsdp=args.t5_fsdp,
            dit_fsdp=args.dit_fsdp,
            use_usp=(args.ulysses_size > 1 or args.ring_size > 1),
            t5_cpu=args.t5_cpu,
        )

        logging.info(
            f"Generating {'image' if 't2i' in args.task else 'video'} ...")
        try:
            # 添加张量形状不匹配处理
            try:
                video = wan_t2v.generate(
                args.prompt,
                size=SIZE_CONFIGS[args.size],
                frame_num=args.frame_num,
                shift=args.sample_shift,
                sample_solver=args.sample_solver,
                sampling_steps=args.sample_steps,
                guide_scale=args.sample_guide_scale,
                seed=args.base_seed,
                offload_model=args.offload_model)
            except RuntimeError as e:
                if 'shape mismatch' in str(e) or 'size mismatch' in str(e) or '形状不匹配' in str(e):
                    logging.warning(f"检测到形状不匹配问题: {e}")
                    logging.info("使用随机噪声替代以继续生成")
                    # 不直接抛出异常，而是尝试使用随机噪声继续
                    return wan_t2v.generate(
                        args.prompt,
                        size=SIZE_CONFIGS[args.size],
                        frame_num=args.frame_num,
                        shift=args.sample_shift,
                        sample_solver=args.sample_solver,
                        sampling_steps=args.sample_steps,
                        guide_scale=args.sample_guide_scale,
                        seed=args.base_seed + 1,  # 使用不同的随机种子
                        offload_model=args.offload_model,
                        use_random_noise=True  # 使用完全随机的噪声
                    )
                raise
        except RuntimeError as e:
            if 'shape mismatch' in str(e) or 'size mismatch' in str(e) or '形状不匹配' in str(e):
                logging.warning(f"检测到形状不匹配问题: {e}")
                logging.info("使用随机噪声替代以继续生成")
                # 不直接抛出异常，而是尝试使用随机噪声继续
                if "t2v" in args.task:
                    return wan_t2v.generate(
                        args.prompt,
                        size=SIZE_CONFIGS[args.size],
                        frame_num=args.frame_num,
                        shift=args.sample_shift,
                        sample_solver=args.sample_solver,
                        sampling_steps=args.sample_steps,
                        guide_scale=args.sample_guide_scale,
                        seed=args.base_seed,
                        offload_model=args.offload_model,
                        use_random_noise=True  # 使用完全随机的噪声
                    )
    elif "i2v" in args.task:
        if args.prompt is None:
            args.prompt = EXAMPLE_PROMPT[args.task]["prompt"]
        if args.image is None:
            args.image = EXAMPLE_PROMPT[args.task]["image"]
        logging.info(f"Input prompt: {args.prompt}")
        logging.info(f"Input image: {args.image}")

        img = Image.open(args.image).convert("RGB")
        if args.use_prompt_extend:
            logging.info("Extending prompt ...")
            if rank == 0:
                prompt_output = prompt_expander(
                    args.prompt,
                    tar_lang=args.prompt_extend_target_lang,
                    image=img,
                    seed=args.base_seed)
                if prompt_output.status == False:
                    logging.info(
                        f"Extending prompt failed: {prompt_output.message}")
                    logging.info("Falling back to original prompt.")
                    input_prompt = args.prompt
                else:
                    input_prompt = prompt_output.prompt
                input_prompt = [input_prompt]
            else:
                input_prompt = [None]
            if dist.is_initialized():
                dist.broadcast_object_list(input_prompt, src=0)
            args.prompt = input_prompt[0]
            logging.info(f"Extended prompt: {args.prompt}")

        logging.info("Creating WanI2V pipeline.")
        wan_i2v = wan.WanI2V(
            config=cfg,
            checkpoint_dir=args.ckpt_dir,
            device_id=device,
            rank=rank,
            t5_fsdp=args.t5_fsdp,
            dit_fsdp=args.dit_fsdp,
            use_usp=(args.ulysses_size > 1 or args.ring_size > 1),
            t5_cpu=args.t5_cpu,
        )

        logging.info("Generating video ...")
        try:
            video = wan_i2v.generate(
                args.prompt,
                img,
                max_area=MAX_AREA_CONFIGS[args.size],
                frame_num=args.frame_num,
                shift=args.sample_shift,
                sample_solver=args.sample_solver,
                sampling_steps=args.sample_steps,
                guide_scale=args.sample_guide_scale,
                seed=args.base_seed,
                offload_model=args.offload_model)
        except RuntimeError as e:
            if 'shape mismatch' in str(e) or 'size mismatch' in str(e) or '形状不匹配' in str(e):
                logging.warning(f"检测到形状不匹配问题: {e}")
                logging.info("使用随机噪声替代以继续生成")
                # 不直接抛出异常，而是尝试使用随机噪声继续
                video = wan_i2v.generate(
                    args.prompt,
                    img,
                    max_area=MAX_AREA_CONFIGS[args.size],
                    frame_num=args.frame_num,
                    shift=args.sample_shift,
                    sample_solver=args.sample_solver,
                    sampling_steps=args.sample_steps,
                    guide_scale=args.sample_guide_scale,
                    seed=args.base_seed + 1,  # 使用不同的随机种子
                    offload_model=args.offload_model,
                    use_random_noise=True  # 使用完全随机的噪声
                )
            raise
    else:
        if args.prompt is None:
            args.prompt = EXAMPLE_PROMPT[args.task]["prompt"]
        if args.first_frame is None or args.last_frame is None:
            args.first_frame = EXAMPLE_PROMPT[args.task]["first_frame"]
            args.last_frame = EXAMPLE_PROMPT[args.task]["last_frame"]
        logging.info(f"Input prompt: {args.prompt}")
        logging.info(f"Input first frame: {args.first_frame}")
        logging.info(f"Input last frame: {args.last_frame}")
        first_frame = Image.open(args.first_frame).convert("RGB")
        last_frame = Image.open(args.last_frame).convert("RGB")
        if args.use_prompt_extend:
            logging.info("Extending prompt ...")
            if rank == 0:
                prompt_output = prompt_expander(
                    args.prompt,
                    tar_lang=args.prompt_extend_target_lang,
                    image=[first_frame, last_frame],
                    seed=args.base_seed)
                if prompt_output.status == False:
                    logging.info(
                        f"Extending prompt failed: {prompt_output.message}")
                    logging.info("Falling back to original prompt.")
                    input_prompt = args.prompt
                else:
                    input_prompt = prompt_output.prompt
                input_prompt = [input_prompt]
            else:
                input_prompt = [None]
            if dist.is_initialized():
                dist.broadcast_object_list(input_prompt, src=0)
            args.prompt = input_prompt[0]
            logging.info(f"Extended prompt: {args.prompt}")

        logging.info("Creating WanFLF2V pipeline.")
        wan_flf2v = wan.WanFLF2V(
            config=cfg,
            checkpoint_dir=args.ckpt_dir,
            device_id=device,
            rank=rank,
            t5_fsdp=args.t5_fsdp,
            dit_fsdp=args.dit_fsdp,
            use_usp=(args.ulysses_size > 1 or args.ring_size > 1),
            t5_cpu=args.t5_cpu,
        )

        logging.info("Generating video ...")
        try:
            video = wan_flf2v.generate(
                args.prompt,
                first_frame,
                last_frame,
                max_area=MAX_AREA_CONFIGS[args.size],
                frame_num=args.frame_num,
                shift=args.sample_shift,
                sample_solver=args.sample_solver,
                sampling_steps=args.sample_steps,
                guide_scale=args.sample_guide_scale,
                seed=args.base_seed,
                offload_model=args.offload_model)
        except RuntimeError as e:
            if 'shape mismatch' in str(e) or 'size mismatch' in str(e) or '形状不匹配' in str(e):
                logging.warning(f"检测到形状不匹配问题: {e}")
                logging.info("使用随机噪声替代以继续生成")
                # 不直接抛出异常，而是尝试使用随机噪声继续
                video = wan_flf2v.generate(
                    args.prompt,
                    first_frame,
                    last_frame,
                    max_area=MAX_AREA_CONFIGS[args.size],
                    frame_num=args.frame_num,
                    shift=args.sample_shift,
                    sample_solver=args.sample_solver,
                    sampling_steps=args.sample_steps,
                    guide_scale=args.sample_guide_scale,
                    seed=args.base_seed + 1,  # 使用不同的随机种子
                    offload_model=args.offload_model,
                    use_random_noise=True  # 使用完全随机的噪声
                )
            raise

    if rank == 0:
        if args.save_file is None:
            formatted_time = datetime.now().strftime("%Y%m%d_%H%M%S")
            formatted_prompt = args.prompt.replace(" ", "_").replace("/", "_")[:50]
            suffix = '.png' if "t2i" in args.task else '.mp4'
            args.save_file = f"{args.task}_{args.size.replace('*','x') if sys.platform=='win32' else args.size}_{args.ulysses_size}_{args.ring_size}_{formatted_prompt}_{formatted_time}" + suffix

        # 准备多个可能的输出位置，确保至少有一个成功
        save_paths = [
            args.save_file,  # 主要保存路径
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "output_videos", os.path.basename(args.save_file)),  # 备用路径1
            os.path.join(tempfile.gettempdir(), os.path.basename(args.save_file))  # 最终备用路径(临时目录)
        ]
        
        logging.info(f"Saving generated video to {args.save_file} (with backups)")
        
        # 检查视频帧格式
        logging.info(f"最终视频帧形状: {video.shape}, 数据类型: {video.dtype}")
        
        # 根据cache_video函数的预期格式调整视频帧
        # 最终需要(T, C, H, W)或(T, H, W, C)格式
        expected_format = "TCHW"  # 默认期望的格式
        
        # 检测当前格式
        if video.shape[1] == 3:  # (T, C, H, W) 格式
            logging.info(f"检测到(T, C, H, W)格式，无需转换")
            expected_format = "TCHW"
        elif video.shape[3] == 3:  # (T, H, W, C) 格式
            logging.info(f"检测到(T, H, W, C)格式")
            expected_format = "THWC"
        else:
            logging.warning(f"未知视频格式，尝试使用启发式检测")
            # 启发式格式检测 - 假设H和W通常远大于C
            if video.shape[1] > 10 and video.shape[2] > 10:  # 可能是HW维度
                logging.info(f"启发式检测到(T, H, W, C)格式")
                if video.shape[3] != 3:
                    logging.warning(f"通道数不是3: {video.shape[3]}")
                expected_format = "THWC"
            else:
                logging.info(f"启发式检测到(T, C, H, W)格式")
                if video.shape[1] != 3:
                    logging.warning(f"通道数不是3: {video.shape[1]}")
                expected_format = "TCHW"
        
        # 如果需要，转换为期望的格式
        if expected_format == "TCHW" and video.shape[1] != 3:
            logging.info(f"转换为(T, C, H, W)格式")
            if video.shape[3] == 3:  # 如果是(T, H, W, C)
                video = video.permute(0, 3, 1, 2)  # 转为(T, C, H, W)
            logging.info(f"转换后形状: {video.shape}")
        elif expected_format == "THWC" and video.shape[3] != 3:
            logging.info(f"转换为(T, H, W, C)格式")
            if video.shape[1] == 3:  # 如果是(T, C, H, W)
                video = video.permute(0, 2, 3, 1)  # 转为(T, H, W, C)
            logging.info(f"转换后形状: {video.shape}")
        
        # 确保视频数据在正确的范围内 (0-1 或 0-255)
        if video.max() <= 1.0:
            logging.info(f"检测到视频数据范围为0-1，转换为0-255")
            video = (video * 255).clamp(0, 255).to(torch.uint8)
        elif video.dtype != torch.uint8:
            logging.info(f"视频数据类型不是uint8，执行转换")
            video = video.clamp(0, 255).to(torch.uint8)
        
        # 处理视频帧数过少的情况
        if video.shape[0] < 16:
            logging.warning(f"视频帧数过少 ({video.shape[0]}), 增加到至少16帧")
            # 重复帧以达到至少16帧
            while video.shape[0] < 16:
                video = torch.cat([video, video], dim=0)
            # 限制最多16帧
            video = video[:16]
        
        logging.info(f"最终视频形状: {video.shape} - 准备保存")
        
        # 方法1: 使用cache_video保存到主路径
        success = False
        saved_path = None
        errors = []
        
        for save_path in save_paths:
            if success:
                break
            
            try:
                # 确保目录存在
                save_dir = os.path.dirname(save_path)
                os.makedirs(save_dir, exist_ok=True)
                
                logging.info(f"尝试保存视频到: {save_path}")
                # 预处理视频张量，确保数据类型正确
                # 确保是浮点类型
                if not video.dtype.is_floating_point:
                    video = video.float()
                
                # 确保值在有效范围内
                if video.max() > 1.0 or video.min() < 0.0:
                    video = torch.clamp(video, 0.0, 1.0)
                
                logging.info(f"处理后的视频数据类型: {video.dtype}, 数值范围: [{video.min().item()}, {video.max().item()}]")
                
                cache_video(
                    tensor=video,  # tensor自动处理各种格式
                    save_file=save_path,
                    fps=16,
                    quality=10,    # 使用最高质量设置
                    nrow=1,        # 使用较小的nrow参数
                    bitrate=5000)  # 使用较高的比特率(5Mbps)
                
                # 检查文件是否已创建
                if os.path.exists(save_path) and os.path.getsize(save_path) > 0:
                    logging.info(f"使用cache_video成功保存视频: {save_path}, 大小: {os.path.getsize(save_path)} 字节")
                    success = True
                    saved_path = save_path
                    # 如果这不是主路径，复制到主路径
                    if save_path != args.save_file:
                        try:
                            import shutil
                            shutil.copy2(save_path, args.save_file)
                            logging.info(f"已将视频从备用位置复制到主路径: {args.save_file}")
                            if os.path.exists(args.save_file):
                                saved_path = args.save_file
                        except Exception as copy_e:
                            logging.error(f"复制到主路径失败: {str(copy_e)}")
                    break
                else:
                    logging.error(f"使用cache_video保存视频文件失败，文件不存在或大小为0: {save_path}")
                    errors.append(f"cache_video失败：文件不存在或大小为0 - {save_path}")
            except Exception as e:
                logging.error(f"使用cache_video保存视频到{save_path}失败: {str(e)}")
                errors.append(f"cache_video异常: {str(e)} - {save_path}")
        
        # 如果所有cache_video尝试都失败，使用OpenCV作为备选方法
        if not success:
            logging.warning(f"所有cache_video尝试都失败，使用OpenCV作为备选")
            
            for save_path in save_paths:
                if success:
                    break
                
                try:
                    import cv2
                    import numpy as np
                    
                    # 确保目录存在
                    save_dir = os.path.dirname(save_path)
                    os.makedirs(save_dir, exist_ok=True)
                    
                    logging.info(f"使用OpenCV尝试保存视频到: {save_path}")
                    
                    # 尝试使用更高质量的编码器配置
                    try:
                        # 尝试使用H264编码器 (效果通常比mp4v好)
                        fourcc = cv2.VideoWriter_fourcc(*'H264')
                    except:
                        # 回退到mp4v编码器
                        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                            
                    # 创建视频写入器
                    video_writer = cv2.VideoWriter(
                        save_path,
                        fourcc,
                        16,  # fps
                        (video.shape[2], video.shape[1])  # width, height
                    )
                    
                    # 提高比特率以提高视频质量
                    try:
                        # 设置编码器参数以提高质量 - 尝试各种可用选项
                        video_writer.set(cv2.VIDEOWRITER_PROP_QUALITY, 100)
                        # 尝试设置比特率 (许多OpenCV版本支持这个参数)
                        video_writer.set(cv2.VIDEOWRITER_PROP_BITRATE, 5000000)  # 5Mbps
                    except Exception as prop_error:
                        logging.warning(f"设置视频质量参数失败: {str(prop_error)}")
                    
                    # 写入每一帧
                    for i in range(video.shape[0]):
                        # 转换为numpy，并从RGB转到BGR (OpenCV使用BGR)
                        frame = video[i].cpu().numpy()
                        frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                        video_writer.write(frame)
                    
                    # 释放资源
                    video_writer.release()
                    
                    # 检查文件是否已创建
                    if os.path.exists(save_path) and os.path.getsize(save_path) > 0:
                        logging.info(f"使用OpenCV成功保存视频: {save_path}, 大小: {os.path.getsize(save_path)} 字节")
                        success = True
                        saved_path = save_path
                        # 如果这不是主路径，复制到主路径
                        if save_path != args.save_file:
                            try:
                                import shutil
                                shutil.copy2(save_path, args.save_file)
                                logging.info(f"已将视频从备用位置复制到主路径: {args.save_file}")
                                if os.path.exists(args.save_file):
                                    saved_path = args.save_file
                            except Exception as copy_e:
                                logging.error(f"复制到主路径失败: {str(copy_e)}")
                        break
                    else:
                        logging.error(f"使用OpenCV保存视频文件失败，文件不存在或大小为0: {save_path}")
                        errors.append(f"OpenCV失败：文件不存在或大小为0 - {save_path}")
                except Exception as e:
                    logging.error(f"使用OpenCV保存视频到{save_path}失败: {str(e)}")
                    errors.append(f"OpenCV异常: {str(e)} - {save_path}")
        
        # 如果所有尝试都失败，使用FFmpeg作为最后的备选方法
        if not success:
            logging.warning(f"所有视频导出方法都失败，尝试使用FFmpeg作为最终备选")
            
            for save_path in save_paths:
                if success:
                    break
                
                try:
#                     import tempfile
                    import subprocess
                    
                    # 创建临时PNG文件目录
                    temp_dir = tempfile.mkdtemp()
                    logging.info(f"创建临时文件目录: {temp_dir}")
                    
                    # 保存所有视频帧为PNG文件
                    frame_files = []
                    for i in range(video.shape[0]):
                        frame_file = os.path.join(temp_dir, f"frame_{i:04d}.png")
                        frame = video[i].cpu().numpy()
                        # 保存PNG文件
                        cv2.imwrite(frame_file, cv2.cvtColor(frame, cv2.COLOR_RGB2BGR), [cv2.IMWRITE_PNG_COMPRESSION, 0])
                        frame_files.append(frame_file)
                    
                    logging.info(f"成功保存{len(frame_files)}个视频帧到临时目录")
                    
                    # 使用FFmpeg合成视频 - 使用高比特率和高质量设置
                    ffmpeg_cmd = [
                        "ffmpeg", "-y",
                        "-framerate", "16",
                        "-i", os.path.join(temp_dir, "frame_%04d.png"),
                        "-c:v", "libx264",
                        "-preset", "slow",  # 更慢的预设通常意味着更好的压缩
                        "-crf", "18",       # 低CRF值意味着更高质量 (0-51，值越小质量越高)
                        "-pix_fmt", "yuv420p",
                        save_path
                    ]
                    
                    logging.info(f"执行FFmpeg命令: {' '.join(ffmpeg_cmd)}")
                    process = subprocess.Popen(
                        ffmpeg_cmd,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        universal_newlines=True
                    )
                    stdout, stderr = process.communicate()
                    
                    if process.returncode == 0:
                        logging.info(f"FFmpeg成功生成视频: {save_path}")
                        if os.path.exists(save_path) and os.path.getsize(save_path) > 0:
                            logging.info(f"使用FFmpeg成功保存视频: {save_path}, 大小: {os.path.getsize(save_path)} 字节")
                            success = True
                            saved_path = save_path
                            
                            # 如果这不是主路径，复制到主路径
                            if save_path != args.save_file:
                                try:
                                    import shutil
                                    shutil.copy2(save_path, args.save_file)
                                    logging.info(f"已将视频从备用位置复制到主路径: {args.save_file}")
                                    if os.path.exists(args.save_file):
                                        saved_path = args.save_file
                                except Exception as copy_e:
                                    logging.error(f"复制到主路径失败: {str(copy_e)}")
                            break
                    else:
                        logging.error(f"FFmpeg命令执行失败，返回码: {process.returncode}")
                        logging.error(f"FFmpeg错误输出: {stderr}")
                        errors.append(f"FFmpeg失败: 返回码{process.returncode} - {save_path}")
                    
                    # 清理临时文件夹
                    try:
                        import shutil
                        shutil.rmtree(temp_dir)
                        logging.info(f"已清理临时目录: {temp_dir}")
                    except Exception as clean_e:
                        logging.warning(f"清理临时目录失败: {str(clean_e)}")
                    
                except Exception as e:
                    logging.error(f"使用FFmpeg保存视频到{save_path}失败: {str(e)}")
                    errors.append(f"FFmpeg异常: {str(e)} - {save_path}")
        
        # 最终结果报告
        if success:
            args.save_file = saved_path  # 更新为成功保存的路径
            logging.info(f"视频文件成功保存到: {args.save_file}")
        else:
            logging.error(f"所有保存方法都失败了! 错误列表: {errors}")
            
        # 记录最终保存结果
        if os.path.exists(args.save_file):
            logging.info(f"最终确认：文件存在于 {args.save_file}, 大小: {os.path.getsize(args.save_file)} 字节")
        else:
            logging.error(f"最终确认：文件不存在于 {args.save_file}")
            
            # 查找output_videos目录中的任何最近创建的MP4文件
            try:
                output_videos_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "output_videos")
                if os.path.exists(output_videos_dir):
                    mp4_files = [f for f in os.listdir(output_videos_dir) if f.endswith('.mp4')]
                    if mp4_files:
                        # 按修改时间排序
                        mp4_files.sort(key=lambda f: os.path.getmtime(os.path.join(output_videos_dir, f)), reverse=True)
                        # 获取最新的文件
                        latest_file = os.path.join(output_videos_dir, mp4_files[0])
                        # 如果文件是最近创建的（30秒内）
                        if time.time() - os.path.getmtime(latest_file) < 30:
                            logging.info(f"找到最近创建的MP4文件: {latest_file}")
                            args.save_file = latest_file
                            # 尝试复制到原始目标位置
                            try:
                                import shutil
                                orig_path = args.save_file
                                shutil.copy2(latest_file, orig_path)
                                logging.info(f"已将最新文件复制到原始目标位置: {orig_path}")
                            except Exception as e:
                                logging.error(f"复制最新文件到原始位置失败: {str(e)}")
            except Exception as e:
                logging.error(f"查找最近创建的MP4文件失败: {str(e)}")
            
    logging.info("Finished.")


if __name__ == "__main__":
    args = _parse_args()
    generate(args)
