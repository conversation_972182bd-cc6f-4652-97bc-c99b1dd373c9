"""
GPU诊断API
提供GPU状态检查、优化建议和问题修复功能
"""
import logging
from typing import Dict, Any
from fastapi import APIRouter, HTTPException
from pydantic import BaseModel

from app.services.gpu_diagnostic import gpu_diagnostic
from app.services.video_generation_optimizer import video_optimizer

logger = logging.getLogger(__name__)

router = APIRouter()

class GPUOptimizationRequest(BaseModel):
    """GPU优化请求"""
    model_type: str = "wan2.1"
    task_type: str = "t2v"  # t2v, i2v, t2i
    resolution: str = "768x512"
    frame_count: int = 81
    inference_steps: int = 50

class GPUDiagnosticResponse(BaseModel):
    """GPU诊断响应"""
    success: bool
    gpu_available: bool
    diagnostic_results: Dict[str, Any]
    recommendations: list
    quick_fixes: list

@router.get("/status", response_model=Dict[str, Any])
async def get_gpu_status():
    """获取GPU状态"""
    try:
        # 检查GPU环境
        gpu_info = video_optimizer.check_gpu_environment()
        usage_info = video_optimizer.monitor_gpu_usage()
        
        return {
            "success": True,
            "gpu_info": gpu_info,
            "usage_info": usage_info,
            "timestamp": video_optimizer.gpu_info.get("timestamp", None)
        }
    except Exception as e:
        logger.error(f"获取GPU状态失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取GPU状态失败: {str(e)}")

@router.post("/diagnostic", response_model=GPUDiagnosticResponse)
async def run_gpu_diagnostic():
    """运行完整的GPU诊断"""
    try:
        logger.info("开始GPU诊断...")
        
        # 运行诊断
        diagnostic_results = gpu_diagnostic.run_full_diagnostic()
        
        # 获取快速修复命令
        quick_fixes = gpu_diagnostic.get_quick_fix_commands()
        
        return GPUDiagnosticResponse(
            success=True,
            gpu_available=diagnostic_results.get("cuda_status", {}).get("available", False),
            diagnostic_results=diagnostic_results,
            recommendations=diagnostic_results.get("recommendations", []),
            quick_fixes=quick_fixes
        )
        
    except Exception as e:
        logger.error(f"GPU诊断失败: {e}")
        raise HTTPException(status_code=500, detail=f"GPU诊断失败: {str(e)}")

@router.post("/optimize", response_model=Dict[str, Any])
async def get_optimization_recommendations(request: GPUOptimizationRequest):
    """获取GPU优化建议"""
    try:
        logger.info(f"生成优化建议: {request.model_type} - {request.task_type}")
        
        # 基础参数
        base_args = {
            "task": request.task_type,
            "size": request.resolution,
            "frame_num": request.frame_count,
            "sample_steps": request.inference_steps,
            "device": "auto"
        }
        
        # 获取优化参数
        optimized_args = video_optimizer.optimize_wan_args(base_args)
        
        # 获取优化报告
        optimization_report = video_optimizer.get_optimization_report()
        
        return {
            "success": True,
            "original_args": base_args,
            "optimized_args": optimized_args,
            "optimization_report": optimization_report,
            "performance_tips": [
                "使用推荐的分辨率和帧数设置",
                "启用内存优化选项以避免OOM错误",
                "定期清理GPU缓存以释放内存",
                "监控GPU温度和利用率"
            ]
        }
        
    except Exception as e:
        logger.error(f"生成优化建议失败: {e}")
        raise HTTPException(status_code=500, detail=f"生成优化建议失败: {str(e)}")

@router.post("/cleanup")
async def cleanup_gpu_memory():
    """清理GPU内存"""
    try:
        logger.info("清理GPU内存...")
        
        # 清理GPU内存
        video_optimizer.cleanup_gpu_memory()
        
        # 获取清理后的状态
        usage_info = video_optimizer.monitor_gpu_usage()
        
        return {
            "success": True,
            "message": "GPU内存清理完成",
            "usage_after_cleanup": usage_info
        }
        
    except Exception as e:
        logger.error(f"GPU内存清理失败: {e}")
        raise HTTPException(status_code=500, detail=f"GPU内存清理失败: {str(e)}")

@router.get("/recommendations", response_model=Dict[str, Any])
async def get_gpu_recommendations():
    """获取GPU使用建议"""
    try:
        # 获取当前GPU状态
        gpu_info = video_optimizer.check_gpu_environment()
        
        if not gpu_info.get("available", False):
            return {
                "success": True,
                "gpu_available": False,
                "recommendations": [
                    {
                        "category": "硬件",
                        "priority": "high",
                        "title": "GPU不可用",
                        "description": "系统未检测到可用的GPU",
                        "solutions": [
                            "检查NVIDIA驱动是否正确安装",
                            "确认GPU硬件连接正常",
                            "安装CUDA工具包",
                            "重启系统以刷新驱动状态"
                        ]
                    },
                    {
                        "category": "软件",
                        "priority": "high", 
                        "title": "PyTorch GPU支持",
                        "description": "安装支持GPU的PyTorch版本",
                        "solutions": [
                            "pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118",
                            "验证安装: python -c \"import torch; print(torch.cuda.is_available())\"",
                            "检查CUDA版本兼容性"
                        ]
                    }
                ]
            }
        
        # GPU可用时的建议
        memory_gb = gpu_info.get("memory_free_gb", 0)
        device_name = gpu_info.get("device_name", "Unknown")
        
        recommendations = []
        
        # 内存相关建议
        if memory_gb < 6:
            recommendations.append({
                "category": "内存优化",
                "priority": "high",
                "title": "GPU内存不足",
                "description": f"当前可用内存: {memory_gb:.1f}GB，建议6GB+",
                "solutions": [
                    "启用模型卸载 (--offload_model)",
                    "使用较小的分辨率 (512x512)",
                    "减少帧数到33帧以下",
                    "启用FP16精度",
                    "考虑使用CPU模式"
                ]
            })
        elif memory_gb < 12:
            recommendations.append({
                "category": "内存优化", 
                "priority": "medium",
                "title": "中等GPU内存",
                "description": f"当前可用内存: {memory_gb:.1f}GB，可以进行优化",
                "solutions": [
                    "启用T5模型CPU卸载 (--t5_cpu)",
                    "使用768x512分辨率",
                    "限制帧数到65帧",
                    "启用梯度检查点"
                ]
            })
        else:
            recommendations.append({
                "category": "性能优化",
                "priority": "low", 
                "title": "充足GPU内存",
                "description": f"当前可用内存: {memory_gb:.1f}GB，可以使用高性能设置",
                "solutions": [
                    "可以使用较高分辨率 (1280x720)",
                    "增加帧数到81帧",
                    "禁用模型卸载以提高速度",
                    "使用更多推理步数提高质量"
                ]
            })
        
        # GPU特定建议
        if "rtx" in device_name.lower():
            recommendations.append({
                "category": "硬件优化",
                "priority": "medium",
                "title": "RTX GPU优化",
                "description": f"检测到RTX GPU: {device_name}",
                "solutions": [
                    "启用TensorRT优化",
                    "使用FP16精度",
                    "启用Flash Attention (如果支持)",
                    "考虑使用DLSS相关技术"
                ]
            })
        
        return {
            "success": True,
            "gpu_available": True,
            "gpu_info": gpu_info,
            "recommendations": recommendations
        }
        
    except Exception as e:
        logger.error(f"获取GPU建议失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取GPU建议失败: {str(e)}")

@router.get("/test", response_model=Dict[str, Any])
async def test_gpu_functionality():
    """测试GPU基本功能"""
    try:
        logger.info("测试GPU基本功能...")
        
        test_results = {
            "cuda_available": False,
            "basic_operations": False,
            "memory_allocation": False,
            "tensor_operations": False,
            "error_messages": []
        }
        
        try:
            import torch
            
            # 测试CUDA可用性
            test_results["cuda_available"] = torch.cuda.is_available()
            
            if test_results["cuda_available"]:
                # 测试基本操作
                device = torch.device("cuda:0")
                
                # 测试内存分配
                try:
                    test_tensor = torch.randn(100, 100).to(device)
                    test_results["memory_allocation"] = True
                except Exception as e:
                    test_results["error_messages"].append(f"内存分配失败: {e}")
                
                # 测试张量运算
                try:
                    if test_results["memory_allocation"]:
                        result = torch.mm(test_tensor, test_tensor)
                        test_results["tensor_operations"] = True
                        test_results["basic_operations"] = True
                except Exception as e:
                    test_results["error_messages"].append(f"张量运算失败: {e}")
                
                # 清理
                try:
                    del test_tensor
                    torch.cuda.empty_cache()
                except:
                    pass
            else:
                test_results["error_messages"].append("CUDA不可用")
                
        except ImportError:
            test_results["error_messages"].append("PyTorch未安装")
        except Exception as e:
            test_results["error_messages"].append(f"GPU测试异常: {e}")
        
        # 生成测试报告
        if test_results["basic_operations"]:
            status = "✅ GPU功能正常"
            recommendations = ["GPU工作正常，可以正常使用视频生成功能"]
        elif test_results["cuda_available"]:
            status = "⚠️  GPU部分功能异常"
            recommendations = [
                "CUDA可用但存在问题",
                "检查GPU驱动版本",
                "尝试重启系统",
                "考虑重新安装CUDA"
            ]
        else:
            status = "❌ GPU不可用"
            recommendations = [
                "安装NVIDIA驱动",
                "安装CUDA工具包", 
                "安装GPU版本的PyTorch",
                "检查硬件连接"
            ]
        
        return {
            "success": True,
            "status": status,
            "test_results": test_results,
            "recommendations": recommendations
        }
        
    except Exception as e:
        logger.error(f"GPU功能测试失败: {e}")
        raise HTTPException(status_code=500, detail=f"GPU功能测试失败: {str(e)}")
