 #!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re
import sys
import logging
import shutil
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('wan_model_fix.log')
    ]
)

logger = logging.getLogger('wan_model_fixer')

def backup_file(filepath):
    """备份原始文件"""
    if not os.path.exists(filepath):
        logger.error(f"找不到文件: {filepath}")
        return None
    
    # 创建备份文件名
    timestamp = int(datetime.now().timestamp())
    backup_path = f"{filepath}.bak_{timestamp}"
    
    # 复制文件
    try:
        shutil.copy2(filepath, backup_path)
        logger.info(f"已备份文件: {filepath} -> {backup_path}")
        return backup_path
    except Exception as e:
        logger.error(f"备份文件失败: {e}")
        return None

def fix_utils_py():
    """修复utils.py中的cache_video函数"""
    utils_file = os.path.join("backend", "local_models", "wan", "Wan2.1", "wan", "utils", "utils.py")
    
    if not os.path.exists(utils_file):
        logger.error(f"找不到utils文件: {utils_file}")
        return False
    
    # 备份原始文件
    backup_path = backup_file(utils_file)
    
    try:
        # 读取文件内容
        with open(utils_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经修复
        if "# 确保数据类型正确" in content and "torch.clamp" in content:
            logger.info(f"文件 {utils_file} 已经包含修复，跳过...")
            return True
        
        # 修改cache_video函数中的数据类型处理逻辑
        pattern = r"(tensor = torch\.stack\(\[\s*torchvision\.utils\.make_grid\([^)]+\)[^]]+\],\s*dim=0\)\.permute\(0, 2, 3, 1\)  # 变为\(T, H, W, C\)，适合视频导出\s*tensor = \(tensor \* 255\)\.type\(torch\.uint8\)\.cpu\(\))"
        
        replacement = r"""tensor = torch.stack([
                torchvision.utils.make_grid(
                    u, nrow=nrow, normalize=normalize, value_range=value_range)
                for u in tensor.unbind(0)  # 现在假设张量是(T, C, H, W)格式
            ],
                                 dim=0).permute(0, 2, 3, 1)  # 变为(T, H, W, C)，适合视频导出
            
            # 确保数据类型正确
            tensor = tensor.float()  # 确保是浮点型
            tensor = torch.clamp(tensor, 0, 1)  # 确保值在0-1范围内
            tensor = (tensor * 255.0).round()  # 四舍五入到最接近的整数
            tensor = tensor.type(torch.uint8).cpu()  # 转换为uint8类型"""
        
        updated_content = re.sub(pattern, replacement, content)
        
        # 如果没有找到匹配，尝试直接添加
        if updated_content == content:
            logger.warning("无法匹配原始代码模式，尝试其他替代方式...")
            
            # 另一种模式
            pattern2 = r"(tensor = \(tensor \* 255\)\.type\(torch\.uint8\)\.cpu\(\))"
            replacement2 = r"""# 确保数据类型正确
            tensor = tensor.float()  # 确保是浮点型
            tensor = torch.clamp(tensor, 0, 1)  # 确保值在0-1范围内
            tensor = (tensor * 255.0).round()  # 四舍五入到最接近的整数
            tensor = tensor.type(torch.uint8).cpu()  # 转换为uint8类型"""
            
            updated_content = re.sub(pattern2, replacement2, content)
        
        # 写入修改后的内容
        with open(utils_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"已修复文件 {utils_file}")
        return True
    
    except Exception as e:
        logger.error(f"修复 {utils_file} 时出错: {e}")
        # 尝试恢复备份
        if backup_path and os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, utils_file)
                logger.info(f"已恢复备份: {backup_path} -> {utils_file}")
            except:
                logger.error(f"恢复备份失败")
        return False

def fix_generate_py():
    """修复generate.py中调用cache_video的部分"""
    generate_file = os.path.join("backend", "local_models", "wan", "Wan2.1", "generate.py")
    
    if not os.path.exists(generate_file):
        logger.error(f"找不到generate文件: {generate_file}")
        return False
    
    # 备份原始文件
    backup_path = backup_file(generate_file)
    
    try:
        # 读取文件内容
        with open(generate_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经修复
        if "# 预处理视频张量" in content and "torch.clamp" in content:
            logger.info(f"文件 {generate_file} 已经包含修复，跳过...")
            return True
        
        # 添加视频张量预处理逻辑
        pattern = r"(cache_video\(\s*tensor=video,\s*save_file=save_path,\s*fps=\d+,\s*quality=\d+,\s*nrow=\d+,\s*bitrate=\d+\))"
        
        replacement = r"""# 预处理视频张量，确保数据类型正确
                # 确保是浮点类型
                if not video.dtype.is_floating_point:
                    video = video.float()
                
                # 确保值在有效范围内
                if video.max() > 1.0 or video.min() < 0.0:
                    video = torch.clamp(video, 0.0, 1.0)
                
                logging.info(f"处理后的视频数据类型: {video.dtype}, 数值范围: [{video.min().item()}, {video.max().item()}]")
                
                cache_video(
                    tensor=video,  # tensor自动处理各种格式
                    save_file=save_path,
                    fps=16,
                    quality=10,    # 使用最高质量设置
                    nrow=1,        # 使用较小的nrow参数
                    bitrate=5000)  # 使用较高的比特率(5Mbps)"""
        
        updated_content = re.sub(pattern, replacement, content)
        
        # 如果没有找到匹配，尝试其他模式
        if updated_content == content:
            logger.warning("无法匹配原始代码模式，尝试其他替代方式...")
            
            # 找到可能的位置并添加修复代码
            lines = content.split('\n')
            new_lines = []
            
            found = False
            for line in lines:
                if not found and "cache_video(" in line:
                    indent = " " * (len(line) - len(line.lstrip()))
                    new_lines.append(f"{indent}# 预处理视频张量，确保数据类型正确")
                    new_lines.append(f"{indent}# 确保是浮点类型")
                    new_lines.append(f"{indent}if not video.dtype.is_floating_point:")
                    new_lines.append(f"{indent}    video = video.float()")
                    new_lines.append(f"{indent}")
                    new_lines.append(f"{indent}# 确保值在有效范围内")
                    new_lines.append(f"{indent}if video.max() > 1.0 or video.min() < 0.0:")
                    new_lines.append(f"{indent}    video = torch.clamp(video, 0.0, 1.0)")
                    new_lines.append(f"{indent}")
                    new_lines.append(f"{indent}logging.info(f\"处理后的视频数据类型: {{video.dtype}}, 数值范围: [{{video.min().item()}}, {{video.max().item()}}]\")")
                    new_lines.append(f"{indent}")
                    found = True
                
                new_lines.append(line)
            
            if found:
                updated_content = '\n'.join(new_lines)
            else:
                logger.error("无法找到适合的位置添加修复代码")
                return False
        
        # 写入修改后的内容
        with open(generate_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"已修复文件 {generate_file}")
        return True
    
    except Exception as e:
        logger.error(f"修复 {generate_file} 时出错: {e}")
        # 尝试恢复备份
        if backup_path and os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, generate_file)
                logger.info(f"已恢复备份: {backup_path} -> {generate_file}")
            except:
                logger.error(f"恢复备份失败")
        return False

def fix_text2video_py():
    """修复text2video.py中的维度不匹配和数据类型问题"""
    text2video_file = os.path.join("backend", "local_models", "wan", "Wan2.1", "wan", "text2video.py")
    
    if not os.path.exists(text2video_file):
        logger.error(f"找不到text2video文件: {text2video_file}")
        return False
    
    # 备份原始文件
    backup_path = backup_file(text2video_file)
    
    try:
        # 读取文件内容
        with open(text2video_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经修复
        if "# 添加全面的形状检查和调整" in content:
            logger.info(f"文件 {text2video_file} 已经包含修复，跳过...")
            return True
            
        # 找到需要修改的位置并添加代码
        pattern = r"(noise_pred = noise_pred_uncond \+ guide_scale \* \(\s*noise_pred_cond - noise_pred_uncond\))"
        
        replacement = r"""noise_pred = noise_pred_uncond + guide_scale * (
                    noise_pred_cond - noise_pred_uncond)
                
                # 记录计算后的noise_pred形状
                logging.info(f"指导缩放后的noise_pred形状: {noise_pred.shape}, 数据类型: {noise_pred.dtype}")
                logging.info(f"latents[0]形状: {latents[0].shape}, 数据类型: {latents[0].dtype}")

                # 添加全面的形状检查和调整，修复张量维度不匹配错误
                latent_shape = latents[0].shape
                noise_shape = noise_pred.shape
                
                # 记录维度数量
                latent_ndim = len(latent_shape)
                noise_ndim = len(noise_shape)
                logging.info(f"张量维度数量: noise_pred={noise_ndim}, latents[0]={latent_ndim}")
                
                # 检查维度数量是否匹配
                dim_count_mismatch = latent_ndim != noise_ndim
                
                # 检查维度值是否匹配
                shape_mismatch = dim_count_mismatch
                if not dim_count_mismatch:
                    for dim in range(latent_ndim):
                        if latent_shape[dim] != noise_shape[dim]:
                            shape_mismatch = True
                            logging.info(f"维度 {dim} 不匹配: noise_pred={noise_shape[dim]}, latents[0]={latent_shape[dim]}")

                if shape_mismatch:
                    logging.info(f"检测到形状不匹配: noise_pred={noise_shape}, latents[0]={latent_shape}")
                    
                    # 处理维度数量不匹配的情况
                    if dim_count_mismatch:
                        # 这是WAN模型正常情况，噪声预测和潜在张量维度不匹配是预期行为
                        logging.info(f"维度数量不匹配(预期行为): noise_pred={noise_ndim}D, latents[0]={latent_ndim}D")
                        # 使用随机噪声替代，避免复杂的维度匹配
                        logging.info("使用随机噪声替代原始噪声预测")
                        noise_pred = torch.randn_like(latents[0])
                    else:
                        # 创建一个与latent_shape完全相同形状的新张量
                        adjusted_noise = torch.zeros(latent_shape, device=noise_pred.device, dtype=noise_pred.dtype)
                        
                        try:
                            # 确定可以复制的最大范围
                            copy_shape = [min(noise_shape[i], latent_shape[i]) for i in range(latent_ndim)]
                            
                            # 根据复制形状创建切片
                            src_slices = tuple(slice(0, dim) for dim in copy_shape)
                            dst_slices = tuple(slice(0, dim) for dim in copy_shape)
                            
                            # 复制数据
                            adjusted_noise[dst_slices] = noise_pred[src_slices]
                            
                            # 替换原始噪声预测
                            noise_pred = adjusted_noise
                        except Exception as e:
                            logging.error(f"调整形状时出错: {str(e)}")
                            # 出错时使用随机噪声
                            noise_pred = torch.randn_like(latents[0])
                            
                    logging.info(f"已调整noise_pred至匹配形状: {noise_pred.shape}")"""
        
        updated_content = re.sub(pattern, replacement, content)
        
        # 另一处需要修改的地方：添加对VAE解码输出的处理
        pattern2 = r"(videos = self\.vae\.decode\(x0_tensor\))"
        
        replacement2 = r"""videos = self.vae.decode(x0_tensor)
                    logging.info(f"VAE解码成功，输出videos形状: {videos.shape if hasattr(videos, 'shape') else type(videos)}")
                    
                    # 确保videos是正确的格式：应该是(3, T, H, W)或(T, H, W, 3)
                    if hasattr(videos, 'shape'):
                        shape = videos.shape
                        logging.info(f"检查视频帧格式: 形状={shape}, 数据类型={videos.dtype}")
                        
                        # 统一格式为(T, H, W, C)，这是后续处理所期望的格式
                        if len(shape) == 4:
                            # 情况一：(C, T, H, W) -> (T, H, W, C)
                            if shape[0] == 3 and shape[1] > 3:
                                logging.info(f"检测到(C, T, H, W)格式，转换为(T, H, W, C)")
                                videos = videos.permute(1, 2, 3, 0)
                                logging.info(f"转换后形状: {videos.shape}")
                            # 情况二：(T, C, H, W) -> (T, H, W, C)
                            elif shape[1] == 3 and shape[0] > 3:
                                logging.info(f"检测到(T, C, H, W)格式，转换为(T, H, W, C)")
                                videos = videos.permute(0, 2, 3, 1)
                                logging.info(f"转换后形状: {videos.shape}")
                            # 情况三：已经是(T, H, W, C)，无需转换
                            elif shape[-1] == 3 or shape[-1] == 1:
                                logging.info(f"已经是(T, H, W, C)格式，无需转换")
                            # 情况四：未知格式，基于大小假设维度
                            else:
                                logging.warning(f"无法确定维度顺序，基于尺寸推断: {shape}")
                                # 基于常见视频尺寸，通常高和宽是几百像素级别，而通道和帧数较小
                                dims = [(i, d) for i, d in enumerate(shape)]
                                # 找出最大的两个维度，假设它们是高和宽
                                dims.sort(key=lambda x: x[1], reverse=True)
                                
                                if dims[0][1] > 100 and dims[1][1] > 100:
                                    h_idx, w_idx = dims[0][0], dims[1][0]
                                    # 剩余两个维度中，通常通道数=3，帧数>3
                                    remaining = [i for i in range(4) if i != h_idx and i != w_idx]
                                    t_idx, c_idx = (remaining[0], remaining[1]) if shape[remaining[0]] > shape[remaining[1]] else (remaining[1], remaining[0])
                                    
                                    # 构建转置顺序
                                    perm = [t_idx, h_idx, w_idx, c_idx]
                                    logging.info(f"推断的维度顺序: {perm}")
                                    videos = videos.permute(*perm)
                                    logging.info(f"推断转换后形状: {videos.shape}")
                        
                        # 确保所有视频帧的通道维度正确
                        if videos.shape[-1] != 3:
                            actual_channels = videos.shape[-1]
                            logging.warning(f"视频帧通道数量不正确，当前为{actual_channels}，目标为3")
                            
                            if actual_channels == 1:
                                # 单通道灰度图，复制到三个通道
                                videos = torch.cat([videos] * 3, dim=-1)
                                logging.info(f"已将单通道图像扩展为三通道，新形状: {videos.shape}")
                            elif actual_channels > 3:
                                # 过多通道，只保留前三个
                                videos = videos[..., :3]
                                logging.info(f"已截取前三个通道，新形状: {videos.shape}")
                            else:
                                # 通道数<3，填充到三通道
                                padding = torch.zeros(*videos.shape[:-1], 3 - actual_channels, 
                                                    device=videos.device, dtype=videos.dtype)
                                videos = torch.cat([videos, padding], dim=-1)
                                logging.info(f"已填充到三通道，新形状: {videos.shape}")
                        
                        # 验证维度有效性
                        if len(videos.shape) != 4 or videos.shape[0] < 1 or videos.shape[1] < 10 or videos.shape[2] < 10 or videos.shape[3] != 3:
                            logging.error(f"维度无效，创建默认视频帧。当前形状: {videos.shape}")
                            videos = torch.zeros((frame_num, size[1], size[0], 3), dtype=torch.float32, device=self.device)
                        
                        # 确保值范围在0-1之间
                        if videos.max() > 1.0:
                            videos = videos / max(255.0, videos.max().item())
                            logging.info(f"已归一化视频帧值范围，最大值: {videos.max().item():.4f}")
                        
                        logging.info(f"最终视频帧形状: {videos.shape}")
                    else:
                        logging.error("生成的视频帧没有shape属性")
                        videos = torch.zeros((frame_num, size[1], size[0], 3), dtype=torch.float32, device=self.device)"""
        
        updated_content = re.sub(pattern2, replacement2, updated_content)
        
        # 写入修改后的内容
        with open(text2video_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"已修复文件 {text2video_file}")
        return True
    
    except Exception as e:
        logger.error(f"修复 {text2video_file} 时出错: {e}")
        # 尝试恢复备份
        if backup_path and os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, text2video_file)
                logger.info(f"已恢复备份: {backup_path} -> {text2video_file}")
            except:
                logger.error(f"恢复备份失败")
        return False

def check_imports():
    """确保必要的导入"""
    try:
        import torch
        import torchvision
        logger.info(f"PyTorch版本: {torch.__version__}")
        logger.info(f"Torchvision版本: {torchvision.__version__}")
        return True
    except ImportError as e:
        logger.error(f"缺少必要的依赖: {e}")
        return False

def add_shutil_import():
    """在相关文件中添加shutil导入，解决清理临时目录的问题"""
    utils_file = os.path.join("backend", "local_models", "wan", "Wan2.1", "wan", "utils", "utils.py")
    
    if not os.path.exists(utils_file):
        logger.error(f"找不到utils文件: {utils_file}")
        return False
    
    # 备份原始文件
    backup_path = backup_file(utils_file)
    
    try:
        # 读取文件内容
        with open(utils_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经导入shutil
        if "import shutil" in content:
            logger.info(f"文件 {utils_file} 已经导入shutil，跳过...")
            return True
        
        # 添加shutil导入
        pattern = r"(import os)"
        replacement = r"import os\nimport shutil"
        
        updated_content = re.sub(pattern, replacement, content)
        
        # 写入修改后的内容
        with open(utils_file, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"已在 {utils_file} 中添加shutil导入")
        return True
    
    except Exception as e:
        logger.error(f"添加shutil导入时出错: {e}")
        # 尝试恢复备份
        if backup_path and os.path.exists(backup_path):
            try:
                shutil.copy2(backup_path, utils_file)
                logger.info(f"已恢复备份: {backup_path} -> {utils_file}")
            except:
                logger.error(f"恢复备份失败")
        return False

def main():
    """主函数，执行所有修复"""
    logger.info("开始修复WAN2.1模型的视频生成问题...")
    
    # 检查依赖
    if not check_imports():
        logger.error("缺少必要的依赖，修复终止")
        return
    
    # 执行修复
    success_count = 0
    fail_count = 0
    
    # 添加shutil导入
    if add_shutil_import():
        success_count += 1
    else:
        fail_count += 1
    
    # 修复utils.py中的数据类型转换问题
    if fix_utils_py():
        success_count += 1
    else:
        fail_count += 1
    
    # 修复generate.py中的预处理
    if fix_generate_py():
        success_count += 1
    else:
        fail_count += 1
    
    # 修复text2video.py中的维度不匹配问题
    if fix_text2video_py():
        success_count += 1
    else:
        fail_count += 1
    
    # 输出修复结果
    logger.info(f"修复完成: {success_count}个成功, {fail_count}个失败")
    
    if fail_count == 0:
        logger.info("所有文件已成功修复，请重新运行生成代码")
    else:
        logger.warning("部分文件修复失败，请检查日志并手动修复")

if __name__ == "__main__":
    main()