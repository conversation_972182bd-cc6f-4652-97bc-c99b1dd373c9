#!/usr/bin/env python3
"""
统一视频生成管理器
智能选择最佳的视频生成方案
"""

import os
import sys
import time
import logging
from pathlib import Path
from datetime import datetime
from typing import Optional, Tuple, Dict, Any

logger = logging.getLogger(__name__)

class VideoGenerationManager:
    """统一视频生成管理器"""
    
    def __init__(self):
        self.available_engines = {}
        self.preferred_order = ["wanx", "animatediff", "svd", "fallback"]
        self.engine_status = {}
        
        # 检测可用引擎
        self._detect_available_engines()
    
    def _detect_available_engines(self):
        """检测可用的视频生成引擎"""
        logger.info("检测可用的视频生成引擎...")
        
        # 1. 检测Wanx 2.1
        try:
            from .video_generation_aggressive import generate_wanx_video_aggressive
            wanx_dir = Path(__file__).parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
            if wanx_dir.exists() and (wanx_dir / "generate.py").exists():
                self.available_engines["wanx"] = {
                    "name": "Wanx 2.1",
                    "function": generate_wanx_video_aggressive,
                    "status": "unknown",
                    "last_test": None,
                    "success_rate": 0.0
                }
                logger.info("✓ Wanx 2.1 可用")
            else:
                logger.info("✗ Wanx 2.1 不可用（模型文件缺失）")
        except Exception as e:
            logger.info(f"✗ Wanx 2.1 不可用: {e}")
        
        # 2. 检测AnimateDiff
        try:
            from .video_generation_animatediff import generate_animatediff_video
            self.available_engines["animatediff"] = {
                "name": "AnimateDiff",
                "function": generate_animatediff_video,
                "status": "unknown",
                "last_test": None,
                "success_rate": 0.0
            }
            logger.info("✓ AnimateDiff 可用")
        except Exception as e:
            logger.info(f"✗ AnimateDiff 不可用: {e}")
        
        # 3. 检测SVD
        try:
            from .video_generation_animatediff import SVDGenerator
            self.available_engines["svd"] = {
                "name": "Stable Video Diffusion",
                "function": None,  # 需要特殊处理
                "status": "unknown",
                "last_test": None,
                "success_rate": 0.0
            }
            logger.info("✓ SVD 可用")
        except Exception as e:
            logger.info(f"✗ SVD 不可用: {e}")
        
        # 4. 回退方案总是可用
        self.available_engines["fallback"] = {
            "name": "高质量回退方案",
            "function": self._generate_fallback_video,
            "status": "available",
            "last_test": datetime.now(),
            "success_rate": 1.0
        }
        logger.info("✓ 回退方案 可用")
        
        logger.info(f"检测到 {len(self.available_engines)} 个可用引擎")
    
    def _test_engine(self, engine_name: str, timeout: int = 60) -> bool:
        """测试引擎是否正常工作"""
        if engine_name not in self.available_engines:
            return False
        
        engine = self.available_engines[engine_name]
        
        try:
            logger.info(f"测试引擎: {engine['name']}")
            
            # 创建测试输出路径
            test_output = Path(f"test_{engine_name}_{int(time.time())}.mp4")
            
            start_time = time.time()
            
            if engine_name == "wanx":
                success, message, result_path = engine["function"](
                    prompt="test",
                    output_path=test_output,
                    timeout=timeout
                )
            elif engine_name == "animatediff":
                success, message, result_path = engine["function"](
                    prompt="test",
                    output_path=test_output,
                    width=512,
                    height=512,
                    duration=1,
                    num_inference_steps=10
                )
            elif engine_name == "fallback":
                success, message, result_path = engine["function"](
                    prompt="test",
                    output_path=test_output
                )
            else:
                success = False
                message = "未实现的引擎测试"
            
            elapsed_time = time.time() - start_time
            
            # 清理测试文件
            if test_output.exists():
                test_output.unlink()
            
            # 更新引擎状态
            engine["last_test"] = datetime.now()
            if success:
                engine["status"] = "available"
                engine["success_rate"] = min(1.0, engine["success_rate"] + 0.1)
                logger.info(f"✓ {engine['name']} 测试成功 (耗时: {elapsed_time:.1f}秒)")
                return True
            else:
                engine["status"] = "failed"
                engine["success_rate"] = max(0.0, engine["success_rate"] - 0.2)
                logger.warning(f"✗ {engine['name']} 测试失败: {message}")
                return False
                
        except Exception as e:
            engine["status"] = "error"
            engine["success_rate"] = max(0.0, engine["success_rate"] - 0.3)
            logger.error(f"✗ {engine['name']} 测试异常: {e}")
            return False
    
    def get_best_engine(self) -> Optional[str]:
        """获取最佳可用引擎"""
        best_engine = None
        best_score = -1
        
        for engine_name in self.preferred_order:
            if engine_name not in self.available_engines:
                continue
            
            engine = self.available_engines[engine_name]
            
            # 计算引擎得分
            score = 0
            
            # 基础可用性
            if engine["status"] == "available":
                score += 100
            elif engine["status"] == "unknown":
                score += 50  # 未测试的引擎有机会
            else:
                continue  # 失败的引擎跳过
            
            # 成功率加分
            score += engine["success_rate"] * 50
            
            # 优先级加分（越靠前越高）
            priority_bonus = (len(self.preferred_order) - self.preferred_order.index(engine_name)) * 10
            score += priority_bonus
            
            # 最近测试时间加分
            if engine["last_test"]:
                time_diff = (datetime.now() - engine["last_test"]).total_seconds()
                if time_diff < 3600:  # 1小时内测试过
                    score += 20
            
            logger.debug(f"引擎 {engine_name} 得分: {score}")
            
            if score > best_score:
                best_score = score
                best_engine = engine_name
        
        if best_engine:
            logger.info(f"选择最佳引擎: {self.available_engines[best_engine]['name']}")
        else:
            logger.warning("没有可用的引擎")
        
        return best_engine
    
    def generate_video(
        self,
        prompt: str,
        output_path: Path,
        **kwargs
    ) -> Tuple[bool, str, Optional[Path]]:
        """智能视频生成"""
        logger.info(f"开始智能视频生成: {prompt}")
        
        # 获取最佳引擎
        best_engine = self.get_best_engine()
        
        if not best_engine:
            return False, "没有可用的视频生成引擎", None
        
        # 尝试使用最佳引擎
        engine = self.available_engines[best_engine]
        logger.info(f"使用引擎: {engine['name']}")
        
        try:
            if best_engine == "wanx":
                success, message, result_path = engine["function"](
                    prompt=prompt,
                    output_path=output_path,
                    timeout=kwargs.get("timeout", 90),
                    **kwargs
                )
            elif best_engine == "animatediff":
                success, message, result_path = engine["function"](
                    prompt=prompt,
                    output_path=output_path,
                    **kwargs
                )
            elif best_engine == "fallback":
                success, message, result_path = engine["function"](
                    prompt=prompt,
                    output_path=output_path,
                    **kwargs
                )
            else:
                success = False
                message = f"未实现的引擎: {best_engine}"
                result_path = None
            
            # 更新引擎状态
            if success:
                engine["success_rate"] = min(1.0, engine["success_rate"] + 0.05)
                engine["status"] = "available"
            else:
                engine["success_rate"] = max(0.0, engine["success_rate"] - 0.1)
                if engine["success_rate"] < 0.3:
                    engine["status"] = "unreliable"
            
            # 如果失败，尝试下一个引擎
            if not success and len(self.available_engines) > 1:
                logger.warning(f"引擎 {engine['name']} 失败，尝试备用引擎...")
                
                # 标记当前引擎为失败
                engine["status"] = "failed"
                
                # 递归尝试下一个引擎
                return self.generate_video(prompt, output_path, **kwargs)
            
            return success, message, result_path
            
        except Exception as e:
            logger.error(f"引擎 {engine['name']} 异常: {e}")
            engine["status"] = "error"
            
            # 尝试备用引擎
            if len(self.available_engines) > 1:
                return self.generate_video(prompt, output_path, **kwargs)
            else:
                return False, f"所有引擎都失败: {e}", None
    
    def _generate_fallback_video(self, prompt: str, output_path: Path, **kwargs) -> Tuple[bool, str, Optional[Path]]:
        """回退视频生成方案"""
        try:
            from .video_generation import create_quality_video
            
            width = kwargs.get("width", 832)
            height = kwargs.get("height", 480)
            duration = kwargs.get("duration", 5)
            fps = kwargs.get("fps", 8)
            
            success = create_quality_video(
                output_path=output_path,
                prompt=prompt,
                width=width,
                height=height,
                duration=duration,
                fps=fps
            )
            
            if success:
                return True, "回退方案生成成功", output_path
            else:
                return False, "回退方案生成失败", None
                
        except Exception as e:
            logger.error(f"回退方案异常: {e}")
            return False, f"回退方案异常: {e}", None
    
    def get_engine_status(self) -> Dict[str, Any]:
        """获取所有引擎状态"""
        return {
            name: {
                "name": engine["name"],
                "status": engine["status"],
                "success_rate": engine["success_rate"],
                "last_test": engine["last_test"].isoformat() if engine["last_test"] else None
            }
            for name, engine in self.available_engines.items()
        }

# 全局管理器实例
video_manager = VideoGenerationManager()

def generate_video_smart(prompt: str, output_path: Path, **kwargs) -> Tuple[bool, str, Optional[Path]]:
    """智能视频生成接口"""
    return video_manager.generate_video(prompt, output_path, **kwargs)

if __name__ == "__main__":
    # 测试智能视频生成
    manager = VideoGenerationManager()
    
    print("=== 引擎状态 ===")
    for name, status in manager.get_engine_status().items():
        print(f"{name}: {status}")
    
    print("\n=== 测试智能生成 ===")
    output_path = Path("test_smart.mp4")
    success, message, result_path = manager.generate_video(
        prompt="a cat running",
        output_path=output_path,
        width=512,
        height=512,
        duration=3
    )
    
    if success:
        print(f"✅ 智能生成成功: {result_path}")
    else:
        print(f"❌ 智能生成失败: {message}")
