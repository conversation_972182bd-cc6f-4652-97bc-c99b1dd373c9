"""
GPU诊断和修复工具
专门用于诊断Wan2.1模型GPU使用问题
"""
import os
import sys
import logging
import subprocess
import time
import psutil
from typing import Dict, Any, List, Tuple
from pathlib import Path

logger = logging.getLogger(__name__)

class GPUDiagnostic:
    """GPU诊断工具"""
    
    def __init__(self):
        self.diagnostic_results = {}
        self.issues_found = []
        self.recommendations = []
    
    def run_full_diagnostic(self) -> Dict[str, Any]:
        """运行完整的GPU诊断"""
        logger.info("🔍 开始GPU诊断...")
        
        self.diagnostic_results = {
            "timestamp": time.time(),
            "system_info": self._check_system_info(),
            "cuda_status": self._check_cuda_installation(),
            "pytorch_status": self._check_pytorch_gpu(),
            "gpu_hardware": self._check_gpu_hardware(),
            "memory_status": self._check_memory_status(),
            "driver_status": self._check_driver_status(),
            "wan_model_status": self._check_wan_model_files(),
            "issues": self.issues_found,
            "recommendations": self.recommendations
        }
        
        # 生成诊断报告
        self._generate_recommendations()
        
        logger.info(f"🔍 诊断完成，发现 {len(self.issues_found)} 个问题")
        return self.diagnostic_results
    
    def _check_system_info(self) -> Dict[str, Any]:
        """检查系统信息"""
        try:
            system_info = {
                "os": os.name,
                "platform": sys.platform,
                "python_version": sys.version,
                "cpu_count": psutil.cpu_count(),
                "memory_total_gb": psutil.virtual_memory().total / 1024**3,
                "memory_available_gb": psutil.virtual_memory().available / 1024**3
            }
            
            # 检查内存是否足够
            if system_info["memory_available_gb"] < 8:
                self.issues_found.append({
                    "type": "memory",
                    "severity": "warning",
                    "message": f"系统可用内存不足8GB ({system_info['memory_available_gb']:.1f}GB)，可能影响模型加载"
                })
            
            return system_info
        except Exception as e:
            self.issues_found.append({
                "type": "system",
                "severity": "error",
                "message": f"无法获取系统信息: {e}"
            })
            return {"error": str(e)}
    
    def _check_cuda_installation(self) -> Dict[str, Any]:
        """检查CUDA安装状态"""
        try:
            # 检查nvidia-smi
            result = subprocess.run(["nvidia-smi"], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                self.issues_found.append({
                    "type": "cuda",
                    "severity": "critical",
                    "message": "nvidia-smi命令失败，NVIDIA驱动可能未正确安装"
                })
                return {"available": False, "error": "nvidia-smi failed"}
            
            # 解析nvidia-smi输出
            output = result.stdout
            cuda_version = None
            driver_version = None
            
            for line in output.split('\n'):
                if 'CUDA Version:' in line:
                    cuda_version = line.split('CUDA Version:')[1].strip().split()[0]
                if 'Driver Version:' in line:
                    driver_version = line.split('Driver Version:')[1].strip().split()[0]
            
            cuda_info = {
                "available": True,
                "driver_version": driver_version,
                "cuda_version": cuda_version,
                "nvidia_smi_output": output
            }
            
            # 检查CUDA版本兼容性
            if cuda_version:
                major_version = int(cuda_version.split('.')[0])
                if major_version < 11:
                    self.issues_found.append({
                        "type": "cuda",
                        "severity": "warning",
                        "message": f"CUDA版本 {cuda_version} 较旧，建议升级到11.0+以获得更好的性能"
                    })
            
            return cuda_info
            
        except subprocess.TimeoutExpired:
            self.issues_found.append({
                "type": "cuda",
                "severity": "critical",
                "message": "nvidia-smi命令超时，GPU可能卡死或驱动异常"
            })
            return {"available": False, "error": "nvidia-smi timeout"}
        except FileNotFoundError:
            self.issues_found.append({
                "type": "cuda",
                "severity": "critical",
                "message": "未找到nvidia-smi命令，NVIDIA驱动未安装"
            })
            return {"available": False, "error": "nvidia-smi not found"}
        except Exception as e:
            self.issues_found.append({
                "type": "cuda",
                "severity": "error",
                "message": f"CUDA检查失败: {e}"
            })
            return {"available": False, "error": str(e)}
    
    def _check_pytorch_gpu(self) -> Dict[str, Any]:
        """检查PyTorch GPU支持"""
        try:
            import torch
            
            pytorch_info = {
                "version": torch.__version__,
                "cuda_available": torch.cuda.is_available(),
                "cuda_version": torch.version.cuda if hasattr(torch.version, 'cuda') else None,
                "cudnn_version": torch.backends.cudnn.version() if torch.backends.cudnn.is_available() else None,
                "device_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
            }
            
            if not pytorch_info["cuda_available"]:
                self.issues_found.append({
                    "type": "pytorch",
                    "severity": "critical",
                    "message": "PyTorch无法访问CUDA，可能是CPU版本或驱动问题"
                })
            else:
                # 测试GPU基本功能
                try:
                    device = torch.device("cuda:0")
                    test_tensor = torch.randn(100, 100).to(device)
                    result = torch.mm(test_tensor, test_tensor)
                    pytorch_info["gpu_test"] = "passed"
                    logger.info("✅ GPU基本功能测试通过")
                except Exception as e:
                    self.issues_found.append({
                        "type": "pytorch",
                        "severity": "critical",
                        "message": f"GPU基本功能测试失败: {e}"
                    })
                    pytorch_info["gpu_test"] = f"failed: {e}"
            
            return pytorch_info
            
        except ImportError:
            self.issues_found.append({
                "type": "pytorch",
                "severity": "critical",
                "message": "PyTorch未安装，无法使用GPU加速"
            })
            return {"available": False, "error": "PyTorch not installed"}
        except Exception as e:
            self.issues_found.append({
                "type": "pytorch",
                "severity": "error",
                "message": f"PyTorch检查失败: {e}"
            })
            return {"available": False, "error": str(e)}
    
    def _check_gpu_hardware(self) -> Dict[str, Any]:
        """检查GPU硬件信息"""
        try:
            import torch
            
            if not torch.cuda.is_available():
                return {"available": False}
            
            gpu_info = {}
            for i in range(torch.cuda.device_count()):
                props = torch.cuda.get_device_properties(i)
                gpu_info[f"gpu_{i}"] = {
                    "name": props.name,
                    "memory_total_gb": props.total_memory / 1024**3,
                    "compute_capability": f"{props.major}.{props.minor}",
                    "multiprocessor_count": props.multi_processor_count
                }
                
                # 检查GPU是否适合深度学习
                if props.total_memory < 4 * 1024**3:  # 4GB
                    self.issues_found.append({
                        "type": "hardware",
                        "severity": "warning",
                        "message": f"GPU {i} ({props.name}) 内存不足4GB，可能无法运行大型模型"
                    })
                
                # 检查计算能力
                compute_capability = float(f"{props.major}.{props.minor}")
                if compute_capability < 6.0:
                    self.issues_found.append({
                        "type": "hardware",
                        "severity": "warning",
                        "message": f"GPU {i} 计算能力 {compute_capability} 较低，建议使用6.0+的GPU"
                    })
            
            return gpu_info
            
        except Exception as e:
            self.issues_found.append({
                "type": "hardware",
                "severity": "error",
                "message": f"GPU硬件检查失败: {e}"
            })
            return {"available": False, "error": str(e)}
    
    def _check_memory_status(self) -> Dict[str, Any]:
        """检查GPU内存状态"""
        try:
            import torch
            
            if not torch.cuda.is_available():
                return {"available": False}
            
            # 清理缓存
            torch.cuda.empty_cache()
            
            memory_info = {}
            for i in range(torch.cuda.device_count()):
                torch.cuda.set_device(i)
                
                memory_allocated = torch.cuda.memory_allocated(i)
                memory_cached = torch.cuda.memory_reserved(i)
                memory_total = torch.cuda.get_device_properties(i).total_memory
                memory_free = memory_total - memory_allocated
                
                memory_info[f"gpu_{i}"] = {
                    "allocated_gb": memory_allocated / 1024**3,
                    "cached_gb": memory_cached / 1024**3,
                    "free_gb": memory_free / 1024**3,
                    "total_gb": memory_total / 1024**3,
                    "usage_percent": (memory_allocated / memory_total) * 100
                }
                
                # 检查内存使用情况
                if memory_info[f"gpu_{i}"]["usage_percent"] > 95:
                    self.issues_found.append({
                        "type": "memory",
                        "severity": "critical",
                        "message": f"GPU {i} 内存使用率超过95%，可能导致OOM错误"
                    })
                elif memory_info[f"gpu_{i}"]["usage_percent"] > 80:
                    self.issues_found.append({
                        "type": "memory",
                        "severity": "warning",
                        "message": f"GPU {i} 内存使用率超过80%，建议清理或优化"
                    })
            
            return memory_info
            
        except Exception as e:
            self.issues_found.append({
                "type": "memory",
                "severity": "error",
                "message": f"GPU内存检查失败: {e}"
            })
            return {"available": False, "error": str(e)}
    
    def _check_driver_status(self) -> Dict[str, Any]:
        """检查驱动状态"""
        try:
            # 检查驱动版本
            result = subprocess.run(
                ["nvidia-smi", "--query-gpu=driver_version", "--format=csv,noheader"],
                capture_output=True, text=True, timeout=5
            )
            
            if result.returncode == 0:
                driver_version = result.stdout.strip()
                
                # 检查驱动版本是否足够新
                try:
                    version_parts = driver_version.split('.')
                    major_version = int(version_parts[0])
                    
                    if major_version < 470:
                        self.issues_found.append({
                            "type": "driver",
                            "severity": "warning",
                            "message": f"NVIDIA驱动版本 {driver_version} 较旧，建议升级到470+以获得更好的CUDA支持"
                        })
                except:
                    pass
                
                return {
                    "version": driver_version,
                    "status": "ok"
                }
            else:
                self.issues_found.append({
                    "type": "driver",
                    "severity": "error",
                    "message": "无法获取NVIDIA驱动版本"
                })
                return {"status": "error"}
                
        except Exception as e:
            self.issues_found.append({
                "type": "driver",
                "severity": "error",
                "message": f"驱动检查失败: {e}"
            })
            return {"status": "error", "error": str(e)}
    
    def _check_wan_model_files(self) -> Dict[str, Any]:
        """检查Wan模型文件"""
        try:
            wan_model_dir = Path(__file__).parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
            
            model_status = {
                "model_dir_exists": wan_model_dir.exists(),
                "model_files": [],
                "missing_files": []
            }
            
            if not wan_model_dir.exists():
                self.issues_found.append({
                    "type": "model",
                    "severity": "critical",
                    "message": f"Wan2.1模型目录不存在: {wan_model_dir}"
                })
                return model_status
            
            # 检查关键文件
            required_files = [
                "generate.py",
                "wan/__init__.py",
                "wan/configs/__init__.py"
            ]
            
            for file_path in required_files:
                full_path = wan_model_dir / file_path
                if full_path.exists():
                    model_status["model_files"].append(str(file_path))
                else:
                    model_status["missing_files"].append(str(file_path))
                    self.issues_found.append({
                        "type": "model",
                        "severity": "error",
                        "message": f"缺少关键文件: {file_path}"
                    })
            
            return model_status
            
        except Exception as e:
            self.issues_found.append({
                "type": "model",
                "severity": "error",
                "message": f"模型文件检查失败: {e}"
            })
            return {"error": str(e)}
    
    def _generate_recommendations(self):
        """生成修复建议"""
        # 根据发现的问题生成建议
        critical_issues = [issue for issue in self.issues_found if issue["severity"] == "critical"]
        warning_issues = [issue for issue in self.issues_found if issue["severity"] == "warning"]
        
        if critical_issues:
            self.recommendations.append({
                "priority": "high",
                "title": "修复关键问题",
                "actions": [
                    "检查NVIDIA驱动是否正确安装",
                    "确认PyTorch GPU版本已安装",
                    "验证CUDA环境配置",
                    "重启系统以刷新驱动状态"
                ]
            })
        
        if warning_issues:
            self.recommendations.append({
                "priority": "medium",
                "title": "优化建议",
                "actions": [
                    "升级NVIDIA驱动到最新版本",
                    "清理GPU内存缓存",
                    "调整模型参数以适应硬件配置",
                    "启用内存优化选项"
                ]
            })
        
        # 通用建议
        self.recommendations.append({
            "priority": "low",
            "title": "性能优化",
            "actions": [
                "使用FP16精度以节省内存",
                "启用模型卸载以减少GPU内存使用",
                "调整批处理大小以平衡速度和内存",
                "定期清理GPU缓存"
            ]
        })
    
    def get_quick_fix_commands(self) -> List[str]:
        """获取快速修复命令"""
        commands = []
        
        # 基于发现的问题生成修复命令
        for issue in self.issues_found:
            if issue["type"] == "memory" and "内存使用率" in issue["message"]:
                commands.append("python -c \"import torch; torch.cuda.empty_cache()\"")
            elif issue["type"] == "pytorch" and "CPU版本" in issue["message"]:
                commands.append("pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118")
        
        return commands

# 全局诊断工具实例
gpu_diagnostic = GPUDiagnostic()
