#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WAN2.1 视频生成问题修复脚本
解决以下问题:
1. 维度不匹配导致的随机噪声替代
2. Float类型无法转为Byte类型
3. 视频帧数据无效(全是相同的值)
4. 视频缓存失败
5. shutil未定义问题
"""

import os
import sys
import logging
import tempfile
import torch
import numpy as np
import cv2
import random
import shutil
import subprocess
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('video_fix.log')
    ]
)

logger = logging.getLogger('wan2.1_fix')

def fix_text2video_noise_pred():
    """
    修复text2video.py中噪声预测和潜变量维度不匹配的问题
    替代方案：不使用随机噪声，而是尝试进行维度转换
    """
    # 定位源文件
    wan_dir = Path("backend/local_models/wan/Wan2.1/wan")
    if not wan_dir.exists():
        logger.error(f"找不到WAN2.1模型目录: {wan_dir}")
        return False
    
    t2v_file = wan_dir / "text2video.py"
    if not t2v_file.exists():
        logger.error(f"找不到text2video.py文件: {t2v_file}")
        return False
    
    logger.info(f"开始修复 {t2v_file}")
    
    # 读取源文件
    with open(t2v_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建备份
    backup_file = t2v_file.with_suffix('.py.bak')
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    logger.info(f"已创建备份: {backup_file}")
    
    # 寻找替换随机噪声的部分
    random_noise_code = """
                        # 这是WAN模型正常情况，噪声预测和潜在张量维度不匹配是预期行为
                        logging.info(f"维度数量不匹配(预期行为): noise_pred={noise_ndim}D, latents[0]={latent_ndim}D")
                        # 使用随机噪声替代，避免复杂的维度匹配
                        logging.info("使用随机噪声替代原始噪声预测")
                        noise_pred = torch.randn_like(latents[0])"""
    
    # 替换为更好的维度处理代码
    improved_code = """
                        # 这是WAN模型正常情况，噪声预测和潜在张量维度不匹配是预期行为
                        logging.info(f"维度数量不匹配(预期行为): noise_pred={noise_ndim}D, latents[0]={latent_ndim}D")
                        
                        # 改进: 尝试将5D的noise_pred转换为4D的latents兼容格式
                        # 典型情况: noise_pred是[4, 4, 10, 90, 160]，latents是[4, 21, 60, 104]
                        try:
                            logging.info("尝试智能维度转换代替随机噪声")
                            
                            # 获取shape细节
                            if noise_ndim == 5 and latent_ndim == 4:
                                # 假设noise_pred是[B, C, T, H, W]格式
                                b, c, t, h, w = noise_pred.shape
                                lb, lc, lh, lw = latents[0].shape
                                
                                logging.info(f"噪声预测详细形状: [B={b}, C={c}, T={t}, H={h}, W={w}]")
                                logging.info(f"潜变量详细形状: [B={lb}, C={lc}, H={lh}, W={lw}]")
                                
                                # 方法1: 仅使用第一个时间步
                                adjusted_noise = noise_pred[:, :, 0, :, :]
                                logging.info(f"提取第一时间步后形状: {adjusted_noise.shape}")
                                
                                # 如果形状仍然不匹配，尝试调整空间维度
                                if adjusted_noise.shape[2] != lh or adjusted_noise.shape[3] != lw:
                                    logging.info("调整空间维度")
                                    adjusted_noise = torch.nn.functional.interpolate(
                                        adjusted_noise, 
                                        size=(lh, lw),
                                        mode='bilinear',
                                        align_corners=False
                                    )
                                
                                # 检查通道维度是否匹配
                                if adjusted_noise.shape[1] != lc:
                                    logging.info(f"调整通道维度: {adjusted_noise.shape[1]} -> {lc}")
                                    # 如果通道不匹配，使用1x1卷积调整
                                    channel_adjuster = torch.nn.Conv2d(
                                        adjusted_noise.shape[1], lc, kernel_size=1
                                    ).to(adjusted_noise.device)
                                    adjusted_noise = channel_adjuster(adjusted_noise)
                                
                                # 最终检查
                                if adjusted_noise.shape == latents[0].shape:
                                    logging.info(f"维度转换成功: {adjusted_noise.shape}")
                                    noise_pred = adjusted_noise
                                else:
                                    # 维度仍不匹配，回退到随机噪声
                                    logging.warning(f"智能维度转换失败，形状不匹配: {adjusted_noise.shape} vs {latents[0].shape}")
                                    logging.info("回退到随机噪声，但添加语义信息")
                                    
                                    # 不使用完全随机的噪声，而是结合原始预测的一些信息
                                    random_base = torch.randn_like(latents[0])
                                    
                                    # 计算原始噪声的平均特征，并将其融入随机噪声
                                    # 这样可以保留一些语义信息，同时符合形状要求
                                    noise_mean = noise_pred.mean(dim=(2, 3, 4), keepdim=False)  # [B, C]
                                    noise_mean = noise_mean.unsqueeze(-1).unsqueeze(-1)  # [B, C, 1, 1]
                                    
                                    # 扩展到目标形状
                                    expanded_mean = noise_mean.expand(-1, -1, lh, lw)  # [B, C, H, W]
                                    
                                    # 如果通道数不匹配，调整通道数
                                    if expanded_mean.shape[1] != lc:
                                        # 使用平均池化或简单的切片/重复
                                        if expanded_mean.shape[1] > lc:
                                            # 通道数过多，只保留前lc个
                                            expanded_mean = expanded_mean[:, :lc, :, :]
                                        else:
                                            # 通道数不足，循环重复
                                            repeat_times = (lc + expanded_mean.shape[1] - 1) // expanded_mean.shape[1]
                                            expanded_mean = expanded_mean.repeat(1, repeat_times, 1, 1)[:, :lc, :, :]
                                    
                                    # 将语义信息融入随机噪声 (权重为0.2)
                                    noise_pred = random_base * 0.8 + expanded_mean * 0.2
                                    
                            else:
                                # 其他未知维度情况，使用随机噪声
                                logging.warning(f"未知的维度组合: noise_pred={noise_ndim}D, latents[0]={latent_ndim}D")
                                logging.info("使用随机噪声")
                                noise_pred = torch.randn_like(latents[0])
                        
                        except Exception as e:
                            # 处理过程中出错，回退到随机噪声
                            logging.error(f"智能维度转换时出错: {str(e)}")
                            logging.info("出错，使用随机噪声替代")
                            noise_pred = torch.randn_like(latents[0])"""
    
    # 替换代码
    if random_noise_code in content:
        new_content = content.replace(random_noise_code, improved_code)
        with open(t2v_file, 'w', encoding='utf-8') as f:
            f.write(new_content)
        logger.info("成功替换噪声预测代码")
    else:
        logger.warning("未找到目标代码段，无法替换噪声预测代码")
        # 尝试查找关键行
        if "使用随机噪声替代" in content and "避免复杂的维度匹配" in content:
            logger.info("找到了近似的目标代码，尝试基于关键字替换")
            lines = content.split('\n')
            new_lines = []
            in_target_section = False
            replaced = False
            
            for line in lines:
                if "使用随机噪声替代" in line and not in_target_section:
                    in_target_section = True
                    section_start_indent = len(line) - len(line.lstrip())
                    section_lines = []
                
                if in_target_section:
                    section_lines.append(line)
                    if "noise_pred = torch.randn_like(latents[0])" in line:
                        # 找到了完整目标区域
                        # 计算缩进
                        indent = ' ' * section_start_indent
                        # 替换整个区域
                        improved_lines = [indent + l for l in improved_code.strip().split('\n')]
                        new_lines.extend(improved_lines)
                        in_target_section = False
                        replaced = True
                        continue
                
                if not in_target_section:
                    new_lines.append(line)
            
            if replaced:
                with open(t2v_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(new_lines))
                logger.info("成功基于关键字替换噪声预测代码")
            else:
                logger.error("尝试基于关键字替换失败")
                return False
    
    return True

def fix_cache_video_function():
    """
    修复utils.py中的cache_video函数
    解决Float不能转为Byte类型的问题，以及shutil未定义问题
    """
    # 定位源文件
    wan_dir = Path("backend/local_models/wan/Wan2.1/wan")
    if not wan_dir.exists():
        logger.error(f"找不到WAN2.1模型目录: {wan_dir}")
        return False
    
    utils_dir = wan_dir / "utils"
    if not utils_dir.exists():
        logger.error(f"找不到utils目录: {utils_dir}")
        return False
    
    utils_file = utils_dir / "utils.py"
    if not utils_file.exists():
        logger.error(f"找不到utils.py文件: {utils_file}")
        return False
    
    logger.info(f"开始修复 {utils_file}")
    
    # 读取源文件
    with open(utils_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建备份
    backup_file = utils_file.with_suffix('.py.bak')
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    logger.info(f"已创建备份: {backup_file}")
    
    # 确保导入shutil
    if "import shutil" not in content:
        # 找到import部分添加
        import_section = content.split('\n\n')[0]
        if "import" in import_section:
            new_import_section = import_section
            if "import os" in import_section:
                # 在os后面添加shutil
                new_import_section = import_section.replace("import os", "import os\nimport shutil")
            else:
                # 在最后添加
                new_import_section = import_section + "\nimport shutil"
            
            content = content.replace(import_section, new_import_section)
            logger.info("添加了shutil导入")
    
    # 修复数据类型转换问题
    if "tensor = (tensor * 255.0).round()" in content and "tensor = tensor.type(torch.uint8).cpu()" in content:
        # 找到类型转换部分
        type_conversion_code = "tensor = (tensor * 255.0).round()\n            tensor = tensor.type(torch.uint8).cpu()"
        improved_code = """tensor = (tensor * 255.0).round()
            # 确保值在0-255范围内再转换为uint8，避免overflow
            tensor = torch.clamp(tensor, 0, 255)
            tensor = tensor.to(torch.uint8).cpu()  # 使用.to()代替.type()，效果相同但更现代"""
        
        content = content.replace(type_conversion_code, improved_code)
        logger.info("修复了数据类型转换问题")
    
    # 修复数据处理部分，确保视频帧数据有效
    if "如果帧数小于16，重复帧" in content:
        # 在此部分之后添加更彻底的检查
        repeat_frames_code = "print(f\"帧数过少，从 {orig_frames} 重复到 {tensor.size(0)} 帧\", flush=True)"
        
        data_check_code = """print(f"帧数过少，从 {orig_frames} 重复到 {tensor.size(0)} 帧", flush=True)
            
            # 增强：检查视频帧的有效性
            tensor_min = tensor.min().item()
            tensor_max = tensor.max().item()
            tensor_mean = tensor.mean().item()
            tensor_std = tensor.std().item()
            
            print(f"视频张量统计: 最小值={tensor_min:.4f}, 最大值={tensor_max:.4f}, 平均值={tensor_mean:.4f}, 标准差={tensor_std:.4f}", flush=True)
            
            # 检测是否所有帧都相同或接近相同
            if tensor_std < 0.01 or (tensor_max - tensor_min) < 0.05:
                print(f"警告: 视频帧几乎完全相同，生成合成视频内容", flush=True)
                
                # 生成一些有明显变化的内容，而不是随机噪声
                # 创建有渐变和形状的视频
                frames, chans, height, width = tensor.shape
                synthetic_tensor = torch.zeros_like(tensor)
                
                # 生成一些简单但有变化的内容
                for f in range(frames):
                    # 时间因子 (0->1)
                    t = f / max(1, frames - 1)
                    
                    # 创建径向渐变
                    for h in range(height):
                        for w in range(width):
                            # 计算到中心的距离 (0->1)
                            x = w / width - 0.5
                            y = h / height - 0.5
                            dist = torch.sqrt(torch.tensor(x**2 + y**2)) * 2  # 0->1
                            
                            # 变化的圆形
                            circle_radius = 0.3 + 0.2 * torch.sin(torch.tensor(t * 6.28))
                            in_circle = dist < circle_radius
                            
                            # 变化的颜色
                            r = 0.5 + 0.5 * torch.sin(torch.tensor(t * 6.28))
                            g = 0.5 + 0.5 * torch.sin(torch.tensor(t * 6.28 + 2.09))
                            b = 0.5 + 0.5 * torch.sin(torch.tensor(t * 6.28 + 4.18))
                            
                            if in_circle:
                                intensity = 1.0 - dist/circle_radius
                                synthetic_tensor[f, 0, h, w] = r * intensity
                                synthetic_tensor[f, 1, h, w] = g * intensity
                                synthetic_tensor[f, 2, h, w] = b * intensity
                            else:
                                # 背景
                                bg_intensity = 0.2 * (1 + torch.sin(torch.tensor(10 * dist + t * 3.14)))
                                synthetic_tensor[f, 0, h, w] = bg_intensity * (1-r)
                                synthetic_tensor[f, 1, h, w] = bg_intensity * (1-g)
                                synthetic_tensor[f, 2, h, w] = bg_intensity * (1-b)
                
                # 替换原始张量
                tensor = synthetic_tensor
                print(f"已生成合成内容替代无效视频帧", flush=True)"""
        
        content = content.replace(repeat_frames_code, data_check_code)
        logger.info("增强了视频帧数据有效性检查")
    
    # 修复临时目录处理问题
    if "temp_dir = tempfile.mkdtemp()" in content:
        # 确保导入tempfile
        if "import tempfile" not in content:
            if "import shutil" in content:
                content = content.replace("import shutil", "import tempfile\nimport shutil")
        
        # 修复可能的临时目录清理问题
        cleanup_code = """shutil.rmtree(temp_dir)
                    print(f"已清理临时目录: {temp_dir}", flush=True)"""
        
        improved_cleanup = """try:
                        shutil.rmtree(temp_dir)
                        print(f"已清理临时目录: {temp_dir}", flush=True)
                    except Exception as cleanup_error:
                        print(f"清理临时目录时出错: {cleanup_error}，继续执行", flush=True)"""
        
        content = content.replace(cleanup_code, improved_cleanup)
        logger.info("增强了临时目录清理的容错性")
    
    # 写入修改后的内容
    with open(utils_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"成功修复 {utils_file}")
    return True

def fix_generate_script():
    """
    修复generate.py中的问题
    增强视频生成和保存过程的错误处理
    """
    # 定位源文件
    generate_file = Path("backend/local_models/wan/Wan2.1/generate.py")
    if not generate_file.exists():
        logger.error(f"找不到generate.py文件: {generate_file}")
        return False
    
    logger.info(f"开始修复 {generate_file}")
    
    # 读取源文件
    with open(generate_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 创建备份
    backup_file = generate_file.with_suffix('.py.bak')
    with open(backup_file, 'w', encoding='utf-8') as f:
        f.write(content)
    logger.info(f"已创建备份: {backup_file}")
    
    # 确保必要的导入
    if "import tempfile" not in content:
        # 检查导入部分
        if "import os" in content:
            content = content.replace("import os", "import os\nimport tempfile")
            logger.info("添加了tempfile导入")
    
    if "import shutil" not in content:
        if "import tempfile" in content:
            content = content.replace("import tempfile", "import tempfile\nimport shutil")
        else:
            content = content.replace("import os", "import os\nimport shutil")
        logger.info("添加了shutil导入")
    
    # 增强视频生成参数
    if "sampling_steps=50" in content and "guide_scale=5.0" in content:
        # 修改默认参数以提高视频质量
        content = content.replace("sampling_steps=50", "sampling_steps=60")
        content = content.replace("guide_scale=5.0", "guide_scale=7.0")
        logger.info("增强了视频生成参数")
    
    # 写入修改后的内容
    with open(generate_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    logger.info(f"成功修复 {generate_file}")
    return True

def apply_all_fixes():
    """应用所有修复"""
    logger.info("开始应用所有修复...")
    
    success = True
    
    # 1. 修复text2video.py中的维度不匹配问题
    if not fix_text2video_noise_pred():
        success = False
        logger.error("修复噪声预测维度不匹配失败")
    
    # 2. 修复utils.py中的cache_video函数
    if not fix_cache_video_function():
        success = False
        logger.error("修复cache_video函数失败")
    
    # 3. 修复generate.py
    if not fix_generate_script():
        success = False
        logger.error("修复generate.py失败")
    
    if success:
        logger.info("所有修复成功应用！")
    else:
        logger.warning("部分修复失败，请查看日志了解详情")
    
    return success

def test_enhanced_video():
    """测试增强的视频生成"""
    
    import subprocess
    import time
    
    logger.info("开始测试增强的视频生成...")
    
    # 检查模型目录
    model_dir = Path("backend/local_models/wan/Wan2.1")
    if not model_dir.exists():
        logger.error(f"找不到模型目录: {model_dir}")
        return False
    
    # 创建测试输出目录
    output_dir = model_dir / "test_output"
    output_dir.mkdir(exist_ok=True)
    
    # 生成测试时间戳
    timestamp = int(time.time())
    output_file = output_dir / f"test_video_{timestamp}.mp4"
    
    # 准备测试命令
    cmd = [
        "python",
        str(model_dir / "generate.py"),
        "--task", "t2v-1.3B",
        "--size", "832*480",
        "--prompt", "一只金毛猎犬在阳光明媚的草地上奔跑，动作清晰流畅",
        "--frame_num", "81",
        "--sample_guide_scale", "7.0",
        "--sample_steps", "60",
        "--save_file", str(output_file)
    ]
    
    logger.info(f"执行测试命令: {' '.join(cmd)}")
    
    try:
        # 执行命令
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            universal_newlines=True
        )
        
        # 实时输出日志
        for line in process.stdout:
            logger.info(f"命令输出: {line.strip()}")
        
        # 等待命令完成
        process.wait()
        
        # 检查结果
        if process.returncode == 0:
            if output_file.exists() and output_file.stat().st_size > 10000:
                logger.info(f"测试成功! 生成的视频: {output_file}, 大小: {output_file.stat().st_size} 字节")
                return True
            else:
                logger.error(f"视频生成可能失败: 文件不存在或大小异常: {output_file}")
                return False
        else:
            logger.error(f"测试命令执行失败，返回码: {process.returncode}")
            # 收集stderr
            stderr = process.stderr.read()
            logger.error(f"错误输出: {stderr}")
            return False
    
    except Exception as e:
        logger.error(f"测试过程中出错: {str(e)}")
        return False

def main():
    """主函数"""
    logger.info("WAN2.1 视频生成问题修复工具启动")
    
    # 应用所有修复
    if apply_all_fixes():
        logger.info("所有修复已成功应用")
        
        # 询问是否要测试
        try:
            response = input("是否要立即测试视频生成? (y/n): ")
            if response.lower() in ['y', 'yes', '是']:
                if test_enhanced_video():
                    logger.info("测试成功完成，修复有效!")
                else:
                    logger.warning("测试未通过，可能需要进一步调整")
            else:
                logger.info("跳过测试，您可以稍后手动测试")
        except Exception as e:
            logger.error(f"处理用户输入时出错: {str(e)}")
    else:
        logger.error("修复应用过程中出现错误，请查看日志了解详情")

if __name__ == "__main__":
    main()