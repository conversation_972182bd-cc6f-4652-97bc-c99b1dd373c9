"""
模型训练 API 端点
"""

from fastapi import APIRouter, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging

from ...services.model_training_service import ModelTrainingService

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/model-training", tags=["模型训练"])

# 请求模型
class CreateProfessionalAgentRequest(BaseModel):
    profession: str = Field(..., description="专业类型：teacher, doctor, lawyer")
    custom_name: Optional[str] = Field(None, description="自定义名称")
    specialization: Optional[str] = Field(None, description="专业化领域")
    additional_training_data: Optional[List[Dict[str, str]]] = Field(None, description="额外训练数据")

class CreateProfessionalModelRequest(BaseModel):
    name: str = Field(..., description="智能体名称")
    description: Optional[str] = Field(None, description="智能体描述")
    profession_type: str = Field(..., description="专业类型")
    specialization: Optional[str] = Field(None, description="专业领域")
    personality: str = Field(default="professional", description="个性类型")
    training_examples: List[Dict[str, str]] = Field(..., description="训练示例")

class FineTuneRequest(BaseModel):
    base_model: str = Field(..., description="基础模型名称")
    model_name: str = Field(..., description="新模型名称")
    conversation_data: List[Dict[str, str]] = Field(..., description="对话训练数据")

class TestModelRequest(BaseModel):
    model_name: str = Field(..., description="模型名称")
    test_prompt: str = Field(..., description="测试提示词")

# 初始化服务
training_service = ModelTrainingService()

@router.get("/profession-types")
async def get_profession_types():
    """获取专业类型"""
    return {
        "success": True,
        "data": [
            {
                "id": "teacher",
                "name": "教师",
                "icon": "👨‍🏫",
                "description": "专业的教育工作者，擅长教学和指导",
                "features": ["耐心教学", "因材施教", "知识传授"],
                "specializations": ["数学", "语文", "英语", "物理", "化学", "生物", "历史", "地理", "音乐", "美术", "体育", "计算机"],
                "examples": ["如何解这道数学题？", "请解释这个概念", "学习方法建议"]
            },
            {
                "id": "doctor",
                "name": "医生",
                "icon": "👨‍⚕️",
                "description": "专业的医疗工作者，提供健康咨询",
                "features": ["专业诊断", "健康建议", "医疗知识"],
                "specializations": ["内科", "外科", "儿科", "妇科", "眼科", "皮肤科", "心理科", "中医科"],
                "examples": ["这个症状是什么原因？", "如何预防疾病？", "用药注意事项"]
            },
            {
                "id": "lawyer",
                "name": "律师",
                "icon": "👨‍💼",
                "description": "专业的法律工作者，提供法律咨询",
                "features": ["法律分析", "风险评估", "专业建议"],
                "specializations": ["民事法", "刑事法", "商事法", "劳动法", "知识产权法", "房地产法", "婚姻法", "税法"],
                "examples": ["这种情况如何处理？", "法律风险分析", "合同条款解释"]
            },
            {
                "id": "consultant",
                "name": "顾问",
                "icon": "💼",
                "description": "专业的商业顾问，提供战略建议",
                "features": ["战略分析", "商业洞察", "解决方案"],
                "specializations": ["管理咨询", "财务咨询", "市场咨询", "技术咨询", "人力资源", "战略规划", "投资顾问", "创业指导"],
                "examples": ["市场分析建议", "商业模式优化", "投资决策建议"]
            }
        ]
    }

@router.get("/professions")
async def get_available_professions():
    """获取可用的专业类型（兼容旧接口）"""
    return {
        "success": True,
        "professions": [
            {
                "id": "teacher",
                "name": "专业教师",
                "description": "耐心细致的教育专家，擅长因材施教和启发思考",
                "specializations": [
                    "数学教师", "语文教师", "英语教师", "物理教师",
                    "化学教师", "生物教师", "历史教师", "地理教师"
                ]
            },
            {
                "id": "doctor",
                "name": "医疗顾问",
                "description": "专业的医疗知识顾问，提供健康咨询和医学科普",
                "specializations": [
                    "内科医生", "外科医生", "儿科医生", "妇科医生",
                    "心理医生", "营养师", "康复师"
                ]
            },
            {
                "id": "lawyer",
                "name": "法律顾问",
                "description": "专业的法律咨询专家，提供法律知识和建议",
                "specializations": [
                    "民事律师", "刑事律师", "商事律师", "劳动法律师",
                    "知识产权律师", "房产律师"
                ]
            }
        ]
    }

@router.post("/create-professional")
async def create_professional_model(request: CreateProfessionalModelRequest, background_tasks: BackgroundTasks):
    """创建专业智能体模型"""
    try:
        # 验证专业类型
        available_professions = ["teacher", "doctor", "lawyer", "consultant"]
        if request.profession_type not in available_professions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的专业类型。可用类型: {', '.join(available_professions)}"
            )

        # 验证训练数据
        if not request.training_examples or len(request.training_examples) < 3:
            raise HTTPException(
                status_code=400,
                detail="训练示例至少需要3个"
            )

        # 创建专业模型
        result = await training_service.create_professional_model(
            name=request.name,
            description=request.description,
            profession_type=request.profession_type,
            specialization=request.specialization,
            personality=request.personality,
            training_examples=request.training_examples
        )

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建专业模型失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/create-professional-agent")
async def create_professional_agent(request: CreateProfessionalAgentRequest, background_tasks: BackgroundTasks):
    """创建专业智能体（兼容旧接口）"""
    try:
        # 验证专业类型
        available_professions = ["teacher", "doctor", "lawyer"]
        if request.profession not in available_professions:
            raise HTTPException(
                status_code=400,
                detail=f"不支持的专业类型。可用类型: {', '.join(available_professions)}"
            )

        # 在后台创建模型（因为可能需要较长时间）
        result = await training_service.create_professional_agent(
            profession=request.profession,
            custom_name=request.custom_name,
            additional_training_data=request.additional_training_data,
            specialization=request.specialization
        )

        return result

    except Exception as e:
        logger.error(f"创建专业智能体失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/fine-tune")
async def fine_tune_model(request: FineTuneRequest, background_tasks: BackgroundTasks):
    """微调模型"""
    try:
        # 验证输入数据
        if not request.conversation_data:
            raise HTTPException(status_code=400, detail="训练数据不能为空")
        
        if len(request.conversation_data) < 5:
            raise HTTPException(status_code=400, detail="训练数据至少需要5个对话示例")
        
        # 验证数据格式
        for i, conv in enumerate(request.conversation_data):
            if "input" not in conv or "output" not in conv:
                raise HTTPException(
                    status_code=400, 
                    detail=f"第{i+1}个训练数据格式错误，需要包含 'input' 和 'output' 字段"
                )
        
        # 开始微调
        result = await training_service.fine_tune_with_conversations(
            base_model=request.base_model,
            conversation_data=request.conversation_data,
            model_name=request.model_name
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"微调模型失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/models")
async def get_available_models():
    """获取可用的模型列表"""
    try:
        models = await training_service.get_available_models()
        return {
            "success": True,
            "models": models
        }
    except Exception as e:
        logger.error(f"获取模型列表失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/test-model")
async def test_model(request: TestModelRequest):
    """测试模型性能"""
    try:
        result = await training_service.test_model(
            model_name=request.model_name,
            test_prompt=request.test_prompt
        )
        return result
    except Exception as e:
        logger.error(f"测试模型失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/training-templates")
async def get_training_templates():
    """获取训练数据模板"""
    return {
        "success": True,
        "templates": {
            "teacher": {
                "name": "教师训练模板",
                "examples": [
                    {
                        "input": "我不理解这个概念，能帮我解释一下吗？",
                        "output": "当然可以！让我用一个简单的例子来解释这个概念。首先，你能告诉我你已经了解的部分吗？这样我可以从你熟悉的地方开始。"
                    },
                    {
                        "input": "这道题太难了，我不会做。",
                        "output": "我理解你的感受，遇到困难是很正常的。让我们把这道题分解成几个小步骤，一步一步来解决。你觉得第一步应该做什么？"
                    },
                    {
                        "input": "我总是记不住这些知识点。",
                        "output": "记忆确实需要技巧。我建议你试试这几种方法：1）制作思维导图 2）定期复习 3）联想记忆法。你想先试试哪一种？"
                    }
                ]
            },
            "doctor": {
                "name": "医疗顾问模板",
                "examples": [
                    {
                        "input": "我最近总是感觉疲劳，这是什么原因？",
                        "output": "疲劳可能有多种原因，包括睡眠不足、压力、营养不良或某些疾病。建议你先检查一下作息和饮食习惯。如果症状持续，请及时就医检查。"
                    },
                    {
                        "input": "这种药物有什么副作用吗？",
                        "output": "每种药物都可能有副作用，具体情况因人而异。我建议你仔细阅读药品说明书，并咨询开药的医生。如果出现不适症状，请立即停药并就医。"
                    }
                ]
            },
            "lawyer": {
                "name": "法律顾问模板", 
                "examples": [
                    {
                        "input": "签合同时需要注意什么？",
                        "output": "签合同时请注意：1）仔细阅读所有条款 2）确认双方权利义务 3）注意违约责任条款 4）保留合同副本。如涉及重大利益，建议咨询专业律师。"
                    },
                    {
                        "input": "遇到劳动纠纷应该怎么办？",
                        "output": "劳动纠纷处理步骤：1）收集相关证据 2）先尝试与用人单位协商 3）可申请劳动仲裁 4）必要时寻求法律援助。建议保留好劳动合同、工资条等重要文件。"
                    }
                ]
            }
        }
    }

@router.delete("/models/{model_name}")
async def delete_custom_model(model_name: str):
    """删除自定义模型"""
    try:
        # 这里需要调用 ollama 命令删除模型
        import subprocess
        result = subprocess.run(
            ["ollama", "rm", model_name],
            capture_output=True,
            text=True
        )
        
        if result.returncode == 0:
            return {
                "success": True,
                "message": f"模型 {model_name} 已删除"
            }
        else:
            return {
                "success": False,
                "error": result.stderr
            }
            
    except Exception as e:
        logger.error(f"删除模型失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/training-status/{model_name}")
async def get_training_status(model_name: str):
    """获取训练状态（模拟）"""
    # 这是一个简化版本，实际实现需要跟踪训练进度
    try:
        models = await training_service.get_available_models()
        model_exists = any(model.get("name") == model_name for model in models)
        
        if model_exists:
            return {
                "success": True,
                "status": "completed",
                "progress": 100,
                "message": "模型训练完成"
            }
        else:
            return {
                "success": True,
                "status": "not_found",
                "progress": 0,
                "message": "模型不存在"
            }
            
    except Exception as e:
        logger.error(f"获取训练状态失败: {e}")
        raise HTTPException(status_code=500, detail=str(e))
