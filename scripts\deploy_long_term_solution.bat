@echo off
REM 长期方案部署脚本
REM 按优先级部署三个策略

echo ========================================
echo AI视频生成系统 - 长期方案部署
echo ========================================

set CURRENT_DIR=%cd%
set PROJECT_ROOT=%~dp0..

echo 项目根目录: %PROJECT_ROOT%
cd /d "%PROJECT_ROOT%"

echo.
echo ========================================
echo 策略1: 环境重建
echo ========================================

echo [1/3] 创建Wanx专用环境...
call scripts\setup_wanx_environment.bat
if %errorlevel% neq 0 (
    echo 警告：Wanx环境创建失败，继续下一个策略
    goto strategy2
)

echo [2/3] 测试Wanx环境...
python scripts\test_wanx_environment.py
if %errorlevel% equ 0 (
    echo ✅ 策略1成功！Wanx 2.1环境重建完成
    echo 系统将优先使用新的Wanx环境
    goto install_dependencies
) else (
    echo ❌ 策略1失败，继续策略2
)

:strategy2
echo.
echo ========================================
echo 策略2: 替代模型集成
echo ========================================

echo [1/2] 安装AnimateDiff依赖...
pip install diffusers[torch]==0.30.2
pip install transformers==4.44.0
pip install accelerate==0.33.0
if %errorlevel% neq 0 (
    echo 警告：AnimateDiff依赖安装失败
)

echo [2/2] 测试AnimateDiff...
python -c "from backend.app.tasks.video_generation_animatediff import generate_animatediff_video; print('AnimateDiff可用')"
if %errorlevel% equ 0 (
    echo ✅ 策略2成功！AnimateDiff集成完成
    echo 系统将使用AnimateDiff作为主要引擎
    goto install_dependencies
) else (
    echo ❌ 策略2失败，继续策略3
)

:strategy3
echo.
echo ========================================
echo 策略3: Docker容器化
echo ========================================

echo 检查Docker环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker未安装，请先安装Docker Desktop
    echo 下载地址: https://www.docker.com/products/docker-desktop/
    goto fallback_only
)

echo 检查NVIDIA Container Toolkit...
docker run --rm --gpus all nvidia/cuda:12.1-base-ubuntu22.04 nvidia-smi >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ NVIDIA Container Toolkit未配置
    echo 请参考: https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html
    goto fallback_only
)

echo [1/2] 构建Wanx Docker镜像...
cd docker
docker-compose build wanx-service
if %errorlevel% neq 0 (
    echo ❌ Docker镜像构建失败
    cd ..
    goto fallback_only
)

echo [2/2] 启动Wanx Docker服务...
docker-compose up -d wanx-service
if %errorlevel% equ 0 (
    echo ✅ 策略3成功！Docker容器化部署完成
    echo 系统将使用Docker容器运行Wanx 2.1
    cd ..
    goto install_dependencies
) else (
    echo ❌ 策略3失败
    cd ..
)

:fallback_only
echo.
echo ========================================
echo 回退方案: 高质量替代视频
echo ========================================
echo 所有高级策略都失败，将使用高质量回退方案
echo 这仍然能提供良好的用户体验

:install_dependencies
echo.
echo ========================================
echo 安装系统依赖
echo ========================================

echo 安装智能视频生成管理器依赖...
pip install opencv-python>=4.8.0
pip install numpy>=1.24.0
pip install pillow>=9.0.0

echo 重启后端服务...
taskkill /f /im python.exe >nul 2>&1
timeout /t 2 >nul

echo.
echo ========================================
echo 部署完成
echo ========================================

echo 检查最终系统状态...
python -c "
from backend.app.tasks.video_generation_manager import video_manager
import json
status = video_manager.get_engine_status()
print('=== 可用引擎 ===')
for name, info in status.items():
    print(f'{name}: {info[\"name\"]} - {info[\"status\"]}')
print('=== 部署成功 ===')
"

echo.
echo 🎉 长期方案部署完成！
echo.
echo 系统现在具有以下能力：
echo - 智能引擎选择
echo - 多重备用方案
echo - 自动故障转移
echo - 高质量视频生成
echo.
echo 建议：
echo 1. 重启后端服务以应用更改
echo 2. 通过前端界面测试视频生成
echo 3. 查看日志确认最佳引擎被选中
echo.

pause
cd /d "%CURRENT_DIR%"
