import os
import torch
import sys
import shutil

def fix_utils_module():
    """
    修复WAN2.1模型中的utils.py文件中的问题：
    1. 添加shutil导入（如果缺失）
    2. 修复数据类型转换问题，确保Float到Byte的正确转换
    3. 添加维度不匹配的处理逻辑
    """
    # 定位utils.py文件路径
    model_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    utils_path = os.path.join(model_dir, 'wan', 'utils', 'utils.py')
    
    if not os.path.exists(utils_path):
        print(f"错误：无法找到utils.py文件: {utils_path}")
        return False
    
    # 创建备份
    backup_path = utils_path + '.bak'
    try:
        shutil.copy2(utils_path, backup_path)
        print(f"已创建备份: {backup_path}")
    except Exception as e:
        print(f"创建备份失败: {e}")
        return False
    
    # 读取文件内容
    try:
        with open(utils_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取文件失败: {e}")
        return False
    
    # 修复1: 确保shutil已导入
    if 'import shutil' not in content:
        content = content.replace('import os.path as osp', 'import os.path as osp\nimport shutil')
        print("已添加 shutil 导入")
    
    # 修复2: 修复数据类型转换问题
    # 将tensor类型转换代码替换为更安全的版本
    if 'tensor = (tensor * 255.0).round()' in content:
        content = content.replace(
            'tensor = (tensor * 255.0).round()',
            'tensor = torch.clamp(tensor, 0.0, 1.0)\n            tensor = (tensor * 255.0).round()'
        )
        # 更改类型转换方法
        content = content.replace(
            'tensor = tensor.type(torch.uint8).cpu()',
            'tensor = tensor.to(torch.uint8).cpu()'
        )
        print("已修复数据类型转换问题 - 添加值范围限制和更安全的类型转换")
    
    # 修复3: 添加更好的维度不匹配处理
    # 搜索make_grid调用的位置
    if 'torchvision.utils.make_grid(' in content:
        # 添加更健壮的网格创建逻辑
        grid_section_start = content.find('# 处理网格排列')
        grid_section_end = content.find('# 检查数据是否有效')
        
        if grid_section_start != -1 and grid_section_end != -1:
            old_grid_section = content[grid_section_start:grid_section_end]
            
            new_grid_section = '''# 处理网格排列
            # 确保数据是浮点型且值在有效范围内
            tensor = tensor.float()  # 确保是浮点型
            
            # 处理网格排列前先确保数值范围合适
            if normalize:
                # 在网格排列前先规范化到0-1范围
                tensor = (tensor - min(value_range)) / (max(value_range) - min(value_range))
                tensor = torch.clamp(tensor, 0.0, 1.0)  # 安全裁剪确保0-1范围
            
            # 处理网格排列
            try:
                # 使用修改后的方法创建网格
                tensor_list = []
                for u in tensor.unbind(0):  # 假设张量是(T, C, H, W)格式
                    # 对每帧应用make_grid，但不再在这里做normalize (已经在上面完成)
                    grid = torchvision.utils.make_grid(
                        u, nrow=nrow, normalize=False, value_range=(0, 1))
                    tensor_list.append(grid)
                
                tensor = torch.stack(tensor_list, dim=0).permute(0, 2, 3, 1)  # 变为(T, H, W, C)
            except Exception as e:
                print(f"网格创建失败: {e}，尝试直接转换格式", flush=True)
                # 如果网格创建失败，直接转换格式
                if tensor.size(1) == 3 or tensor.size(1) == 1:  # (T, C, H, W)
                    tensor = tensor.permute(0, 2, 3, 1)  # 转为(T, H, W, C)
            '''
            
            content = content.replace(old_grid_section, new_grid_section)
            print("已改进网格排列和格式转换处理")
    
    # 保存修改后的文件
    try:
        with open(utils_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"成功修复文件: {utils_path}")
        return True
    except Exception as e:
        print(f"保存文件失败: {e}")
        # 恢复备份
        try:
            shutil.copy2(backup_path, utils_path)
            print("已恢复备份")
        except:
            print("恢复备份失败，原始文件可能已损坏")
        return False

if __name__ == "__main__":
    print("开始修复WAN2.1模型的utils.py文件...")
    success = fix_utils_module()
    if success:
        print("修复完成！现在可以重新运行视频生成任务。")
    else:
        print("修复失败，请检查错误信息。")