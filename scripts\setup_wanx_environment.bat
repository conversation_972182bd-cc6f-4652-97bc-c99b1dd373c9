@echo off
REM Wanx 2.1 专用环境设置脚本
REM 解决环境兼容性问题

echo ========================================
echo Wanx 2.1 专用环境设置
echo ========================================

REM 检查Python版本
python --version
if %errorlevel% neq 0 (
    echo 错误：Python未安装或不在PATH中
    pause
    exit /b 1
)

REM 创建专用虚拟环境
echo.
echo [1/6] 创建Wanx专用虚拟环境...
python -m venv wanx_env
if %errorlevel% neq 0 (
    echo 错误：无法创建虚拟环境
    pause
    exit /b 1
)

REM 激活虚拟环境
echo.
echo [2/6] 激活虚拟环境...
call wanx_env\Scripts\activate.bat

REM 升级pip
echo.
echo [3/6] 升级pip...
python -m pip install --upgrade pip

REM 安装精确版本的PyTorch（兼容CUDA 12.1）
echo.
echo [4/6] 安装PyTorch（CUDA 12.1兼容版本）...
pip install torch==2.4.0 torchvision==0.19.0 torchaudio==2.4.0 --index-url https://download.pytorch.org/whl/cu121

REM 安装Wanx 2.1的精确依赖
echo.
echo [5/6] 安装Wanx 2.1依赖...
pip install diffusers==0.30.2
pip install transformers==4.44.0
pip install tokenizers==0.19.1
pip install accelerate==0.33.0
pip install opencv-python==*********
pip install imageio==2.35.1
pip install imageio-ffmpeg==0.5.1
pip install easydict==1.13
pip install ftfy==6.2.3
pip install numpy==1.26.4
pip install tqdm==4.66.5

REM 尝试安装flash_attn（可选，可能提高性能）
echo.
echo [6/6] 尝试安装flash_attn（可选）...
pip install flash-attn --no-build-isolation
if %errorlevel% neq 0 (
    echo 警告：flash_attn安装失败，将使用标准注意力机制
)

REM 验证安装
echo.
echo ========================================
echo 验证安装...
echo ========================================
python -c "import torch; print(f'PyTorch: {torch.__version__}'); print(f'CUDA available: {torch.cuda.is_available()}'); print(f'CUDA version: {torch.version.cuda}')"
python -c "import diffusers; print(f'Diffusers: {diffusers.__version__}')"
python -c "import transformers; print(f'Transformers: {transformers.__version__}')"
python -c "import cv2; print(f'OpenCV: {cv2.__version__}')"

echo.
echo ========================================
echo 环境设置完成！
echo ========================================
echo 要使用此环境，请运行：
echo call wanx_env\Scripts\activate.bat
echo.
pause
