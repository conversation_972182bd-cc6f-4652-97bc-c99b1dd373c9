<template>
  <div class="training-data-manager">
    <!-- 数据导入方式选择 -->
    <div class="import-methods">
      <h3>📊 训练数据管理</h3>
      <div class="method-tabs">
        <div 
          v-for="method in importMethods" 
          :key="method.id"
          :class="['method-tab', { active: activeMethod === method.id }]"
          @click="activeMethod = method.id"
        >
          <div class="method-icon">{{ method.icon }}</div>
          <div class="method-info">
            <div class="method-name">{{ method.name }}</div>
            <div class="method-desc">{{ method.description }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- Excel文件导入 -->
    <div v-if="activeMethod === 'excel'" class="import-section">
      <div class="section-header">
        <h4>📋 Excel文件导入</h4>
        <p>支持.xlsx、.xls格式，自动识别列结构</p>
      </div>
      
      <div class="excel-upload">
        <el-upload
          ref="excelUpload"
          :auto-upload="false"
          :on-change="handleExcelChange"
          :before-upload="beforeExcelUpload"
          accept=".xlsx,.xls"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将Excel文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持.xlsx/.xls格式，建议列结构：问题 | 回答 | 分类（可选）
            </div>
          </template>
        </el-upload>
      </div>

      <!-- Excel数据预览 -->
      <div v-if="excelData.length > 0" class="excel-preview">
        <div class="preview-header">
          <h4>📋 数据预览 (共{{ excelData.length }}条)</h4>
          <div class="preview-actions">
            <el-button @click="clearExcelData" size="small">清空</el-button>
            <el-button @click="importExcelData" type="primary" size="small">
              导入{{ selectedRows.length > 0 ? selectedRows.length : excelData.length }}条数据
            </el-button>
          </div>
        </div>
        
        <el-table 
          :data="excelData.slice(0, 100)" 
          @selection-change="handleSelectionChange"
          max-height="400"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="input" label="问题/输入" min-width="200" show-overflow-tooltip />
          <el-table-column prop="output" label="回答/输出" min-width="300" show-overflow-tooltip />
          <el-table-column prop="category" label="分类" width="120" show-overflow-tooltip />
        </el-table>
        
        <div v-if="excelData.length > 100" class="data-pagination">
          <p>仅显示前100条数据，实际导入时会处理所有{{ excelData.length }}条数据</p>
        </div>
      </div>
    </div>

    <!-- 批量文本导入 -->
    <div v-if="activeMethod === 'text'" class="import-section">
      <div class="section-header">
        <h4>📝 批量文本导入</h4>
        <p>支持多种格式：JSON、CSV、问答对等</p>
      </div>
      
      <div class="text-format-selector">
        <el-radio-group v-model="textFormat">
          <el-radio label="json">JSON格式</el-radio>
          <el-radio label="csv">CSV格式</el-radio>
          <el-radio label="qa">问答对格式</el-radio>
        </el-radio-group>
      </div>
      
      <div class="text-input-area">
        <el-input
          v-model="textInput"
          type="textarea"
          :rows="12"
          :placeholder="getTextPlaceholder()"
        />
        <div class="text-actions">
          <el-button @click="parseTextData" type="primary">解析数据</el-button>
          <el-button @click="clearTextData">清空</el-button>
        </div>
      </div>
      
      <!-- 解析结果预览 -->
      <div v-if="parsedTextData.length > 0" class="text-preview">
        <h4>解析结果 ({{ parsedTextData.length }}条)</h4>
        <div class="parsed-items">
          <div v-for="(item, index) in parsedTextData.slice(0, 10)" :key="index" class="parsed-item">
            <div class="item-input">问：{{ item.input }}</div>
            <div class="item-output">答：{{ item.output }}</div>
          </div>
        </div>
        <div v-if="parsedTextData.length > 10" class="more-items">
          还有{{ parsedTextData.length - 10 }}条数据...
        </div>
        <el-button @click="importTextData" type="primary" style="margin-top: 1rem;">
          导入所有数据
        </el-button>
      </div>
    </div>

    <!-- 手动添加 -->
    <div v-if="activeMethod === 'manual'" class="import-section">
      <div class="section-header">
        <h4>✏️ 手动添加</h4>
        <p>逐条添加训练数据，适合精细化定制</p>
      </div>
      
      <div class="manual-add-form">
        <el-form :model="newItem" label-width="80px">
          <el-form-item label="问题">
            <el-input v-model="newItem.input" placeholder="输入问题或用户输入" />
          </el-form-item>
          <el-form-item label="回答">
            <el-input 
              v-model="newItem.output" 
              type="textarea" 
              :rows="3"
              placeholder="输入期望的回答"
            />
          </el-form-item>
          <el-form-item label="分类">
            <el-input v-model="newItem.category" placeholder="数据分类（可选）" />
          </el-form-item>
          <el-form-item>
            <el-button @click="addManualItem" type="primary">添加</el-button>
            <el-button @click="clearManualForm">清空</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- AI智能生成 -->
    <div v-if="activeMethod === 'ai'" class="import-section">
      <div class="section-header">
        <h4>🤖 AI智能生成</h4>
        <p>基于您的专业领域，AI自动生成专业训练数据</p>
      </div>
      
      <div class="ai-generation-form">
        <el-form :model="aiConfig" label-width="120px">
          <el-form-item label="专业领域">
            <el-input v-model="aiConfig.domain" placeholder="如：英语翻译、法律咨询、医疗诊断" />
          </el-form-item>
          <el-form-item label="数据类型">
            <el-select v-model="aiConfig.dataType" placeholder="选择数据类型">
              <el-option label="问答对话" value="qa" />
              <el-option label="翻译对照" value="translation" />
              <el-option label="专业咨询" value="consultation" />
              <el-option label="教学指导" value="teaching" />
            </el-select>
          </el-form-item>
          <el-form-item label="生成数量">
            <el-slider v-model="aiConfig.count" :min="5" :max="50" show-tooltip />
          </el-form-item>
          <el-form-item>
            <el-button @click="generateAIData" type="primary" :loading="aiGenerating">
              {{ aiGenerating ? '生成中...' : 'AI智能生成' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 当前训练数据列表 -->
    <div class="current-data-section">
      <div class="section-header">
        <h4>📚 当前训练数据 ({{ trainingData.length }}条)</h4>
        <div class="data-actions">
          <el-button @click="exportData" size="small">导出</el-button>
          <el-button @click="clearAllData" size="small" type="danger">清空所有</el-button>
        </div>
      </div>
      
      <div v-if="trainingData.length === 0" class="empty-data">
        <p>暂无训练数据，请使用上方方式添加数据</p>
      </div>
      
      <div v-else class="data-list">
        <div v-for="(item, index) in trainingData" :key="index" class="data-item">
          <div class="item-header">
            <span class="item-index">#{{ index + 1 }}</span>
            <span v-if="item.category" class="item-category">{{ item.category }}</span>
            <el-button @click="removeItem(index)" size="small" type="danger">删除</el-button>
          </div>
          <div class="item-content">
            <div class="item-input">
              <strong>问：</strong>{{ item.input }}
            </div>
            <div class="item-output">
              <strong>答：</strong>{{ item.output }}
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div v-if="trainingData.length > 20" class="data-pagination">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="20"
          :total="trainingData.length"
          layout="prev, pager, next, total"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'

export default {
  name: 'TrainingDataManager',
  components: {
    UploadFilled
  },
  props: {
    modelValue: {
      type: Array,
      default: () => []
    }
  },
  emits: ['update:modelValue', 'data-changed'],
  setup(props, { emit }) {
    const activeMethod = ref('excel')
    const currentPage = ref(1)
    
    // 导入方式
    const importMethods = [
      {
        id: 'excel',
        name: 'Excel导入',
        icon: '📊',
        description: '从Excel文件批量导入'
      },
      {
        id: 'text',
        name: '文本导入',
        icon: '📝',
        description: '粘贴文本批量导入'
      },
      {
        id: 'manual',
        name: '手动添加',
        icon: '✏️',
        description: '逐条手动添加'
      },
      {
        id: 'ai',
        name: 'AI生成',
        icon: '🤖',
        description: 'AI智能生成数据'
      }
    ]
    
    // 训练数据
    const trainingData = ref([...props.modelValue])
    
    // Excel相关
    const excelData = ref([])
    const selectedRows = ref([])
    
    // 文本导入相关
    const textFormat = ref('json')
    const textInput = ref('')
    const parsedTextData = ref([])
    
    // 手动添加
    const newItem = reactive({
      input: '',
      output: '',
      category: ''
    })
    
    // AI生成
    const aiConfig = reactive({
      domain: '',
      dataType: 'qa',
      count: 10
    })
    const aiGenerating = ref(false)
    
    // 监听数据变化
    watch(trainingData, (newData) => {
      emit('update:modelValue', newData)
      emit('data-changed', newData)
    }, { deep: true })
    
    // Excel处理方法
    const handleExcelChange = (file) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const data = new Uint8Array(e.target.result)
          const workbook = XLSX.read(data, { type: 'array' })
          const sheetName = workbook.SheetNames[0]
          const worksheet = workbook.Sheets[sheetName]
          const jsonData = XLSX.utils.sheet_to_json(worksheet)
          
          // 转换数据格式
          excelData.value = jsonData.map(row => {
            const keys = Object.keys(row)
            return {
              input: row[keys[0]] || '',
              output: row[keys[1]] || '',
              category: row[keys[2]] || ''
            }
          }).filter(item => item.input && item.output)
          
          ElMessage.success(`成功解析${excelData.value.length}条数据`)
        } catch (error) {
          ElMessage.error('Excel文件解析失败')
          console.error(error)
        }
      }
      reader.readAsArrayBuffer(file.raw)
    }
    
    const beforeExcelUpload = () => {
      return true
    }
    
    const handleSelectionChange = (selection) => {
      selectedRows.value = selection
    }
    
    const importExcelData = () => {
      const dataToImport = selectedRows.value.length > 0 ? selectedRows.value : excelData.value
      trainingData.value.push(...dataToImport)
      ElMessage.success(`成功导入${dataToImport.length}条数据`)
      excelData.value = []
      selectedRows.value = []
    }
    
    const clearExcelData = () => {
      excelData.value = []
      selectedRows.value = []
    }
    
    // 文本处理方法
    const getTextPlaceholder = () => {
      const placeholders = {
        json: '[{"input": "问题1", "output": "回答1"}, {"input": "问题2", "output": "回答2"}]',
        csv: '问题1,回答1,分类1\n问题2,回答2,分类2',
        qa: '问：问题1\n答：回答1\n\n问：问题2\n答：回答2'
      }
      return placeholders[textFormat.value]
    }
    
    const parseTextData = () => {
      try {
        let parsed = []
        
        if (textFormat.value === 'json') {
          parsed = JSON.parse(textInput.value)
        } else if (textFormat.value === 'csv') {
          const lines = textInput.value.split('\n').filter(line => line.trim())
          parsed = lines.map(line => {
            const parts = line.split(',')
            return {
              input: parts[0]?.trim() || '',
              output: parts[1]?.trim() || '',
              category: parts[2]?.trim() || ''
            }
          })
        } else if (textFormat.value === 'qa') {
          const pairs = textInput.value.split('\n\n')
          parsed = pairs.map(pair => {
            const lines = pair.split('\n')
            const input = lines.find(line => line.startsWith('问：'))?.replace('问：', '').trim() || ''
            const output = lines.find(line => line.startsWith('答：'))?.replace('答：', '').trim() || ''
            return { input, output, category: '' }
          })
        }
        
        parsedTextData.value = parsed.filter(item => item.input && item.output)
        ElMessage.success(`成功解析${parsedTextData.value.length}条数据`)
      } catch (error) {
        ElMessage.error('数据格式错误，请检查输入')
        console.error(error)
      }
    }
    
    const importTextData = () => {
      trainingData.value.push(...parsedTextData.value)
      ElMessage.success(`成功导入${parsedTextData.value.length}条数据`)
      textInput.value = ''
      parsedTextData.value = []
    }
    
    const clearTextData = () => {
      textInput.value = ''
      parsedTextData.value = []
    }
    
    // 手动添加方法
    const addManualItem = () => {
      if (!newItem.input || !newItem.output) {
        ElMessage.warning('请填写问题和回答')
        return
      }
      
      trainingData.value.push({
        input: newItem.input,
        output: newItem.output,
        category: newItem.category
      })
      
      clearManualForm()
      ElMessage.success('添加成功')
    }
    
    const clearManualForm = () => {
      newItem.input = ''
      newItem.output = ''
      newItem.category = ''
    }
    
    // AI生成方法
    const generateAIData = async () => {
      if (!aiConfig.domain) {
        ElMessage.warning('请输入专业领域')
        return
      }
      
      aiGenerating.value = true
      
      try {
        // 模拟AI生成
        await new Promise(resolve => setTimeout(resolve, 2000))
        
        const generated = []
        for (let i = 0; i < aiConfig.count; i++) {
          generated.push({
            input: `${aiConfig.domain}相关问题${i + 1}`,
            output: `这是关于${aiConfig.domain}的专业回答${i + 1}`,
            category: aiConfig.dataType
          })
        }
        
        trainingData.value.push(...generated)
        ElMessage.success(`AI成功生成${generated.length}条数据`)
      } catch (error) {
        ElMessage.error('AI生成失败')
      } finally {
        aiGenerating.value = false
      }
    }
    
    // 数据管理方法
    const removeItem = (index) => {
      trainingData.value.splice(index, 1)
    }
    
    const clearAllData = () => {
      trainingData.value = []
      ElMessage.success('已清空所有数据')
    }
    
    const exportData = () => {
      const dataStr = JSON.stringify(trainingData.value, null, 2)
      const blob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = 'training_data.json'
      a.click()
      URL.revokeObjectURL(url)
    }
    
    return {
      activeMethod,
      currentPage,
      importMethods,
      trainingData,
      excelData,
      selectedRows,
      textFormat,
      textInput,
      parsedTextData,
      newItem,
      aiConfig,
      aiGenerating,
      handleExcelChange,
      beforeExcelUpload,
      handleSelectionChange,
      importExcelData,
      clearExcelData,
      getTextPlaceholder,
      parseTextData,
      importTextData,
      clearTextData,
      addManualItem,
      clearManualForm,
      generateAIData,
      removeItem,
      clearAllData,
      exportData
    }
  }
}
</script>

<style scoped>
.training-data-manager {
  max-width: 1000px;
  margin: 0 auto;
}

.import-methods h3 {
  margin-bottom: 1rem;
  color: #1a202c;
}

.method-tabs {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.method-tab {
  display: flex;
  align-items: center;
  padding: 1rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.method-tab:hover {
  border-color: #667eea;
}

.method-tab.active {
  border-color: #667eea;
  background: #f0f4ff;
}

.method-icon {
  font-size: 2rem;
  margin-right: 1rem;
}

.method-name {
  font-weight: 600;
  color: #1a202c;
}

.method-desc {
  font-size: 0.9rem;
  color: #64748b;
}

.import-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
}

.section-header h4 {
  margin: 0 0 0.5rem 0;
  color: #1a202c;
}

.section-header p {
  margin: 0 0 1rem 0;
  color: #64748b;
  font-size: 0.9rem;
}

.excel-preview,
.text-preview {
  margin-top: 1rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.data-item {
  margin-bottom: 1rem;
  padding: 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: white;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.item-index {
  font-weight: 600;
  color: #667eea;
}

.item-category {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
}

.item-content {
  font-size: 0.9rem;
}

.item-input {
  margin-bottom: 0.5rem;
  color: #1a202c;
}

.item-output {
  color: #4a5568;
}

.empty-data {
  text-align: center;
  padding: 2rem;
  color: #64748b;
}

.current-data-section {
  margin-top: 2rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
}
</style>
