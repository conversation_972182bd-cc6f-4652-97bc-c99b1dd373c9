import os

# 确定utils.py的绝对路径
current_dir = os.path.dirname(os.path.abspath(__file__))
utils_path = os.path.join(current_dir, 'wan', 'utils', 'utils.py')

print(f"尝试读取文件: {utils_path}")

# 检查文件是否存在
if not os.path.exists(utils_path):
    print(f"错误: 文件不存在 {utils_path}")
    # 尝试查找文件
    for root, dirs, files in os.walk(current_dir):
        if 'utils.py' in files and 'wan' in root:
            possible_path = os.path.join(root, 'utils.py')
            print(f"找到可能的utils.py: {possible_path}")
    exit(1)

# 读取文件内容
with open(utils_path, 'r', encoding='utf-8') as f:
    content = f.read()

# 添加shutil导入
if 'import shutil' not in content:
    content = content.replace('import os.path as osp', 'import os.path as osp\nimport shutil')
    print("已添加shutil导入")

# 修复数据类型转换问题 - 在cache_video函数中更新tensor处理部分
# 这部分代码处理将float类型的数据转换为uint8类型，避免类型转换错误
if 'tensor = tensor.type(torch.uint8).cpu()' in content:
    content = content.replace(
        'tensor = (tensor * 255.0).round()', 
        'tensor = torch.clamp(tensor, 0.0, 1.0)\n            tensor = (tensor * 255.0).round()'
    )
    print("已修复tensor数据类型转换问题 - 添加值范围限制")

# 写回文件
with open(utils_path, 'w', encoding='utf-8') as f:
    f.write(content)

print(f"成功修复 {utils_path}")
print("1. 添加了shutil导入")
print("2. 修复了数据类型转换问题，确保值在0-1范围内再转为uint8")

# 输出前几行以验证
with open(utils_path, 'r', encoding='utf-8') as f:
    for i, line in enumerate(f):
        if i < 10:
            print(line.strip())
        else:
            break

# 查找与类型转换相关的行
print("\n相关的数据类型转换代码：")
with open(utils_path, 'r', encoding='utf-8') as f:
    lines = f.readlines()
    for i, line in enumerate(lines):
        if "tensor =" in line and ("255" in line or "uint8" in line or "clamp" in line):
            start_line = max(0, i-1)
            end_line = min(len(lines), i+3)
            print(f"行 {start_line+1}-{end_line}:")
            for j in range(start_line, end_line):
                print(f"  {lines[j].strip()}")