#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Wan2.1 Black Video Generation Fix

This script addresses multiple issues in the Wan2.1 model that cause black video generation:
1. Shape mismatch between noise prediction and latents tensors
2. Zero-value tensors leading to black frames
3. Improper tensor normalization and data type conversion
4. Issues with video frame caching and saving

Author: <PERSON> Assistant
Date: May 27, 2025
"""

import os
import sys
import logging
import torch
import shutil
import tempfile
import re
from pathlib import Path
import subprocess

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("logs/fix_wan_video_blackscreen.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("fix-wan-video")

def backup_file(filepath):
    """Create a backup of the file before modifying it"""
    if not os.path.exists(filepath):
        logger.error(f"File not found: {filepath}")
        return False
    
    backup_path = f"{filepath}.bak_{int(time.time())}"
    try:
        shutil.copy2(filepath, backup_path)
        logger.info(f"Created backup: {backup_path}")
        return True
    except Exception as e:
        logger.error(f"Failed to create backup for {filepath}: {str(e)}")
        return False

def fix_utils_py():
    """Fix the utils.py file to properly handle video tensor conversion and saving"""
    utils_path = "wan/utils/utils.py"
    
    if not os.path.exists(utils_path):
        logger.error(f"Utils file not found: {utils_path}")
        return False
    
    logger.info(f"Fixing {utils_path}...")
    
    # Create backup
    if not backup_file(utils_path):
        return False
    
    try:
        with open(utils_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if the file has already been fixed
        if "# Enhanced video tensor handling" in content:
            logger.info(f"File {utils_path} already contains fixes")
            return True
        
        # Replace the cache_video function with an improved version
        cache_video_pattern = r"def cache_video\(video, path, h=None, w=None\):.*?(?=def|\Z)"
        improved_cache_video = """def cache_video(video, path, h=None, w=None):
    \"\"\"
    Enhanced video tensor handling and caching function
    Handles zero values, adds noise to prevent black videos, and ensures proper format
    \"\"\"
    logger = logging.getLogger("WAN-Utils")
    
    logger.info(f"Processing video frames with shape: {video.shape if hasattr(video, 'shape') else 'unknown'}")
    
    # Ensure video is a tensor and on CPU
    if isinstance(video, torch.Tensor):
        logger.info(f"Converting tensor from {video.device} to CPU")
        video = video.detach().cpu()
    elif not isinstance(video, torch.Tensor):
        logger.error(f"Video is not a tensor but {type(video)}")
        if hasattr(video, '__iter__'):
            # Try to convert list/tuple to tensor
            try:
                video = torch.stack(list(video)) if len(video) > 0 else torch.zeros((16, 720, 1280, 3))
                logger.info(f"Converted iterable to tensor with shape {video.shape}")
            except Exception as e:
                logger.error(f"Failed to convert to tensor: {str(e)}")
                # Create default tensor
                video = torch.zeros((16, 720, 1280, 3))
        else:
            video = torch.zeros((16, 720, 1280, 3))
    
    # Ensure proper dimensionality - expect (T, H, W, C) or (C, T, H, W)
    if len(video.shape) != 4:
        logger.warning(f"Unexpected tensor shape: {video.shape}, reshaping...")
        if len(video.shape) == 3:
            # Add singleton dimension based on shape
            if video.shape[0] == 3:  # (C, H, W)
                video = video.unsqueeze(1)  # -> (C, 1, H, W)
            else:  # (H, W, C)
                video = video.unsqueeze(0)  # -> (1, H, W, C)
        else:
            # Create default tensor for invalid shapes
            logger.error(f"Cannot reshape tensor with shape {video.shape}")
            video = torch.zeros((16, 720, 1280, 3))
    
    # Standardize to (T, H, W, C) format if needed
    if video.shape[0] == 3 and video.shape[-1] != 3:  # (C, T, H, W) format
        logger.info(f"Converting from (C,T,H,W) to (T,H,W,C) format")
        video = video.permute(1, 2, 3, 0)
    elif video.shape[1] == 3 and video.shape[-1] != 3:  # (T, C, H, W) format
        logger.info(f"Converting from (T,C,H,W) to (T,H,W,C) format")
        video = video.permute(0, 2, 3, 1)
    
    # Check data range and type
    min_val = float(video.min())
    max_val = float(video.max())
    logger.info(f"Video data range: [{min_val}, {max_val}]")
    
    # Check for uniform values (potential black frames)
    if max_val - min_val < 1e-6:
        logger.warning(f"Video has uniform values ({min_val}), adding noise to prevent black frames")
        # Add significant noise to make frames visible
        video = video + torch.rand_like(video) * 0.5
        # Recompute range
        min_val = float(video.min())
        max_val = float(video.max())
        logger.info(f"After adding noise, range: [{min_val}, {max_val}]")
    
    # Normalize based on value range
    if -1.0 <= min_val < 0 and 0 < max_val <= 1.0:
        logger.info(f"Normalizing from [-1,1] to [0,1] range")
        video = (video + 1) / 2
    
    # Ensure data is in 0-1 range
    if min_val < 0 or max_val > 1.0:
        logger.info(f"Clamping values to [0,1] range")
        video = torch.clamp(video, 0.0, 1.0)
    
    # Convert to uint8 for saving
    logger.info(f"Converting to uint8 format")
    video = (video * 255).to(torch.uint8)
    
    # Ensure minimum number of frames for valid video
    num_frames = video.shape[0]
    if num_frames < 16:
        logger.warning(f"Too few frames ({num_frames}), extending to 16")
        # Repeat frames to reach minimum length
        repeats = max(1, int(16 / num_frames) + 1)
        video = video.repeat_interleave(repeats, dim=0)
        # Trim to 16 frames if we went over
        video = video[:16]
        logger.info(f"Extended to {video.shape[0]} frames")
    
    # Save video with multiple fallback options
    try:
        # Create a temporary directory to save frames
        with tempfile.TemporaryDirectory() as temp_dir:
            logger.info(f"Saving video frames to temporary directory: {temp_dir}")
            
            # Save each frame as an image
            for i in range(video.shape[0]):
                frame = video[i].numpy()
                frame_path = os.path.join(temp_dir, f"frame_{i:04d}.png")
                
                import cv2
                # OpenCV expects BGR, but our tensor is RGB
                if frame.shape[-1] == 3:
                    frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                cv2.imwrite(frame_path, frame)
            
            # Ensure directory for output video exists
            os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)
            
            # Use FFmpeg to combine frames with higher bitrate
            framerate = 8  # 8 FPS
            ffmpeg_cmd = f"ffmpeg -y -framerate {framerate} -i {temp_dir}/frame_%04d.png -c:v libx264 -preset medium -crf 18 -pix_fmt yuv420p -b:v 2M {path}"
            logger.info(f"Running FFmpeg: {ffmpeg_cmd}")
            
            result = subprocess.run(ffmpeg_cmd, shell=True, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"FFmpeg error: {result.stderr}")
                raise RuntimeError(f"FFmpeg failed: {result.stderr}")
            
            # Verify the file exists and has content
            if os.path.exists(path) and os.path.getsize(path) > 1000:  # Minimum size for valid video
                logger.info(f"Successfully saved video to {path}, size: {os.path.getsize(path)} bytes")
                return True
            else:
                logger.warning(f"Video file exists but may be too small: {os.path.getsize(path) if os.path.exists(path) else 'not found'}")
                raise ValueError(f"Generated video may be invalid (too small)")
    
    except Exception as e:
        logger.error(f"Failed to save video using primary method: {str(e)}")
        
        # Fallback method using imageio
        try:
            logger.info("Attempting fallback with imageio...")
            import imageio
            
            # Convert tensor to numpy for imageio
            if isinstance(video, torch.Tensor):
                video_np = video.numpy()
            else:
                video_np = video
            
            # Ensure correct format
            if video_np.shape[-1] != 3:
                logger.warning(f"Unexpected frame format: {video_np.shape}")
                # Try to correct format
                if video_np.shape[0] == 3:  # (C,T,H,W)
                    video_np = np.transpose(video_np, (1, 2, 3, 0))
            
            # Save with imageio
            imageio.mimwrite(path, video_np, fps=8, quality=8)
            
            # Verify file
            if os.path.exists(path) and os.path.getsize(path) > 1000:
                logger.info(f"Successfully saved video using imageio fallback, size: {os.path.getsize(path)}")
                return True
            else:
                logger.error(f"Imageio fallback produced invalid file")
                return False
        
        except Exception as e2:
            logger.error(f"All video saving methods failed: {str(e2)}")
            return False
"""
        
        # Apply the replacement
        updated_content = re.sub(cache_video_pattern, improved_cache_video, content, flags=re.DOTALL)
        
        # Write the updated content
        with open(utils_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"Successfully updated {utils_path}")
        return True
    
    except Exception as e:
        logger.error(f"Failed to fix {utils_path}: {str(e)}")
        return False

def fix_text2video_py():
    """Fix the text2video.py file to handle shape mismatches properly"""
    text2video_path = "wan/text2video.py"
    
    if not os.path.exists(text2video_path):
        logger.error(f"File not found: {text2video_path}")
        return False
    
    logger.info(f"Fixing {text2video_path}...")
    
    # Create backup
    if not backup_file(text2video_path):
        return False
    
    try:
        with open(text2video_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if the file has already been fixed
        if "# Enhanced shape mismatch handling" in content:
            logger.info(f"File {text2video_path} already contains fixes")
            return True
        
        # Find the section where shape mismatches are handled
        shape_mismatch_pattern = r"(if shape_mismatch:.*?)(if dim_count_mismatch:.*?)(else:.*?)(logging\.info\(f\"已调整noise_pred至匹配形状:)"
        
        # Create improved shape mismatch handling code
        improved_shape_mismatch = """if shape_mismatch:
                    logging.info(f"检测到形状不匹配: noise_pred={noise_shape}, latents[0]={latent_shape}")
                    
                    # Enhanced shape mismatch handling
                    logging.info("使用改进的形状匹配算法")
                    
                    # Create tensor with correct shape
                    adjusted_noise = torch.zeros_like(latents[0])
                    
                    # Add small random noise to prevent zero values
                    adjusted_noise += torch.randn_like(adjusted_noise) * 0.05
                    
                    # Initialize with data from noise_pred when possible
                    if dim_count_mismatch:
                        logging.info(f"维度数量不匹配: noise_pred={noise_ndim}D, latents[0]={latent_ndim}D")
                        # Special handling for dimension count mismatch
                        if noise_pred.numel() > 0:
                            # Try to reshape if element counts allow
                            if noise_pred.numel() == latents[0].numel():
                                try:
                                    reshaped = noise_pred.reshape(latents[0].shape)
                                    adjusted_noise = reshaped
                                    logging.info(f"成功将noise_pred重塑为所需形状")
                                except Exception as e:
                                    logging.error(f"重塑失败: {str(e)}")
                            else:
                                # Cannot reshape exactly, copy what we can
                                logging.info(f"无法精确重塑，元素数量不同: noise_pred={noise_pred.numel()}, latents={latents[0].numel()}")
                                # Use partial copying with random fill
                                adjusted_noise = torch.randn_like(latents[0]) * 0.1
                    else:
                        # Dimensions match in count but not size
                        # Copy data from matching regions
                        copy_shape = [min(noise_shape[i], latent_shape[i]) for i in range(latent_ndim)]
                        copy_slices = tuple(slice(0, s) for s in copy_shape)
                        
                        try:
                            adjusted_noise[copy_slices] = noise_pred[copy_slices]
                            logging.info(f"成功复制共同区域数据，共同形状: {copy_shape}")
                        except Exception as e:
                            logging.error(f"复制共同区域数据失败: {str(e)}")
                    
                    # Ensure the tensor has non-zero values
                    if adjusted_noise.abs().max() < 1e-6:
                        logging.warning("调整后的张量值全为零，添加随机噪声")
                        adjusted_noise += torch.randn_like(adjusted_noise) * 0.1
                    
                    # Use the adjusted tensor
                    noise_pred = adjusted_noise
                    """
        
        # Apply the replacement
        updated_content = re.sub(shape_mismatch_pattern, 
                                improved_shape_mismatch + r"\4", 
                                content, 
                                flags=re.DOTALL)
        
        # Improve tensor initialization at the beginning of generate method
        init_pattern = r"(noise = \[.*?torch\.randn\(.*?\)\s*\])"
        improved_init = r"\1\n\n        # Ensure non-zero initialization\n        # Add additional initialization verification\n        for i in range(len(noise)):\n            if noise[i].abs().max() < 1e-6:\n                logging.warning(f\"Initial noise tensor {i} has very small values, adding additional noise\")\n                noise[i] += torch.randn_like(noise[i]) * 0.1\n        logging.info(f\"Initial noise range: [{noise[0].min().item():.4f}, {noise[0].max().item():.4f}]\")"
        
        updated_content = re.sub(init_pattern, improved_init, updated_content, flags=re.DOTALL)
        
        # Add improved decoding code
        decode_pattern = r"(videos = self\.vae\.decode\(x0_tensor\))"
        improved_decode = r"\1\n                    # Verify tensor after VAE decoding\n                    if videos is not None:\n                        # Analyze tensor values to ensure non-zero output\n                        if hasattr(videos, 'shape'):\n                            video_min = videos.min().item() if hasattr(videos, 'min') else 0\n                            video_max = videos.max().item() if hasattr(videos, 'max') else 0\n                            logging.info(f\"VAE decoded video range: [{video_min:.4f}, {video_max:.4f}]\")\n                            \n                            # Add noise if values are too uniform\n                            if abs(video_max - video_min) < 1e-4:\n                                logging.warning(\"VAE decoded video values are too uniform, adding noise\")\n                                videos = videos + torch.randn_like(videos) * 0.1"
        
        updated_content = re.sub(decode_pattern, improved_decode, updated_content, flags=re.DOTALL)
        
        # Write the updated content
        with open(text2video_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"Successfully updated {text2video_path}")
        return True
    
    except Exception as e:
        logger.error(f"Failed to fix {text2video_path}: {str(e)}")
        return False

def fix_generate_py():
    """Fix the generate.py file to ensure sufficient frames and proper validation"""
    generate_path = "generate.py"
    
    if not os.path.exists(generate_path):
        logger.error(f"File not found: {generate_path}")
        return False
    
    logger.info(f"Fixing {generate_path}...")
    
    # Create backup
    if not backup_file(generate_path):
        return False
    
    try:
        with open(generate_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if the file has already been fixed
        if "# Enhanced frame count validation" in content:
            logger.info(f"File {generate_path} already contains fixes")
            return True
        
        # Add improved frame count validation
        frame_validation_pattern = r"(if args\.frame_num is None:.*?args\.frame_num = 1 if \"t2i\" in args\.task else 81\s*\n)(.*?)(if \"t2i\" in args\.task:)"
        improved_frame_validation = r"\1    else:\n        # Enhanced frame count validation\n        if \"t2i\" not in args.task and args.frame_num < 32:\n            logging.warning(f\"帧数过少 ({args.frame_num})，自动增加到32帧以避免黑屏问题\")\n            args.frame_num = 32\n\n        # Ensure frame count is suitable for diffusion models (4n+1)\n        if args.frame_num % 4 != 1 and \"t2i\" not in args.task:\n            old_frame_num = args.frame_num\n            args.frame_num = ((args.frame_num - 1) // 4) * 4 + 1\n            if args.frame_num < 32:\n                args.frame_num = 33  # Smallest valid value >= 32\n            logging.info(f\"调整帧数从 {old_frame_num} 到 {args.frame_num} (4n+1格式)\")\n\n\3"
        
        # Apply the replacement
        updated_content = re.sub(frame_validation_pattern, improved_frame_validation, content, flags=re.DOTALL)
        
        # Add video tensor preprocessing before saving
        video_preprocessing_pattern = r"(if \"t2v\" in args\.task:.*?video = wan_t2v\.generate\(.*?\))(.*?)(if rank == 0.*?)"
        improved_preprocessing = r"\1\n\n            # Add preprocessing for video tensors\n            if rank == 0 and video is not None:\n                # Ensure we have meaningful video content by checking tensor values\n                if isinstance(video, torch.Tensor):\n                    video_min = float(video.min()) if hasattr(video, 'min') else 0\n                    video_max = float(video.max()) if hasattr(video, 'max') else 0\n                    logging.info(f\"Raw video tensor range: [{video_min}, {video_max}]\")\n                    \n                    # If values are all the same or very close, add noise\n                    if abs(video_max - video_min) < 1e-4:\n                        logging.warning(\"Video tensor has uniform values, adding noise to prevent black screen\")\n                        video = video + torch.randn_like(video) * 0.1\n                        logging.info(f\"After adding noise: [{float(video.min())}, {float(video.max())}]\")\n\n\2\3"
        
        updated_content = re.sub(video_preprocessing_pattern, improved_preprocessing, updated_content, flags=re.DOTALL)
        
        # Write the updated content
        with open(generate_path, 'w', encoding='utf-8') as f:
            f.write(updated_content)
        
        logger.info(f"Successfully updated {generate_path}")
        return True
    
    except Exception as e:
        logger.error(f"Failed to fix {generate_path}: {str(e)}")
        return False

def ensure_directories():
    """Ensure required directories exist"""
    directories = ["logs"]
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        logger.info(f"Ensured directory exists: {directory}")
    return True

def main():
    """Main function to apply all fixes"""
    import time
    
    # Start with banner
    logger.info("=" * 80)
    logger.info("Wan2.1 Black Video Generation Fix")
    logger.info("=" * 80)
    
    # Ensure required directories exist
    ensure_directories()
    
    # Apply fixes
    fixes = [
        ("utils.py", fix_utils_py),
        ("text2video.py", fix_text2video_py),
        ("generate.py", fix_generate_py)
    ]
    
    success_count = 0
    for name, fix_func in fixes:
        logger.info(f"Applying fix for {name}...")
        try:
            if fix_func():
                logger.info(f"Successfully applied fix for {name}")
                success_count += 1
            else:
                logger.error(f"Failed to apply fix for {name}")
        except Exception as e:
            logger.error(f"Error applying fix for {name}: {str(e)}")
    
    # Summary
    logger.info("=" * 80)
    logger.info(f"Fix completed: {success_count}/{len(fixes)} files updated successfully")
    
    if success_count == len(fixes):
        logger.info("All fixes were applied successfully!")
        logger.info("You can now generate videos without black screen issues.")
        logger.info("Recommended test command:")
        logger.info("python generate.py --task t2v-14B --prompt \"A cute dog running on grass, high definition, detailed, 4K, smooth motion\" --size 1280*720 --ckpt_dir ./Wan2.1-T2V-14B --frame_num 33 --sample_steps 50 --save_file output_videos/test_fixed_dog.mp4")
    else:
        logger.warning(f"Some fixes failed. Check the log for details.")
    
    logger.info("=" * 80)
    return success_count == len(fixes)

if __name__ == "__main__":
    import time
    main() 