"""
视频生成任务模块
包含文本转视频、图片转视频等异步任务
"""

import os
import sys
import time
import logging
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

from app.core.celery_unified import celery_app
from app.tasks.base import BaseTask, TaskProgressTracker, task_with_progress

logger = logging.getLogger(__name__)

# Wanx 2.1 模型路径
# 当前文件: backend/app/tasks/video_generation.py
# 目标路径: backend/storage/models/video_generation/wan/Wan2.1
WAN_MODEL_PATH = Path(__file__).parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
WAN_OUTPUT_DIR = Path(__file__).parent.parent.parent / "storage" / "videos"
WAN_SCRIPT_PATH = WAN_MODEL_PATH / "generate.py"

@celery_app.task(bind=True, base=BaseTask, name="generate_text_to_video")
@task_with_progress("文本转视频")
def generate_text_to_video(self, task_id: str, tracker: TaskProgressTracker,
                          prompt: str, **kwargs):
    """
    文本转视频任务 - 使用真实的Wanx 2.1模型

    Args:
        task_id: 任务ID
        tracker: 进度跟踪器
        prompt: 文本提示
        **kwargs: 其他参数
    """

    try:
        logger.info(f"开始真实视频生成任务: {task_id}")
        logger.info(f"提示词: {prompt}")

        tracker.update(5, "初始化视频生成模型...")

        # 提取参数
        model = kwargs.get('model', 't2v-1.3B')
        duration = kwargs.get('duration', 10)
        resolution = kwargs.get('resolution', '1280x720')
        fps = kwargs.get('fps', 24)
        guidance_scale = kwargs.get('guidance_scale', 7.5)
        num_inference_steps = kwargs.get('num_inference_steps', 50)

        # 解析分辨率
        if 'x' in resolution:
            width, height = map(int, resolution.split('x'))
        else:
            # 默认分辨率映射
            resolution_map = {
                '480p': (854, 480),
                '720p': (1280, 720),
                '1080p': (1920, 1080)
            }
            width, height = resolution_map.get(resolution, (1280, 720))

        # 计算帧数
        num_frames = min(max(int(duration * fps / 8), 16), 240)  # Wanx限制：16-240帧

        logger.info(f"视频参数: {width}x{height}, {num_frames}帧, {fps}fps")

        tracker.update(10, "检查模型文件...")

        # 检查Wanx模型是否存在
        if not WAN_SCRIPT_PATH.exists():
            logger.error(f"Wanx生成脚本不存在: {WAN_SCRIPT_PATH}")
            # 使用模拟模式
            return generate_mock_video(task_id, prompt, tracker, **kwargs)

        # 创建输出目录
        WAN_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"wanx_t2v_{task_id}_{timestamp}.mp4"
        output_path = WAN_OUTPUT_DIR / output_filename

        tracker.update(15, "启动Wanx 2.1模型...")

        # 检查GPU可用性
        try:
            gpu_check = subprocess.run(["nvidia-smi"], capture_output=True, text=True, timeout=10)
            if gpu_check.returncode != 0:
                logger.warning("GPU不可用，将使用CPU模式")
                device = "cpu"
            else:
                device = "cuda"
                logger.info("GPU可用，使用CUDA加速")
        except Exception as e:
            logger.warning(f"GPU检查失败: {e}，使用CPU模式")
            device = "cpu"

        # 构建Wanx命令 - 使用官方推荐的参数格式
        # 官方示例：python generate.py --task t2v-1.3B --size 832*480 --ckpt_dir ./Wan2.1-T2V-1.3B --offload_model True --t5_cpu --sample_shift 8 --sample_guide_scale 6

        # 1.3B模型官方推荐配置
        if "1.3B" in model:
            # 官方推荐分辨率：832*480 或 480*832
            if width > height:
                size = "832*480"  # 横屏
            else:
                size = "480*832"  # 竖屏

            # 官方推荐参数
            guidance_scale = 6.0  # 官方推荐6
            sample_shift = 8      # 官方推荐8-12
            num_frames = min(num_frames, 17)  # 4n+1格式，最大17帧
            ckpt_dir = WAN_MODEL_PATH / "Wan2.1-T2V-1.3B"

            logger.info(f"1.3B模型使用官方推荐配置：{size}, {num_frames}帧, guide_scale={guidance_scale}")
        else:
            # 14B模型配置
            size = f"{width}*{height}"
            sample_shift = 8
            ckpt_dir = WAN_MODEL_PATH / "Wan2.1-T2V-14B"

        # 使用相对路径，因为Wanx脚本期望相对路径
        relative_ckpt_dir = f"./Wan2.1-T2V-1.3B" if "1.3B" in model else f"./Wan2.1-T2V-14B"
        relative_output_path = f"./{output_path.name}"

        cmd = [
            sys.executable,
            "generate.py",  # 使用相对路径
            "--task", model,  # t2v-1.3B 或 t2v-14B
            "--size", size,   # 官方格式：832*480
            "--ckpt_dir", relative_ckpt_dir,  # 使用相对路径
            "--prompt", prompt,
            "--offload_model", "True",  # 关键：模型卸载，防止OOM
            "--t5_cpu",  # 关键：T5模型使用CPU，节省GPU内存
            "--sample_shift", str(sample_shift),  # 官方推荐8
            "--sample_guide_scale", str(guidance_scale),  # 官方推荐6
            "--sample_steps", str(min(num_inference_steps, 30)),  # 限制步数
            "--frame_num", str(num_frames),  # 帧数
            "--base_seed", str(kwargs.get('seed', int(time.time()) % 10000)),
            "--save_file", relative_output_path  # 使用相对路径
        ]

        logger.info(f"使用官方Wanx 2.1参数：{model}, {size}, {num_frames}帧")

        logger.info(f"执行Wanx命令: {' '.join(cmd)}")

        tracker.update(20, "选择最佳视频生成引擎...")

        # 使用智能视频生成管理器
        try:
            from .video_generation_manager import generate_video_smart

            tracker.update(25, "开始智能视频生成...")

            success, message, result_path = generate_video_smart(
                prompt=prompt,
                output_path=output_path,
                width=width,
                height=height,
                duration=duration,
                fps=fps,
                guidance_scale=guidance_scale,
                num_inference_steps=num_inference_steps,
                timeout=60  # 1分钟快速超时
            )

            if success and result_path and result_path.exists():
                tracker.update(95, "保存视频文件...")

                # 生成相对URL
                video_url = f"/storage/videos/{output_filename}"

                tracker.complete("智能视频生成完成")

                elapsed_time = time.time() - start_time
                logger.info(f"智能视频生成成功: {output_path} (耗时: {elapsed_time:.1f}秒)")

                return {
                    "success": True,
                    "task_id": task_id,
                    "video_url": video_url,
                    "output_path": str(output_path),
                    "duration": elapsed_time,
                    "parameters": {
                        "prompt": prompt,
                        "model": model,
                        "resolution": f"{width}x{height}",
                        "frames": num_frames,
                        "fps": fps,
                        "guidance_scale": guidance_scale,
                        "steps": num_inference_steps
                    }
                }
            else:
                logger.error(f"智能视频生成失败: {message}")
                # 回退到模拟模式
                return generate_mock_video(task_id, prompt, tracker, **kwargs)

        except Exception as e:
            logger.error(f"智能视频生成异常: {e}")
            # 回退到模拟模式
            return generate_mock_video(task_id, prompt, tracker, **kwargs)

        # 以下是原来的代码，保留作为最后的备用
        """
        # 执行Wanx生成（在Wanx目录中运行）
        start_time = time.time()
        process = subprocess.Popen(
            cmd,
            cwd=str(WAN_MODEL_PATH),  # 在Wanx 2.1目录中运行
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        # 监控进度（添加更严格的超时机制）
        progress = 20
        max_wait_time = 120  # 最大等待2分钟（1.3B模型应该很快）
        last_progress_time = start_time
        stall_timeout = 20   # 20秒无响应就认为卡死

        while process.poll() is None:
            current_time = time.time()
            elapsed = current_time - start_time

            # 检查超时
            if elapsed > max_wait_time:
                logger.error(f"Wanx生成超时 ({max_wait_time}秒)，终止进程")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                raise Exception(f"视频生成超时 ({max_wait_time}秒)")

            # 检查进度停滞（更严格）
            if current_time - last_progress_time > stall_timeout:
                logger.warning(f"进度停滞超过{stall_timeout}秒，当前进度: {progress}%")
                # 任何阶段停滞超过20秒都终止
                logger.error(f"进度停滞过久，终止进程（进度: {progress}%, 耗时: {elapsed:.1f}秒）")
                process.terminate()
                time.sleep(2)
                if process.poll() is None:
                    process.kill()
                raise Exception(f"视频生成卡死（进度: {progress}%）")

            # 更新进度
            if progress < 90:
                progress += 3  # 减慢进度更新
                tracker.update(progress, f"生成中... ({elapsed:.0f}秒)")
                last_progress_time = current_time
            elif progress < 95:
                # 在90-95%之间更慢更新
                if elapsed % 10 == 0:  # 每10秒更新一次
                    progress += 1
                    tracker.update(progress, f"后处理中... ({elapsed:.0f}秒)")
                    last_progress_time = current_time

            time.sleep(3)  # 增加检查间隔

        # 获取输出
        try:
            stdout, stderr = process.communicate(timeout=30)
        except subprocess.TimeoutExpired:
            logger.error("获取进程输出超时")
            process.kill()
            stdout, stderr = process.communicate()
            raise Exception("获取进程输出超时")

        if process.returncode != 0:
            logger.error(f"Wanx生成失败 (返回码: {process.returncode})")
            logger.error(f"错误输出: {stderr}")
            logger.info(f"标准输出: {stdout}")

            # 清理可能的临时文件
            try:
                if output_path.exists():
                    output_path.unlink()
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")

            # 检查是否是内存不足错误
            if "out of memory" in stderr.lower() or "cuda out of memory" in stderr.lower():
                logger.error("GPU内存不足，建议降低分辨率或时长")
                tracker.update(50, "GPU内存不足，回退到模拟模式...")
            elif "no module named" in stderr.lower() or "import error" in stderr.lower():
                logger.error("模型依赖缺失")
                tracker.update(50, "模型依赖缺失，回退到模拟模式...")
            else:
                tracker.update(50, "生成失败，回退到模拟模式...")

            # 回退到模拟模式
            return generate_mock_video(task_id, prompt, tracker, **kwargs)

        # 检查输出文件（在Wanx目录中）
        temp_output_path = WAN_MODEL_PATH / relative_output_path.lstrip('./')
        if temp_output_path.exists() and temp_output_path.stat().st_size > 0:
            tracker.update(95, "保存视频文件...")

            # 移动文件到最终输出目录
            import shutil
            WAN_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
            shutil.move(str(temp_output_path), str(output_path))

            # 生成相对URL
            video_url = f"/storage/videos/{output_filename}"

            tracker.complete("视频生成完成")

            elapsed_time = time.time() - start_time
            logger.info(f"视频生成成功: {output_path} (耗时: {elapsed_time:.1f}秒)")

            return {
                "success": True,
                "task_id": task_id,
                "video_url": video_url,
                "output_path": str(output_path),
                "duration": elapsed_time,
                "parameters": {
                    "prompt": prompt,
                    "model": model,
                    "resolution": f"{width}x{height}",
                    "frames": num_frames,
                    "fps": fps,
                    "guidance_scale": guidance_scale,
                    "steps": num_inference_steps
                }
            }
        else:
            logger.error(f"输出文件不存在或为空: {output_path}")
            # 回退到模拟模式
            return generate_mock_video(task_id, prompt, tracker, **kwargs)

    except Exception as e:
        logger.error(f"视频生成异常: {e}")
        # 回退到模拟模式
        return generate_mock_video(task_id, prompt, tracker, **kwargs)

def generate_mock_video(task_id: str, prompt: str, tracker: TaskProgressTracker, **kwargs):
    """
    模拟视频生成（当真实模型不可用时）
    """
    logger.info(f"使用模拟模式生成视频: {task_id}")

    tracker.update(30, "模拟视频生成中...")
    time.sleep(2)  # 模拟处理时间

    tracker.update(60, "渲染视频帧...")
    time.sleep(3)

    tracker.update(90, "编码视频文件...")
    time.sleep(2)

    # 创建模拟输出文件
    WAN_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"mock_video_{task_id}_{timestamp}.mp4"
    output_path = WAN_OUTPUT_DIR / output_filename

    # 创建占位符文件
    with open(output_path, 'w') as f:
        f.write(f"# Mock Video File\n")
        f.write(f"# Task ID: {task_id}\n")
        f.write(f"# Prompt: {prompt}\n")
        f.write(f"# Generated: {datetime.now()}\n")

    video_url = f"/storage/videos/{output_filename}"

    tracker.complete("模拟视频生成完成")

    return {
        "success": True,
        "task_id": task_id,
        "video_url": video_url,
        "mock_mode": True,
        "message": "使用模拟模式生成（真实模型不可用）"
    }
