"""
视频生成任务模块
包含文本转视频、图片转视频等异步任务
"""

import os
import sys
import time
import logging
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, Any

from app.core.celery_unified import celery_app
from app.tasks.base import BaseTask, TaskProgressTracker, task_with_progress

logger = logging.getLogger(__name__)

# Wanx 2.1 模型路径
WAN_MODEL_PATH = Path(__file__).parent.parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
WAN_OUTPUT_DIR = Path(__file__).parent.parent.parent.parent / "storage" / "videos"
WAN_SCRIPT_PATH = WAN_MODEL_PATH / "generate.py"

@celery_app.task(bind=True, base=BaseTask, name="generate_text_to_video")
@task_with_progress("文本转视频")
def generate_text_to_video(self, task_id: str, tracker: TaskProgressTracker,
                          prompt: str, **kwargs):
    """
    文本转视频任务 - 使用真实的Wanx 2.1模型

    Args:
        task_id: 任务ID
        tracker: 进度跟踪器
        prompt: 文本提示
        **kwargs: 其他参数
    """

    try:
        logger.info(f"开始真实视频生成任务: {task_id}")
        logger.info(f"提示词: {prompt}")

        tracker.update(5, "初始化视频生成模型...")

        # 提取参数
        model = kwargs.get('model', 't2v-1.3B')
        duration = kwargs.get('duration', 10)
        resolution = kwargs.get('resolution', '1280x720')
        fps = kwargs.get('fps', 24)
        guidance_scale = kwargs.get('guidance_scale', 7.5)
        num_inference_steps = kwargs.get('num_inference_steps', 50)

        # 解析分辨率
        if 'x' in resolution:
            width, height = map(int, resolution.split('x'))
        else:
            # 默认分辨率映射
            resolution_map = {
                '480p': (854, 480),
                '720p': (1280, 720),
                '1080p': (1920, 1080)
            }
            width, height = resolution_map.get(resolution, (1280, 720))

        # 计算帧数
        num_frames = min(max(int(duration * fps / 8), 16), 240)  # Wanx限制：16-240帧

        logger.info(f"视频参数: {width}x{height}, {num_frames}帧, {fps}fps")

        tracker.update(10, "检查模型文件...")

        # 检查Wanx模型是否存在
        if not WAN_SCRIPT_PATH.exists():
            logger.error(f"Wanx生成脚本不存在: {WAN_SCRIPT_PATH}")
            # 使用模拟模式
            return generate_mock_video(task_id, prompt, tracker, **kwargs)

        # 创建输出目录
        WAN_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)

        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"wanx_t2v_{task_id}_{timestamp}.mp4"
        output_path = WAN_OUTPUT_DIR / output_filename

        tracker.update(15, "启动Wanx 2.1模型...")

        # 构建Wanx命令
        cmd = [
            sys.executable,
            str(WAN_SCRIPT_PATH),
            "--task", f"t2v-{model.split('-')[-1]}",  # t2v-1.3B -> 1.3B
            "--size", f"{width}*{height}",
            "--prompt", prompt,
            "--frame_num", str(num_frames),
            "--sample_guide_scale", str(guidance_scale),
            "--sample_steps", str(num_inference_steps),
            "--save_file", str(output_path)
        ]

        # 添加检查点目录
        ckpt_dir = WAN_MODEL_PATH / "checkpoints"
        if ckpt_dir.exists():
            cmd.extend(["--ckpt_dir", str(ckpt_dir)])

        logger.info(f"执行Wanx命令: {' '.join(cmd)}")

        tracker.update(20, "正在生成视频...")

        # 执行Wanx生成
        start_time = time.time()
        process = subprocess.Popen(
            cmd,
            cwd=str(WAN_MODEL_PATH),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )

        # 监控进度
        progress = 20
        while process.poll() is None:
            # 模拟进度更新
            if progress < 90:
                progress += 5
                elapsed = time.time() - start_time
                tracker.update(progress, f"生成中... ({elapsed:.0f}秒)")

            time.sleep(2)

        # 获取输出
        stdout, stderr = process.communicate()

        if process.returncode != 0:
            logger.error(f"Wanx生成失败: {stderr}")
            # 回退到模拟模式
            return generate_mock_video(task_id, prompt, tracker, **kwargs)

        # 检查输出文件
        if output_path.exists() and output_path.stat().st_size > 0:
            tracker.update(95, "保存视频文件...")

            # 生成相对URL
            video_url = f"/storage/videos/{output_filename}"

            tracker.complete("视频生成完成")

            elapsed_time = time.time() - start_time
            logger.info(f"视频生成成功: {output_path} (耗时: {elapsed_time:.1f}秒)")

            return {
                "success": True,
                "task_id": task_id,
                "video_url": video_url,
                "output_path": str(output_path),
                "duration": elapsed_time,
                "parameters": {
                    "prompt": prompt,
                    "model": model,
                    "resolution": f"{width}x{height}",
                    "frames": num_frames,
                    "fps": fps,
                    "guidance_scale": guidance_scale,
                    "steps": num_inference_steps
                }
            }
        else:
            logger.error(f"输出文件不存在或为空: {output_path}")
            # 回退到模拟模式
            return generate_mock_video(task_id, prompt, tracker, **kwargs)

    except Exception as e:
        logger.error(f"视频生成异常: {e}")
        # 回退到模拟模式
        return generate_mock_video(task_id, prompt, tracker, **kwargs)

def generate_mock_video(task_id: str, prompt: str, tracker: TaskProgressTracker, **kwargs):
    """
    模拟视频生成（当真实模型不可用时）
    """
    logger.info(f"使用模拟模式生成视频: {task_id}")

    tracker.update(30, "模拟视频生成中...")
    time.sleep(2)  # 模拟处理时间

    tracker.update(60, "渲染视频帧...")
    time.sleep(3)

    tracker.update(90, "编码视频文件...")
    time.sleep(2)

    # 创建模拟输出文件
    WAN_OUTPUT_DIR.mkdir(parents=True, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_filename = f"mock_video_{task_id}_{timestamp}.mp4"
    output_path = WAN_OUTPUT_DIR / output_filename

    # 创建占位符文件
    with open(output_path, 'w') as f:
        f.write(f"# Mock Video File\n")
        f.write(f"# Task ID: {task_id}\n")
        f.write(f"# Prompt: {prompt}\n")
        f.write(f"# Generated: {datetime.now()}\n")

    video_url = f"/storage/videos/{output_filename}"

    tracker.complete("模拟视频生成完成")

    return {
        "success": True,
        "task_id": task_id,
        "video_url": video_url,
        "mock_mode": True,
        "message": "使用模拟模式生成（真实模型不可用）"
    }
