<template>
  <div class="agent-platform">
    <!-- 专业的英雄区域 -->
    <div class="platform-hero">
      <div class="hero-background">
        <div class="hero-particles"></div>
        <div class="hero-gradient"></div>
      </div>

      <div class="hero-content">
        <div class="hero-badge">
          <span class="badge-icon">🚀</span>
          <span class="badge-text">真正的智能体平台</span>
        </div>

        <h1 class="hero-title">
          构建下一代
          <span class="title-highlight">AI智能体</span>
        </h1>

        <p class="hero-description">
          不只是聊天机器人，而是具备工具调用、知识库、记忆系统的真正智能体。
          <br>让AI真正为您执行任务，而不仅仅是回答问题。
        </p>

        <div class="hero-actions">
          <el-dropdown @command="handleCreateCommand" trigger="click">
            <el-button type="primary" size="large" class="primary-action">
              <el-icon><plus /></el-icon>
              创建智能体
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="new">
                  <span class="dropdown-icon">🧙‍♂️</span>
                  向导模式（推荐）
                </el-dropdown-item>
                <el-dropdown-item command="advanced">
                  <span class="dropdown-icon">⚙️</span>
                  高级模式
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>

          <el-button size="large" class="secondary-action" @click="scrollToAgents">
            <el-icon><view /></el-icon>
            浏览智能体
          </el-button>
        </div>

        <div class="hero-stats">
          <div class="stat-item">
            <div class="stat-number">{{ totalAgents }}</div>
            <div class="stat-label">智能体</div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <div class="stat-number">7</div>
            <div class="stat-label">内置工具</div>
          </div>
          <div class="stat-divider"></div>
          <div class="stat-item">
            <div class="stat-number">5</div>
            <div class="stat-label">记忆类型</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 核心特性展示 -->
    <div class="features-section">
      <div class="container">
        <div class="section-header">
          <h2 class="section-title">真正智能体的核心能力</h2>
          <p class="section-subtitle">超越传统聊天机器人，具备真正的任务执行能力</p>
        </div>

        <div class="features-grid">
          <div class="feature-card">
            <div class="feature-icon">🛠️</div>
            <h3 class="feature-title">工具调用</h3>
            <p class="feature-desc">网络搜索、代码执行、数据分析、图像处理等7种内置工具</p>
            <div class="feature-tags">
              <span class="tag">搜索</span>
              <span class="tag">编程</span>
              <span class="tag">分析</span>
            </div>
          </div>

          <div class="feature-card">
            <div class="feature-icon">📚</div>
            <h3 class="feature-title">知识库</h3>
            <p class="feature-desc">上传文档、构建专业知识库，让智能体学习专业知识</p>
            <div class="feature-tags">
              <span class="tag">文档</span>
              <span class="tag">向量化</span>
              <span class="tag">检索</span>
            </div>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🧠</div>
            <h3 class="feature-title">记忆系统</h3>
            <p class="feature-desc">短期、长期、情节、语义、程序记忆，智能管理对话历史</p>
            <div class="feature-tags">
              <span class="tag">短期</span>
              <span class="tag">长期</span>
              <span class="tag">情节</span>
            </div>
          </div>

          <div class="feature-card">
            <div class="feature-icon">🔄</div>
            <h3 class="feature-title">工作流</h3>
            <p class="feature-desc">复杂任务编排，多步骤自动化执行</p>
            <div class="feature-tags">
              <span class="tag">编排</span>
              <span class="tag">自动化</span>
              <span class="tag">流程</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 智能体展示区域 -->
    <div class="agents-section" id="agents-section">
      <div class="container">
        <!-- 搜索和筛选 -->
        <div class="agents-header">
          <div class="header-left">
            <h2 class="agents-title">智能体市场</h2>
            <p class="agents-subtitle">发现和使用专业的AI智能体</p>
          </div>

          <div class="header-actions">
            <div class="search-box">
              <el-input
                v-model="searchQuery"
                placeholder="搜索智能体..."
                class="search-input"
                @keyup.enter="handleSearch"
              >
                <template #prefix><el-icon><search /></el-icon></template>
              </el-input>
            </div>

            <el-select
              v-model="currentCategory"
              placeholder="分类"
              class="category-filter"
              @change="handleCategoryChange"
            >
              <el-option value="" label="全部分类" />
              <el-option value="research_assistant" label="研究助手" />
              <el-option value="coding_assistant" label="编程助手" />
              <el-option value="data_analyst" label="数据分析" />
              <el-option value="multimedia_assistant" label="多媒体" />
              <el-option value="language_tutor" label="语言学习" />
            </el-select>

            <el-select
              v-model="sortBy"
              class="sort-filter"
              @change="handleSortChange"
            >
              <el-option value="newest" label="最新" />
              <el-option value="popular" label="热门" />
              <el-option value="rating" label="评分" />
            </el-select>
          </div>
        </div>

        <!-- 快速开始 - 简化版 -->
        <div class="quick-start-section">
          <div class="quick-start-card">
            <div class="quick-start-content">
              <div class="quick-start-icon">⚡</div>
              <div class="quick-start-text">
                <h3>快速开始</h3>
                <p>使用模板快速创建您的第一个智能体</p>
              </div>
            </div>
            <el-button type="primary" @click="navigateToStudio">
              立即创建
            </el-button>
          </div>
        </div>
        <!-- 智能体网格 -->
        <div class="agents-grid-container">
          <div v-if="loading" class="loading-state" v-loading="loading">
            <p>加载智能体中...</p>
          </div>

          <div v-else-if="agents && agents.length > 0" class="agents-grid">
            <div
              v-for="agent in agents"
              :key="agent.id"
              class="agent-card"
            >
              <div class="agent-card-header">
                <div class="agent-avatar">
                  <div class="avatar-icon">{{ getAgentIcon(agent.agent_type) }}</div>
                </div>
                <div class="agent-badge" v-if="agent.featured">
                  <span>推荐</span>
                </div>
              </div>

              <div class="agent-card-content" @click="goToAgentDetail(agent)">
                <h3 class="agent-name">{{ agent.name }}</h3>
                <p class="agent-description">{{ agent.description }}</p>

                <div class="agent-capabilities">
                  <div class="capability-item" v-if="agent.tools && agent.tools.length > 0">
                    <span class="capability-icon">🛠️</span>
                    <span class="capability-text">{{ agent.tools.length }}个工具</span>
                  </div>
                  <div class="capability-item" v-if="agent.memory_types && agent.memory_types.length > 0">
                    <span class="capability-icon">🧠</span>
                    <span class="capability-text">{{ agent.memory_types.length }}种记忆</span>
                  </div>
                  <div class="capability-item" v-if="agent.workflow_enabled">
                    <span class="capability-icon">🔄</span>
                    <span class="capability-text">工作流</span>
                  </div>
                </div>

                <div class="agent-stats">
                  <div class="stat">
                    <span class="stat-value">{{ agent.usage_count || 0 }}</span>
                    <span class="stat-label">使用次数</span>
                  </div>
                  <div class="stat">
                    <span class="stat-value">{{ agent.success_rate ? (agent.success_rate * 100).toFixed(0) : 85 }}%</span>
                    <span class="stat-label">成功率</span>
                  </div>
                </div>
              </div>

              <div class="agent-card-footer" @click.stop>
                <div class="agent-type">
                  <span class="type-tag">{{ getAgentTypeName(agent.agent_type) }}</span>
                </div>
                <div class="agent-actions">
                  <el-button type="primary" size="small" @click.stop="chatWithAgent(agent)">
                    💬 对话
                  </el-button>
                  <el-button size="small" @click.stop="toggleFavorite(agent.id)" :class="{ 'favorited': agent.is_favorite }">
                    <el-icon>
                      <star-filled v-if="agent.is_favorite" style="color: #ff4d4f;" />
                      <star v-else />
                    </el-icon>
                  </el-button>
                  <el-dropdown @command="handleAgentAction" trigger="click">
                    <el-button size="small">
                      更多 <el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="{action: 'digitalHuman', agent}">🎭 数字人对话</el-dropdown-item>
                        <el-dropdown-item :command="{action: 'edit', agent}">✏️ 编辑智能体</el-dropdown-item>
                        <el-dropdown-item :command="{action: 'knowledge', agent}">📚 管理知识库</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>
            </div>
          </div>

          <div v-else class="empty-state">
            <div class="empty-icon">🤖</div>
            <h3>暂无智能体</h3>
            <p>{{ currentCategory ? '该分类下暂无智能体' : '还没有智能体，快来创建第一个吧！' }}</p>
            <el-button type="primary" @click="navigateToStudio">
              创建智能体
            </el-button>
          </div>
        </div>

        <!-- 分页 -->
        <div v-if="total > pageSize" class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            :total="total"
            :page-size="pageSize"
            layout="prev, pager, next, jumper"
            @current-change="handlePageChange"
          />
        </div>
      </div>
    </div>

    
  </div>
</template>

<script>
import { defineComponent, ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import {
  Plus,
  User,
  Search,
  View,
  Star,
  StarFilled,
  Edit,
  Document,
  ArrowDown
} from '@element-plus/icons-vue';
import trueAgentService from '../services/trueAgentService.js';

export default defineComponent({
  name: 'AgentPlatform',
  components: {
    Plus,
    User,
    Search,
    View,
    Star,
    StarFilled,
    Edit,
    Document,
    ArrowDown
  },
  setup() {
    const router = useRouter();

    // 响应式数据
    const searchQuery = ref('');
    const currentCategory = ref('');
    const sortBy = ref('newest');
    const currentPage = ref(1);
    const pageSize = ref(12);
    const total = ref(0);
    const totalAgents = ref(0);
    const agents = ref([]);
    const loading = ref(true);

    // 加载智能体数据
    const loadAgents = async () => {
      try {
        loading.value = true;

        const params = {
          page: currentPage.value,
          size: pageSize.value,
          agent_type: currentCategory.value || undefined
        };

        const response = await trueAgentService.getAgents(params);

        if (response.success) {
          agents.value = response.agents || [];
          if (response.pagination) {
            total.value = response.pagination.total;
            totalAgents.value = response.pagination.total;
          } else {
            total.value = response.agents ? response.agents.length : 0;
            totalAgents.value = response.agents ? response.agents.length : 0;
          }
          console.log('加载智能体成功:', response);
          console.log('智能体数量:', agents.value.length, '总数:', total.value);
        } else {
          console.warn('加载智能体失败:', response);
          // 使用本地存储的数据作为备用
          loadLocalAgents();
        }
      } catch (error) {
        console.error('加载智能体出错:', error);
        // 使用本地存储的数据作为备用
        loadLocalAgents();
      } finally {
        loading.value = false;
      }
    };

    // 加载本地智能体数据（备用方案）
    const loadLocalAgents = () => {
      try {
        const localAgents = JSON.parse(localStorage.getItem('ai_agents') || '[]');
        agents.value = localAgents;
        total.value = localAgents.length;
        totalAgents.value = localAgents.length;
        console.log('使用本地智能体数据:', localAgents);
      } catch (error) {
        console.error('加载本地智能体数据失败:', error);
        agents.value = [];
        total.value = 0;
        totalAgents.value = 0;
      }
    };

    // 工具方法
    const getAgentIcon = (agentType) => {
      const iconMap = {
        'research_assistant': '🔍',
        'coding_assistant': '💻',
        'data_analyst': '📊',
        'multimedia_assistant': '🖼️',
        'language_tutor': '🌐',
        'general': '🤖'
      };
      return iconMap[agentType] || '🤖';
    };

    const getAgentTypeName = (agentType) => {
      const nameMap = {
        'research_assistant': '研究助手',
        'coding_assistant': '编程助手',
        'data_analyst': '数据分析',
        'multimedia_assistant': '多媒体',
        'language_tutor': '语言学习',
        'general': '通用'
      };
      return nameMap[agentType] || '通用';
    };

    // 导航方法
    const navigateToStudio = () => {
      router.push('/agents/studio-new');
    };

    const handleCreateCommand = (command) => {
      if (command === 'new') {
        router.push('/agents/studio-new');
      } else if (command === 'advanced') {
        router.push('/agents/studio');
      }
    };

    const scrollToAgents = () => {
      const element = document.getElementById('agents-section');
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    };

    const goToAgentDetail = (agent) => {
      console.log('查看智能体详情:', agent);
      // 可以导航到详情页或打开对话
      chatWithAgent(agent);
    };

    const chatWithAgent = (agent) => {
      const chatUrl = `/agents/true-chat?agent_id=${agent.id}`;
      router.push(chatUrl);
    };

    const chatWithDigitalHuman = (agent) => {
      const chatUrl = `/digital-human/chat?agent_id=${agent.id}`;
      router.push(chatUrl);
    };

    // 处理下拉菜单操作
    const handleAgentAction = (command) => {
      const { action, agent } = command;
      switch (action) {
        case 'digitalHuman':
          chatWithDigitalHuman(agent);
          break;
        case 'edit':
          editAgent(agent);
          break;
        case 'knowledge':
          manageKnowledge(agent);
          break;
        default:
          console.log('未知操作:', action);
      }
    };

    // 搜索和筛选方法
    const handleSearch = () => {
      currentPage.value = 1;
      loadAgents();
    };

    const handleCategoryChange = () => {
      currentPage.value = 1;
      loadAgents();
    };

    const handleSortChange = () => {
      currentPage.value = 1;
      loadAgents();
    };

    const handlePageChange = (page) => {
      currentPage.value = page;
      loadAgents();
    };

    // 其他操作方法
    const toggleFavorite = async (agentId) => {
      try {
        // 这里可以调用API来切换收藏状态
        const agent = agents.value.find(a => a.id === agentId);
        if (agent) {
          agent.is_favorite = !agent.is_favorite;
          ElMessage.success(agent.is_favorite ? '已添加到收藏' : '已取消收藏');
        }
      } catch (error) {
        console.error('切换收藏状态失败:', error);
        ElMessage.error('操作失败');
      }
    };

    // 编辑智能体
    const editAgent = (agent) => {
      // 跳转到智能体编辑页面
      router.push({
        path: '/agents/studio',
        query: {
          mode: 'edit',
          agent_id: agent.id
        }
      });
    };

    // 管理知识库
    const manageKnowledge = (agent) => {
      // 跳转到智能体工作室的知识库标签页
      router.push({
        path: '/agents/studio',
        query: {
          mode: 'edit',
          agent_id: agent.id,
          tab: 'knowledge'  // 直接打开知识库标签页
        }
      });
    };

    // 动态获取后端 URL
    const getBackendUrl = () => {
      const { hostname, protocol } = window.location;
      if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return 'http://localhost:8000';
      } else {
        return `${protocol}//${hostname}:8000`;
      }
    };

    // 外部链接方法
    const openTutorial = () => {
      window.open(`${getBackendUrl()}/docs`, '_blank');
    };

    const openApiDocs = () => {
      window.open(`${getBackendUrl()}/docs`, '_blank');
    };

    const openExamples = () => {
      window.open('https://github.com/your-repo/examples', '_blank');
    };

    const openCommunity = () => {
      window.open('https://github.com/your-repo/community', '_blank');
    };

    const openHelp = () => {
      ElMessage.info('帮助中心即将开放');
    };

    const openFeedback = () => {
      ElMessage.info('问题反馈功能即将开放');
    };

    const openContact = () => {
      ElMessage.info('联系我们: <EMAIL>');
    };

    const openStatus = () => {
      window.open(`${getBackendUrl()}/health`, '_blank');
    };

    // 生命周期
    onMounted(() => {
      loadAgents();
    });

    return {
      // 数据
      searchQuery,
      currentCategory,
      sortBy,
      currentPage,
      pageSize,
      total,
      totalAgents,
      agents,
      loading,

      // 方法
      loadAgents,
      getAgentIcon,
      getAgentTypeName,
      navigateToStudio,
      handleCreateCommand,
      scrollToAgents,
      goToAgentDetail,
      chatWithAgent,
      chatWithDigitalHuman,
      handleAgentAction,
      handleSearch,
      handleCategoryChange,
      handleSortChange,
      handlePageChange,
      toggleFavorite,
      editAgent,
      manageKnowledge,
      openTutorial,
      openApiDocs,
      openExamples,
      openCommunity,
      openHelp,
      openFeedback,
      openContact,
      openStatus
    };
  }
});

</script>

<style scoped>
/* 专业的智能体平台样式 */
.agent-platform {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 英雄区域 */
.platform-hero {
  position: relative;
  min-height: 80vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.hero-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
  animation: float 20s infinite linear;
}

.hero-gradient {
  position: absolute;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 20px;
}

.hero-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50px;
  padding: 8px 16px;
  margin-bottom: 24px;
  font-size: 14px;
  font-weight: 500;
}

.badge-icon {
  font-size: 16px;
}

.hero-title {
  font-size: 64px;
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: 24px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.title-highlight {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-description {
  font-size: 20px;
  line-height: 1.6;
  margin-bottom: 40px;
  opacity: 0.9;
}

.hero-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 60px;
}

.primary-action {
  height: 56px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #10b981, #059669);
  border: none;
  box-shadow: 0 8px 24px rgba(16, 185, 129, 0.3);
  transition: all 0.3s ease;
}

.primary-action:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(16, 185, 129, 0.4);
}

.secondary-action {
  height: 56px;
  padding: 0 32px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
  transition: all 0.3s ease;
}

.secondary-action:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 下拉菜单样式 */
.dropdown-icon {
  margin-right: 8px;
  font-size: 16px;
}

.hero-stats {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 32px;
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 4px;
  color: white;
  text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.stat-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
  text-shadow: 0 1px 2px rgba(0,0,0,0.3);
}

.stat-divider {
  width: 1px;
  height: 40px;
  background: rgba(255, 255, 255, 0.2);
}

/* 特性展示区域 */
.features-section {
  padding: 120px 0;
  background: white;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-size: 48px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 20px;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 32px;
}

.feature-card {
  background: white;
  border-radius: 16px;
  padding: 32px;
  text-align: center;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.06);
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

.feature-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.feature-title {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
}

.feature-desc {
  font-size: 16px;
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 20px;
}

.feature-tags {
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.tag {
  background: #f3f4f6;
  color: #6b7280;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

/* 智能体展示区域 */
.agents-section {
  padding: 80px 0;
  background: #f8fafc;
}

.agents-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 40px;
  gap: 40px;
}

.header-left {
  flex: 1;
}

.agents-title {
  font-size: 36px;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
}

.agents-subtitle {
  font-size: 18px;
  color: #6b7280;
}

.header-actions {
  display: flex;
  gap: 16px;
  align-items: center;
}

.search-box {
  width: 300px;
  position: relative;
}

.search-input {
  height: 44px !important;
  border-radius: 12px !important;
  border: 1px solid #d1d5db !important;
  background: white !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.search-input .ant-input {
  height: 42px !important;
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
  padding-left: 12px !important;
}

.search-input .ant-input-affix-wrapper {
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.search-input .ant-input-prefix {
  margin-right: 8px;
  color: #9ca3af;
}

.category-filter,
.sort-filter {
  width: 140px;
  height: 44px;
}

/* 快速开始区域 */
.quick-start-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 60px;
}

.quick-start-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.quick-start-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.08);
}

.quick-start-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.quick-start-icon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f3f4f6;
  border-radius: 12px;
}

.quick-start-text h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.quick-start-text p {
  font-size: 14px;
  color: #6b7280;
  margin: 0;
}

/* 智能体网格 */
.agents-grid-container {
  min-height: 400px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 0;
  color: #6b7280;
}

.loading-state p {
  margin-top: 16px;
  font-size: 16px;
}

.agents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 24px;
}

.agent-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  border: 1px solid #e5e7eb;
  transition: all 0.3s ease;
}

.agent-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.1);
  border-color: #3b82f6;
}

.agent-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.agent-avatar {
  width: 56px;
  height: 56px;
  border-radius: 12px;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 24px;
  color: white;
}

.agent-badge {
  background: #fef3c7;
  color: #d97706;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.agent-card-content {
  margin-bottom: 20px;
  cursor: pointer;
  border-radius: 8px;
  padding: 8px;
  transition: background-color 0.2s ease;
}

.agent-card-content:hover {
  background-color: #f8fafc;
}

.agent-name {
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.agent-description {
  font-size: 14px;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 16px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.agent-capabilities {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.capability-item {
  display: flex;
  align-items: center;
  gap: 4px;
  background: #f3f4f6;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  color: #6b7280;
}

.capability-icon {
  font-size: 14px;
}

.agent-stats {
  display: flex;
  gap: 20px;
}

.stat {
  text-align: center;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
}

.agent-card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.type-tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.agent-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.agent-actions .favorited {
  background-color: #fff2f0;
  border-color: #ff4d4f;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #6b7280;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
}

.empty-state h3 {
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.empty-state p {
  font-size: 16px;
  margin-bottom: 24px;
}

/* 分页 */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 60px;
}

/* 底部信息 */
.platform-footer {
  background: #1f2937;
  color: white;
  padding: 60px 0 20px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-bottom: 40px;
}

.footer-section h4 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 16px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-section a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #374151;
  color: #9ca3af;
}

/* 动画 */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
  100% { transform: translateY(0px); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 40px;
  }

  .hero-description {
    font-size: 16px;
  }

  .hero-actions {
    flex-direction: column;
    align-items: center;
  }

  .hero-stats {
    flex-direction: column;
    gap: 16px;
  }

  .agents-header {
    flex-direction: column;
    gap: 20px;
  }

  .header-actions {
    flex-direction: column;
    width: 100%;
  }

  .search-box {
    width: 100%;
  }

  .agents-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .quick-start-section {
    grid-template-columns: 1fr;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}
</style>





