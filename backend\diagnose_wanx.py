#!/usr/bin/env python3
"""
Wanx 2.1 深度诊断工具
分析92%卡死问题的根本原因
"""

import os
import sys
import time
import subprocess
import threading
import psutil
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def monitor_process_resources(pid, duration=300):
    """监控进程资源使用情况"""
    try:
        process = psutil.Process(pid)
        start_time = time.time()
        
        logger.info(f"开始监控进程 {pid} 的资源使用情况")
        
        while time.time() - start_time < duration:
            try:
                if not process.is_running():
                    logger.info("进程已结束")
                    break
                
                # CPU和内存使用率
                cpu_percent = process.cpu_percent()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                
                # GPU使用情况（如果可用）
                gpu_info = ""
                try:
                    import GPUtil
                    gpus = GPUtil.getGPUs()
                    if gpus:
                        gpu = gpus[0]
                        gpu_info = f", GPU: {gpu.memoryUsed}MB/{gpu.memoryTotal}MB ({gpu.memoryUtil*100:.1f}%)"
                except:
                    pass
                
                logger.info(f"资源使用 - CPU: {cpu_percent:.1f}%, 内存: {memory_mb:.1f}MB{gpu_info}")
                
                # 检查进程状态
                status = process.status()
                if status in ['stopped', 'zombie']:
                    logger.warning(f"进程状态异常: {status}")
                    break
                
                time.sleep(5)  # 每5秒检查一次
                
            except psutil.NoSuchProcess:
                logger.info("进程不存在")
                break
            except Exception as e:
                logger.error(f"监控异常: {e}")
                break
                
    except Exception as e:
        logger.error(f"监控进程失败: {e}")

def run_wanx_with_monitoring(cmd, work_dir, timeout=300):
    """运行Wanx命令并监控资源使用"""
    logger.info(f"执行命令: {' '.join(cmd)}")
    logger.info(f"工作目录: {work_dir}")
    logger.info(f"超时设置: {timeout}秒")
    
    try:
        # 启动进程
        process = subprocess.Popen(
            cmd,
            cwd=work_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        logger.info(f"进程已启动，PID: {process.pid}")
        
        # 启动资源监控线程
        monitor_thread = threading.Thread(
            target=monitor_process_resources,
            args=(process.pid, timeout)
        )
        monitor_thread.daemon = True
        monitor_thread.start()
        
        # 监控输出
        start_time = time.time()
        output_lines = []
        error_lines = []
        last_output_time = start_time
        
        while True:
            # 检查进程是否完成
            if process.poll() is not None:
                logger.info(f"进程已完成，返回码: {process.returncode}")
                break
            
            # 检查超时
            current_time = time.time()
            elapsed = current_time - start_time
            
            if elapsed > timeout:
                logger.error(f"进程超时 ({timeout}秒)，终止进程")
                process.terminate()
                time.sleep(5)
                if process.poll() is None:
                    logger.error("强制终止进程")
                    process.kill()
                break
            
            # 检查输出停滞
            if current_time - last_output_time > 60:  # 60秒无输出
                logger.warning(f"输出停滞超过60秒，当前耗时: {elapsed:.1f}秒")
                
                # 检查进程是否还活着
                try:
                    proc = psutil.Process(process.pid)
                    if proc.is_running():
                        status = proc.status()
                        cpu_percent = proc.cpu_percent()
                        logger.warning(f"进程状态: {status}, CPU使用率: {cpu_percent:.1f}%")
                        
                        if cpu_percent < 1.0 and status != 'sleeping':
                            logger.error("进程可能已卡死（CPU使用率过低且非睡眠状态）")
                            process.terminate()
                            time.sleep(5)
                            if process.poll() is None:
                                process.kill()
                            break
                except:
                    pass
            
            # 尝试读取输出
            try:
                # 非阻塞读取
                import select
                if hasattr(select, 'select'):
                    ready, _, _ = select.select([process.stdout, process.stderr], [], [], 1)
                    
                    if process.stdout in ready:
                        line = process.stdout.readline()
                        if line:
                            output_lines.append(line.strip())
                            logger.info(f"STDOUT: {line.strip()}")
                            last_output_time = current_time
                    
                    if process.stderr in ready:
                        line = process.stderr.readline()
                        if line:
                            error_lines.append(line.strip())
                            logger.error(f"STDERR: {line.strip()}")
                            last_output_time = current_time
                else:
                    # Windows fallback
                    time.sleep(1)
            except:
                time.sleep(1)
        
        # 获取剩余输出
        try:
            remaining_stdout, remaining_stderr = process.communicate(timeout=10)
            if remaining_stdout:
                output_lines.extend(remaining_stdout.strip().split('\n'))
            if remaining_stderr:
                error_lines.extend(remaining_stderr.strip().split('\n'))
        except subprocess.TimeoutExpired:
            logger.error("获取剩余输出超时")
            process.kill()
        
        # 等待监控线程结束
        monitor_thread.join(timeout=5)
        
        elapsed_time = time.time() - start_time
        logger.info(f"总耗时: {elapsed_time:.1f}秒")
        
        return {
            'returncode': process.returncode,
            'stdout': output_lines,
            'stderr': error_lines,
            'elapsed_time': elapsed_time
        }
        
    except Exception as e:
        logger.error(f"执行命令异常: {e}")
        return {
            'returncode': -1,
            'stdout': [],
            'stderr': [str(e)],
            'elapsed_time': 0
        }

def test_wanx_minimal():
    """测试最小配置的Wanx生成"""
    wan_model_path = Path(__file__).parent / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
    wan_script_path = wan_model_path / "generate.py"
    
    if not wan_script_path.exists():
        logger.error(f"Wanx脚本不存在: {wan_script_path}")
        return False
    
    # 最小配置参数
    output_file = wan_model_path / "test_minimal.mp4"
    if output_file.exists():
        output_file.unlink()
    
    cmd = [
        sys.executable,
        "generate.py",
        "--task", "t2v-1.3B",
        "--size", "832*480",  # 官方支持的分辨率
        "--ckpt_dir", "./Wan2.1-T2V-1.3B",
        "--prompt", "cat",    # 简单提示词
        "--offload_model", "True",
        "--t5_cpu",
        "--sample_shift", "8",
        "--sample_guide_scale", "5.0",  # 更低的引导强度
        "--sample_steps", "10",         # 更少的步数
        "--frame_num", "5",             # 更少的帧数
        "--base_seed", "12345",
        "--save_file", "./test_minimal.mp4"
    ]
    
    logger.info("开始最小配置测试...")
    result = run_wanx_with_monitoring(cmd, str(wan_model_path), timeout=180)
    
    # 检查结果
    if result['returncode'] == 0:
        if output_file.exists() and output_file.stat().st_size > 0:
            logger.info(f"✅ 最小配置测试成功！文件大小: {output_file.stat().st_size} bytes")
            return True
        else:
            logger.error("❌ 进程成功但文件未生成")
            return False
    else:
        logger.error(f"❌ 最小配置测试失败，返回码: {result['returncode']}")
        logger.error("错误输出:")
        for line in result['stderr']:
            logger.error(f"  {line}")
        return False

def test_environment():
    """测试环境配置"""
    logger.info("=== 环境测试 ===")
    
    # Python版本
    logger.info(f"Python版本: {sys.version}")
    
    # PyTorch版本
    try:
        import torch
        logger.info(f"PyTorch版本: {torch.__version__}")
        logger.info(f"CUDA可用: {torch.cuda.is_available()}")
        if torch.cuda.is_available():
            logger.info(f"CUDA版本: {torch.version.cuda}")
            logger.info(f"GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                gpu_name = torch.cuda.get_device_name(i)
                gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1024**3
                logger.info(f"GPU {i}: {gpu_name}, 内存: {gpu_memory:.1f}GB")
    except Exception as e:
        logger.error(f"PyTorch检查失败: {e}")
    
    # 关键依赖
    dependencies = ['diffusers', 'transformers', 'accelerate', 'imageio', 'opencv-python']
    for dep in dependencies:
        try:
            module = __import__(dep.replace('-', '_'))
            version = getattr(module, '__version__', 'unknown')
            logger.info(f"{dep}: {version}")
        except ImportError:
            logger.error(f"❌ {dep}: 未安装")
    
    # 系统资源
    logger.info(f"系统内存: {psutil.virtual_memory().total / 1024**3:.1f}GB")
    logger.info(f"可用内存: {psutil.virtual_memory().available / 1024**3:.1f}GB")

if __name__ == "__main__":
    logger.info("=== Wanx 2.1 深度诊断开始 ===")
    
    # 1. 环境测试
    test_environment()
    
    # 2. 最小配置测试
    logger.info("\n=== 最小配置测试 ===")
    success = test_wanx_minimal()
    
    if success:
        logger.info("🎉 诊断完成：最小配置可以正常工作！")
        logger.info("建议：在实际使用中采用更保守的参数设置")
    else:
        logger.error("❌ 诊断完成：即使最小配置也无法正常工作")
        logger.error("建议：检查环境配置或考虑使用替代方案")
