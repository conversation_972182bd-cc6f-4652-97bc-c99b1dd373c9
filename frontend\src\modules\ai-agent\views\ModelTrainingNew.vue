<template>
  <div class="model-training-new">
    <!-- 英雄区域 -->
    <div class="hero-section">
      <div class="hero-background">
        <div class="hero-content">
          <h1 class="hero-title">🎓 AI模型训练工作室</h1>
          <p class="hero-subtitle">创建专业的AI智能体，让AI具备专业知识和个性</p>
          <div class="hero-features">
            <div class="feature-item">
              <div class="feature-icon">🧠</div>
              <div class="feature-text">专业知识训练</div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">🎭</div>
              <div class="feature-text">个性化定制</div>
            </div>
            <div class="feature-item">
              <div class="feature-icon">⚡</div>
              <div class="feature-text">快速部署</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <div class="container">
        <!-- 创建流程指示器 -->
        <div class="process-indicator">
          <div 
            v-for="(step, index) in trainingSteps" 
            :key="step.id"
            :class="['process-step', { 
              active: currentStep === index, 
              completed: index < currentStep 
            }]"
          >
            <div class="step-circle">
              <span v-if="index < currentStep">✓</span>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <div class="step-info">
              <div class="step-title">{{ step.title }}</div>
              <div class="step-desc">{{ step.description }}</div>
            </div>
          </div>
        </div>

        <!-- 步骤内容 -->
        <div class="step-content-container">
          <!-- 步骤1: 选择专业类型 -->
          <div v-if="currentStep === 0" class="step-content">
            <div class="content-header">
              <h2>🎯 选择专业类型</h2>
              <p>选择您想要训练的专业智能体类型，我们将为您提供专业的训练模板</p>
            </div>

            <div class="profession-grid">
              <div 
                v-for="profession in availableProfessions" 
                :key="profession.id"
                :class="['profession-card', { selected: selectedProfession === profession.id }]"
                @click="selectProfession(profession.id)"
              >
                <div class="profession-icon">{{ profession.icon }}</div>
                <div class="profession-name">{{ profession.name }}</div>
                <div class="profession-desc">{{ profession.description }}</div>
                <div class="profession-features">
                  <div class="feature-tag" v-for="feature in profession.features" :key="feature">
                    {{ feature }}
                  </div>
                </div>
                <div class="profession-examples">
                  <div class="examples-title">训练示例：</div>
                  <div class="example-item" v-for="example in profession.examples.slice(0, 2)" :key="example">
                    {{ example }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 步骤2: 基本配置 -->
          <div v-if="currentStep === 1" class="step-content">
            <div class="content-header">
              <h2>⚙️ 基本配置</h2>
              <p>设置您的专业智能体的基本信息和特性</p>
            </div>

            <div class="config-form">
              <div class="form-section">
                <h3>基础信息</h3>
                <div class="form-grid">
                  <div class="form-item">
                    <label>智能体名称</label>
                    <el-input 
                      v-model="trainingConfig.name" 
                      placeholder="例如：张教授、李医生、王律师"
                      size="large"
                    />
                  </div>
                  <div class="form-item">
                    <label>专业领域</label>
                    <el-select 
                      v-model="trainingConfig.specialization" 
                      placeholder="选择专业领域"
                      size="large"
                      style="width: 100%"
                    >
                      <el-option
                        v-for="spec in currentSpecializations"
                        :key="spec"
                        :label="spec"
                        :value="spec"
                      />
                    </el-select>
                  </div>
                </div>
                
                <div class="form-item full-width">
                  <label>专业描述</label>
                  <el-input 
                    v-model="trainingConfig.description" 
                    type="textarea"
                    :rows="3"
                    placeholder="描述这个智能体的专业能力和特色"
                    size="large"
                  />
                </div>
              </div>

              <div class="form-section">
                <h3>个性设置</h3>
                <div class="personality-selector">
                  <div 
                    v-for="personality in personalityOptions" 
                    :key="personality.id"
                    :class="['personality-option', { selected: trainingConfig.personality === personality.id }]"
                    @click="trainingConfig.personality = personality.id"
                  >
                    <div class="personality-icon">{{ personality.icon }}</div>
                    <div class="personality-name">{{ personality.name }}</div>
                    <div class="personality-desc">{{ personality.description }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 步骤3: 训练数据 -->
          <div v-if="currentStep === 2" class="step-content">
            <div class="content-header">
              <h2>📚 训练数据</h2>
              <p>添加专业对话示例，让AI学习专业的回答方式</p>
            </div>

            <div class="training-data-section">
              <!-- AI智能生成 -->
              <div class="ai-generator" v-if="selectedProfession && trainingConfig.specialization">
                <h3>🤖 AI智能生成训练数据</h3>
                <p>基于您选择的{{ getSelectedProfessionName() }} - {{ trainingConfig.specialization }}，AI为您智能生成专业训练数据：</p>
                <div class="generator-controls">
                  <el-button
                    @click="generateTrainingData"
                    type="primary"
                    :loading="isGenerating"
                    size="large"
                  >
                    <el-icon><magic-stick /></el-icon>
                    {{ isGenerating ? '正在生成...' : 'AI智能生成训练数据' }}
                  </el-button>
                  <span class="generator-tip">AI将根据您的专业领域生成5-8个专业对话示例</span>
                </div>
              </div>

              <!-- 智能推荐 -->
              <div class="smart-suggestions">
                <h3>💡 智能推荐</h3>
                <p>基于您选择的专业类型，我们为您推荐以下训练数据：</p>
                <div class="suggestion-list">
                  <div 
                    v-for="(suggestion, index) in smartSuggestions" 
                    :key="index"
                    class="suggestion-item"
                  >
                    <div class="suggestion-header">
                      <span class="suggestion-title">{{ suggestion.title }}</span>
                      <el-button 
                        @click="addSuggestion(suggestion)" 
                        type="primary" 
                        size="small"
                        :disabled="isSuggestionAdded(suggestion)"
                      >
                        {{ isSuggestionAdded(suggestion) ? '已添加' : '添加' }}
                      </el-button>
                    </div>
                    <div class="suggestion-preview">
                      <div class="user-input">👤 {{ suggestion.input }}</div>
                      <div class="ai-output">🤖 {{ suggestion.output }}</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 自定义训练数据 -->
              <div class="custom-training-data">
                <div class="section-header">
                  <h3>✏️ 自定义训练数据</h3>
                  <el-button @click="addTrainingItem" type="primary" size="small">
                    <el-icon><plus /></el-icon>
                    添加对话示例
                  </el-button>
                </div>

                <div class="training-items">
                  <div 
                    v-for="(item, index) in trainingConfig.training_data" 
                    :key="index"
                    class="training-item"
                  >
                    <div class="item-header">
                      <span class="item-title">对话示例 {{ index + 1 }}</span>
                      <el-button
                        @click="removeTrainingItem(index)"
                        type="danger"
                        size="small"
                      >
                        <el-icon><delete /></el-icon>
                      </el-button>
                    </div>
                    <div class="item-content">
                      <div class="input-section">
                        <label>用户提问：</label>
                        <el-input 
                          v-model="item.input" 
                          placeholder="用户可能会问的问题"
                          size="large"
                        />
                      </div>
                      <div class="output-section">
                        <label>专业回答：</label>
                        <el-input 
                          v-model="item.output" 
                          type="textarea"
                          :rows="3"
                          placeholder="专业、准确的回答"
                          size="large"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 步骤4: 预览和训练 -->
          <div v-if="currentStep === 3" class="step-content">
            <div class="content-header">
              <h2>🚀 预览和训练</h2>
              <p>确认配置信息，开始训练您的专业智能体</p>
            </div>

            <div class="preview-section">
              <div class="preview-card">
                <h3>📋 配置预览</h3>
                <div class="preview-item">
                  <span class="label">智能体名称：</span>
                  <span class="value">{{ trainingConfig.name || '未设置' }}</span>
                </div>
                <div class="preview-item">
                  <span class="label">专业类型：</span>
                  <span class="value">{{ getSelectedProfessionName() }}</span>
                </div>
                <div class="preview-item">
                  <span class="label">专业领域：</span>
                  <span class="value">{{ trainingConfig.specialization || '未设置' }}</span>
                </div>
                <div class="preview-item">
                  <span class="label">个性特征：</span>
                  <span class="value">{{ getSelectedPersonalityName() }}</span>
                </div>
                <div class="preview-item">
                  <span class="label">训练数据：</span>
                  <span class="value">{{ trainingConfig.training_data.length }} 个对话示例</span>
                </div>
              </div>

              <div class="training-progress" v-if="isTraining">
                <h3>🔄 训练进度</h3>
                <el-progress 
                  :percentage="trainingProgress" 
                  :status="trainingProgress === 100 ? 'success' : 'active'"
                  :stroke-width="8"
                />
                <div class="progress-text">{{ trainingStatusText }}</div>
              </div>

              <div class="training-result" v-if="trainingCompleted">
                <h3>✅ 训练完成</h3>
                <div class="result-info">
                  <div class="result-item">
                    <span class="result-label">模型名称：</span>
                    <span class="result-value">{{ trainedModelName }}</span>
                  </div>
                  <div class="result-item">
                    <span class="result-label">训练时间：</span>
                    <span class="result-value">{{ trainingDuration }}</span>
                  </div>
                  <div class="result-item">
                    <span class="result-label">模型大小：</span>
                    <span class="result-value">{{ modelSize }}</span>
                  </div>
                </div>
                <div class="result-actions">
                  <el-button @click="testModel" type="primary" size="large">
                    <el-icon><chat-line-round /></el-icon>
                    测试模型
                  </el-button>
                  <el-button @click="deployModel" type="success" size="large">
                    <el-icon><upload /></el-icon>
                    部署模型
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 导航按钮 -->
        <div class="navigation-buttons">
          <el-button 
            v-if="currentStep > 0" 
            @click="previousStep" 
            size="large"
            class="nav-btn-secondary"
          >
            <el-icon><arrow-left /></el-icon>
            上一步
          </el-button>
          
          <div class="nav-spacer"></div>
          
          <el-button 
            v-if="currentStep < trainingSteps.length - 1" 
            @click="nextStep" 
            type="primary" 
            size="large"
            :disabled="!canProceed()"
          >
            下一步
            <el-icon><arrow-right /></el-icon>
          </el-button>
          
          <el-button 
            v-if="currentStep === trainingSteps.length - 1 && !isTraining && !trainingCompleted" 
            @click="startTraining" 
            type="success" 
            size="large"
            :disabled="!canStartTraining()"
          >
            <el-icon><cpu /></el-icon>
            开始训练
          </el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Plus, Delete, ArrowLeft, ArrowRight, ChatLineRound,
  Upload, Cpu, MagicStick
} from '@element-plus/icons-vue'
import modelTrainingService from '../services/modelTrainingService.js'

export default {
  name: 'ModelTrainingNew',
  components: {
    Plus,
    Delete,
    ArrowLeft,
    ArrowRight,
    ChatLineRound,
    Upload,
    Cpu,
    MagicStick
  },
  
  setup() {
    const router = useRouter()
    const currentStep = ref(0)
    const selectedProfession = ref('')
    const isTraining = ref(false)
    const trainingCompleted = ref(false)
    const trainingProgress = ref(0)
    const trainingStatusText = ref('')
    const trainedModelName = ref('')
    const trainingDuration = ref('')
    const modelSize = ref('')
    const isGenerating = ref(false)

    // 训练步骤
    const trainingSteps = [
      { id: 'profession', title: '选择专业', description: '确定智能体类型' },
      { id: 'config', title: '基本配置', description: '设置基础信息' },
      { id: 'data', title: '训练数据', description: '添加专业知识' },
      { id: 'train', title: '开始训练', description: '生成专业模型' }
    ]

    // 训练配置
    const trainingConfig = reactive({
      name: '',
      description: '',
      specialization: '',
      personality: 'professional',
      training_data: []
    })

    // 可用专业类型
    const availableProfessions = ref([
      {
        id: 'teacher',
        name: '教师',
        icon: '👨‍🏫',
        description: '专业的教育工作者，擅长教学和指导',
        features: ['耐心教学', '因材施教', '知识传授'],
        examples: ['如何解这道数学题？', '请解释这个概念', '学习方法建议'],
        specializations: ['数学', '语文', '英语', '物理', '化学', '生物', '历史', '地理', '音乐', '美术', '体育', '计算机']
      },
      {
        id: 'doctor',
        name: '医生',
        icon: '👨‍⚕️',
        description: '专业的医疗工作者，提供健康咨询',
        features: ['专业诊断', '健康建议', '医疗知识'],
        examples: ['这个症状是什么原因？', '如何预防疾病？', '用药注意事项'],
        specializations: ['内科', '外科', '儿科', '妇科', '眼科', '皮肤科', '心理科', '中医科']
      },
      {
        id: 'lawyer',
        name: '律师',
        icon: '👨‍💼',
        description: '专业的法律工作者，提供法律咨询',
        features: ['法律分析', '风险评估', '专业建议'],
        examples: ['这种情况如何处理？', '法律风险分析', '合同条款解释'],
        specializations: ['民事法', '刑事法', '商事法', '劳动法', '知识产权法', '房地产法', '婚姻法', '税法']
      },
      {
        id: 'consultant',
        name: '顾问',
        icon: '💼',
        description: '专业的商业顾问，提供战略建议',
        features: ['战略分析', '商业洞察', '解决方案'],
        examples: ['市场分析建议', '商业模式优化', '投资决策建议'],
        specializations: ['管理咨询', '财务咨询', '市场咨询', '技术咨询', '人力资源', '战略规划', '投资顾问', '创业指导']
      }
    ])

    // 个性选项
    const personalityOptions = ref([
      { id: 'professional', name: '专业严谨', icon: '👔', description: '正式专业，逻辑清晰' },
      { id: 'friendly', name: '友好亲切', icon: '😊', description: '温和友善，平易近人' },
      { id: 'patient', name: '耐心细致', icon: '🤗', description: '耐心教导，细致入微' },
      { id: 'authoritative', name: '权威专家', icon: '🎓', description: '权威专业，经验丰富' }
    ])

    // 当前专业化选项
    const currentSpecializations = computed(() => {
      const profession = availableProfessions.value.find(p => p.id === selectedProfession.value)
      return profession ? profession.specializations : []
    })

    // 智能推荐 - 根据专业类型和专业领域提供精准推荐
    const smartSuggestions = computed(() => {
      const profession = availableProfessions.value.find(p => p.id === selectedProfession.value)
      if (!profession) return []

      // 基础推荐数据
      const baseSuggestions = {
        teacher: {
          // 通用教师推荐
          general: [
            {
              title: '课堂管理',
              input: '学生上课不专心怎么办？',
              output: '学生注意力不集中是常见问题。我建议：1. 采用多样化的教学方法，如互动问答、小组讨论 2. 设置适当的课堂规则和奖励机制 3. 关注学生的个体差异，调整教学节奏 4. 创造积极的学习氛围，激发学习兴趣。'
            },
            {
              title: '学习方法指导',
              input: '如何帮助学生提高学习效率？',
              output: '提高学习效率需要科学的方法：1. 帮助学生制定个性化的学习计划 2. 教授有效的学习技巧，如思维导图、记忆法 3. 培养良好的学习习惯 4. 定期检查学习进度，及时调整策略 5. 鼓励学生主动思考和提问。'
            }
          ],
          // 英语老师专门推荐
          英语: [
            {
              title: '英语语法教学',
              input: '学生总是搞不清楚时态，怎么教？',
              output: '时态确实是英语学习的难点。我建议这样教学：1. 用时间轴图解，让学生直观理解不同时态的时间概念 2. 结合具体情境，让学生在语境中理解时态用法 3. 设计对比练习，突出不同时态的区别 4. 多用实例和故事，让抽象概念具体化 5. 循序渐进，先掌握基本时态再学复合时态。'
            },
            {
              title: '英语口语训练',
              input: '如何提高学生的英语口语能力？',
              output: '提高口语能力需要多方面努力：1. 创造英语交流环境，鼓励学生大胆开口 2. 设计角色扮演、情景对话等互动活动 3. 纠正发音时要耐心，重点关注影响理解的错误 4. 教授实用的口语表达和习惯用语 5. 定期组织英语角或演讲比赛，增加练习机会。'
            },
            {
              title: '英语阅读理解',
              input: '学生阅读理解总是做不好，有什么技巧？',
              output: '阅读理解需要系统的训练方法：1. 教授阅读策略，如略读、精读、扫读的使用场合 2. 培养学生抓关键词和主题句的能力 3. 训练学生根据上下文猜测词义 4. 教授不同题型的解题技巧，如细节题、推理题、主旨题 5. 增加阅读量，提高阅读速度和理解能力。'
            },
            {
              title: '英语写作指导',
              input: '如何指导学生写好英语作文？',
              output: '英语写作需要循序渐进的指导：1. 从句子结构开始，确保语法正确 2. 教授段落写作，强调主题句和支撑句 3. 提供写作模板和常用句型 4. 重视写作过程，包括构思、起草、修改 5. 多读优秀范文，学习表达方式 6. 定期练习不同类型的写作，如记叙文、议论文等。'
            }
          ],
          // 数学老师推荐
          数学: [
            {
              title: '数学概念教学',
              input: '如何让学生理解抽象的数学概念？',
              output: '抽象概念的教学需要具体化：1. 使用生活中的实例来解释数学概念 2. 运用图形、模型等直观教具 3. 从具体到抽象，循序渐进地引导 4. 鼓励学生用自己的话解释概念 5. 设计动手操作活动，让学生在实践中理解。'
            },
            {
              title: '数学解题方法',
              input: '学生解题思路混乱，如何培养逻辑思维？',
              output: '培养数学逻辑思维需要系统训练：1. 教授解题步骤，强调分析-计划-执行-检验的过程 2. 引导学生画图、列表等方式整理信息 3. 鼓励多种解法，培养发散思维 4. 重视解题后的反思和总结 5. 从简单问题开始，逐步提高难度。'
            }
          ],
          // 语文老师推荐
          语文: [
            {
              title: '阅读理解教学',
              input: '如何提高学生的阅读理解能力？',
              output: '阅读理解能力的培养需要多方面训练：1. 教授阅读方法，如预测、质疑、总结等策略 2. 引导学生关注文章结构和写作手法 3. 培养学生的想象力和联想能力 4. 鼓励学生表达自己的理解和感受 5. 选择适合的阅读材料，激发阅读兴趣。'
            },
            {
              title: '作文写作指导',
              input: '学生作文总是写不好，怎么指导？',
              output: '作文教学需要系统的方法：1. 从观察生活开始，积累写作素材 2. 教授写作技巧，如开头结尾、过渡衔接 3. 重视写作过程，包括构思、起草、修改 4. 多读优秀范文，学习表达方式 5. 鼓励真情实感，避免套话空话 6. 定期练习不同体裁的写作。'
            }
          ]
        },
        doctor: [
          {
            title: '症状分析',
            input: '最近总是头痛，是什么原因？',
            output: '头痛的原因很多，常见的包括：1. 紧张性头痛 - 通常由压力、疲劳引起 2. 偏头痛 - 可能伴有恶心、畏光 3. 睡眠不足或睡眠质量差 4. 颈椎问题导致的头痛 5. 眼疲劳引起的头痛。建议您注意休息，如果症状持续或加重，请及时就医检查。'
          },
          {
            title: '健康建议',
            input: '如何保持身体健康？',
            output: '保持健康需要综合考虑多个方面：1. 均衡饮食 - 多吃蔬菜水果，控制油盐糖摄入 2. 适量运动 - 每周至少150分钟中等强度运动 3. 充足睡眠 - 成人每天7-9小时 4. 定期体检 - 及早发现健康问题 5. 心理健康 - 学会管理压力，保持积极心态 6. 戒烟限酒，避免不良生活习惯。'
          }
        ],
        lawyer: [
          {
            title: '法律咨询',
            input: '合同违约怎么办？',
            output: '合同违约的处理需要分析具体情况：1. 确认违约事实和程度 - 收集相关证据 2. 评估实际损失 - 计算直接损失和间接损失 3. 选择合适的救济方式 - 要求继续履行、赔偿损失或解除合同 4. 协商解决 - 先尝试与对方协商 5. 法律途径 - 必要时通过仲裁或诉讼解决。建议保留所有相关文件和证据。'
          },
          {
            title: '权益保护',
            input: '如何保护自己的合法权益？',
            output: '保护合法权益需要做到：1. 了解相关法律法规 - 知法才能用法 2. 保留完整的证据材料 - 合同、收据、通信记录等 3. 及时寻求专业法律帮助 - 咨询律师或法律援助 4. 通过正当途径维权 - 协商、调解、仲裁、诉讼 5. 注意时效性 - 了解诉讼时效，及时行动 6. 理性维权 - 避免过激行为。'
          }
        ],
        consultant: [
          {
            title: '商业分析',
            input: '如何分析市场机会？',
            output: '市场机会分析需要从多个维度考虑：1. 市场规模和增长趋势 - 分析目标市场的容量和发展前景 2. 竞争格局分析 - 了解主要竞争对手和市场份额 3. 客户需求洞察 - 深入了解目标客户的痛点和需求 4. 技术发展趋势 - 关注相关技术的发展方向 5. 政策环境分析 - 考虑政策对行业的影响 6. SWOT分析 - 评估自身的优势、劣势、机会和威胁。'
          },
          {
            title: '战略建议',
            input: '公司发展战略如何制定？',
            output: '制定发展战略需要系统的方法：1. 明确公司愿景和使命 - 确定长期目标和价值观 2. 分析内外部环境 - 进行SWOT分析和行业分析 3. 确定战略目标 - 设定具体、可衡量的目标 4. 制定实施路径 - 分阶段、分步骤的行动计划 5. 资源配置 - 合理分配人力、财力、物力资源 6. 风险评估 - 识别潜在风险并制定应对措施 7. 监控和调整 - 定期评估执行效果并及时调整。'
          }
        ]
      }

      // 根据专业类型获取推荐
      const professionSuggestions = baseSuggestions[selectedProfession.value] || []

      // 如果是教师且选择了具体专业领域，返回专门的推荐
      if (selectedProfession.value === 'teacher' && trainingConfig.specialization) {
        const specializationSuggestions = professionSuggestions[trainingConfig.specialization] || []
        const generalSuggestions = professionSuggestions.general || []
        return [...specializationSuggestions, ...generalSuggestions]
      }

      // 如果是教师但没选择专业领域，返回通用推荐
      if (selectedProfession.value === 'teacher') {
        return professionSuggestions.general || []
      }

      // 其他专业返回对应推荐
      return professionSuggestions || []
    })

    // 方法
    const selectProfession = (professionId) => {
      selectedProfession.value = professionId
      trainingConfig.specialization = ''
    }

    const nextStep = () => {
      if (canProceed() && currentStep.value < trainingSteps.length - 1) {
        currentStep.value++
      }
    }

    const previousStep = () => {
      if (currentStep.value > 0) {
        currentStep.value--
      }
    }

    const canProceed = () => {
      switch (currentStep.value) {
        case 0: return selectedProfession.value !== ''
        case 1: return trainingConfig.name.trim() !== ''
        case 2: return trainingConfig.training_data.length >= 3
        default: return true
      }
    }

    const canStartTraining = () => {
      return trainingConfig.name.trim() !== '' && 
             selectedProfession.value !== '' && 
             trainingConfig.training_data.length >= 3
    }

    const addTrainingItem = () => {
      trainingConfig.training_data.push({
        input: '',
        output: ''
      })
    }

    const removeTrainingItem = (index) => {
      trainingConfig.training_data.splice(index, 1)
    }

    const addSuggestion = (suggestion) => {
      trainingConfig.training_data.push({
        input: suggestion.input,
        output: suggestion.output
      })
    }

    const isSuggestionAdded = (suggestion) => {
      return trainingConfig.training_data.some(item => 
        item.input === suggestion.input && item.output === suggestion.output
      )
    }

    const startTraining = async () => {
      if (!canStartTraining()) {
        ElMessage.warning('请完善训练配置')
        return
      }

      isTraining.value = true
      trainingProgress.value = 0
      trainingStatusText.value = '准备训练数据...'

      try {
        // 准备训练配置
        const trainingData = {
          name: trainingConfig.name,
          description: trainingConfig.description,
          profession_type: selectedProfession.value,
          specialization: trainingConfig.specialization,
          personality: trainingConfig.personality,
          training_examples: trainingConfig.training_data
        }

        // 调用API开始训练
        const response = await modelTrainingService.createProfessionalModel(trainingData)

        if (response.success) {
          // 模拟训练进度更新
          const trainingSteps = [
            { progress: 20, text: '处理训练数据...' },
            { progress: 40, text: '初始化模型架构...' },
            { progress: 60, text: '开始模型训练...' },
            { progress: 80, text: '优化模型参数...' },
            { progress: 100, text: '训练完成，保存模型...' }
          ]

          for (const step of trainingSteps) {
            await new Promise(resolve => setTimeout(resolve, 2000))
            trainingProgress.value = step.progress
            trainingStatusText.value = step.text
          }

          // 训练完成
          isTraining.value = false
          trainingCompleted.value = true
          trainedModelName.value = response.data.modelId || `${trainingConfig.name}_${selectedProfession.value}_model`
          trainingDuration.value = '约 10 分钟'
          modelSize.value = '2.3 GB'

          ElMessage.success('专业智能体模型训练完成！')
        } else {
          throw new Error(response.message || '训练失败')
        }
      } catch (error) {
        console.error('模型训练失败:', error)
        isTraining.value = false
        ElMessage.error('模型训练失败，请重试')
      }
    }

    const testModel = async () => {
      try {
        const testInput = `作为一名${getSelectedProfessionName()}，请介绍一下你的专业能力。`
        const response = await modelTrainingService.testModel(trainedModelName.value, testInput)

        if (response.success) {
          ElMessage.success('模型测试成功！')
          // 可以显示测试结果
          console.log('测试结果:', response.data)
        } else {
          ElMessage.error('模型测试失败')
        }
      } catch (error) {
        console.error('测试模型失败:', error)
        ElMessage.error('模型测试失败，请重试')
      }
    }

    const deployModel = async () => {
      try {
        const deployConfig = {
          name: trainingConfig.name,
          description: trainingConfig.description,
          profession_type: selectedProfession.value,
          specialization: trainingConfig.specialization
        }

        const response = await modelTrainingService.deployModel(trainedModelName.value, deployConfig)

        if (response.success) {
          ElMessage.success('专业智能体部署成功！')
          setTimeout(() => {
            router.push('/agents?tab=my')
          }, 1500)
        } else {
          ElMessage.error('模型部署失败')
        }
      } catch (error) {
        console.error('部署模型失败:', error)
        ElMessage.error('模型部署失败，请重试')
      }
    }

    const getSelectedProfessionName = () => {
      const profession = availableProfessions.value.find(p => p.id === selectedProfession.value)
      return profession ? profession.name : '未选择'
    }

    const getSelectedPersonalityName = () => {
      const personality = personalityOptions.value.find(p => p.id === trainingConfig.personality)
      return personality ? personality.name : '专业严谨'
    }

    // AI智能生成训练数据
    const generateTrainingData = async () => {
      if (!selectedProfession.value || !trainingConfig.specialization) {
        ElMessage.warning('请先选择专业类型和专业领域')
        return
      }

      isGenerating.value = true

      try {
        // 模拟AI生成过程
        await new Promise(resolve => setTimeout(resolve, 2000))

        // 根据专业类型和领域生成训练数据
        const generatedData = generateProfessionalTrainingData(
          selectedProfession.value,
          trainingConfig.specialization
        )

        // 添加到训练数据中
        generatedData.forEach(item => {
          trainingConfig.training_data.push(item)
        })

        ElMessage.success(`AI成功生成了 ${generatedData.length} 个专业训练示例！`)
      } catch (error) {
        console.error('AI生成失败:', error)
        ElMessage.error('AI生成失败，请重试')
      } finally {
        isGenerating.value = false
      }
    }

    // 生成专业训练数据
    const generateProfessionalTrainingData = (profession, specialization) => {
      const templates = {
        teacher: {
          英语: [
            {
              input: '学生问：老师，"have been doing" 和 "have done" 有什么区别？',
              output: '这是一个很好的问题！"have been doing"是现在完成进行时，强调动作从过去开始一直持续到现在，可能还会继续。比如"I have been studying English for 3 years"（我学英语已经3年了，现在还在学）。而"have done"是现在完成时，强调动作已经完成，对现在有影响。比如"I have finished my homework"（我已经完成作业了）。关键区别在于是否强调动作的持续性。'
            },
            {
              input: '如何快速记住英语单词？',
              output: '记忆英语单词需要科学的方法：1. 词根词缀法 - 学会常用词根如"tele-"（远程）、"-ology"（学科）2. 联想记忆法 - 将单词与熟悉的事物联系 3. 语境记忆法 - 在句子和文章中记忆单词 4. 重复记忆法 - 遵循艾宾浩斯遗忘曲线，定期复习 5. 分类记忆法 - 按主题分类记忆，如食物、动物等 6. 制作单词卡片，随时复习。记住，理解比死记硬背更重要！'
            },
            {
              input: '英语作文总是写不好，有什么技巧吗？',
              output: '英语写作确实需要技巧和练习：1. 结构清晰 - 使用"总-分-总"结构，每段有明确主题 2. 连接词的使用 - 如"firstly, however, in conclusion"让文章更流畅 3. 句式多样化 - 简单句、复合句、复杂句相结合 4. 词汇丰富 - 避免重复使用同一个词，学会同义词替换 5. 多读范文 - 学习优秀作文的表达方式 6. 从模仿开始 - 先模仿好的句型和段落结构 7. 定期练习 - 每周至少写2-3篇短文。记住，好的作文是改出来的！'
            },
            {
              input: '英语听力总是听不懂，怎么提高？',
              output: '听力提高需要循序渐进的训练：1. 从慢速开始 - 先听慢速英语，逐渐提高语速 2. 精听与泛听结合 - 精听训练细节理解，泛听培养语感 3. 跟读练习 - 提高语音语调，增强语感 4. 预测技巧 - 根据上下文预测内容 5. 关键词捕捉 - 学会抓住关键信息 6. 多样化材料 - 新闻、电影、歌曲、播客等 7. 创造英语环境 - 每天至少听30分钟英语。坚持练习，听力一定会有显著提高！'
            },
            {
              input: '如何纠正学生的英语发音？',
              output: '纠正发音需要耐心和正确的方法：1. 示范发音 - 清晰地示范正确发音，让学生模仿 2. 音标教学 - 教授国际音标，帮助学生掌握发音规律 3. 对比练习 - 将错误发音与正确发音对比 4. 口型指导 - 指导舌位、唇形等发音要领 5. 录音对比 - 让学生录音并与标准发音对比 6. 重点突破 - 针对中国学生常见发音问题重点练习 7. 鼓励为主 - 多鼓励，避免过度纠正影响学生信心。记住，发音是可以通过练习改善的！'
            }
          ],
          数学: [
            {
              input: '学生问：为什么要学数学？数学有什么用？',
              output: '这是一个很重要的问题！数学的作用远比我们想象的大：1. 逻辑思维训练 - 数学培养严密的逻辑推理能力 2. 解决问题的能力 - 数学教会我们分析问题、寻找规律、找到解决方案 3. 生活应用 - 购物计算、理财投资、装修设计都需要数学 4. 科技基础 - 计算机、人工智能、航天技术都建立在数学基础上 5. 美的欣赏 - 数学中的对称、黄金比例体现了自然之美 6. 职业发展 - 工程师、经济学家、数据分析师等都需要数学。数学不仅是工具，更是一种思维方式！'
            },
            {
              input: '如何让学生不怕数学，爱上数学？',
              output: '让学生爱上数学需要用心引导：1. 生活化教学 - 用生活中的例子解释数学概念，如用购物讲比例 2. 游戏化学习 - 设计数学游戏，在玩中学 3. 成功体验 - 从简单问题开始，让学生体验成功的喜悦 4. 故事化讲解 - 讲述数学家的故事，数学发现的历程 5. 动手操作 - 用教具、模型让抽象概念具体化 6. 鼓励提问 - 营造敢问敢想的氛围 7. 个性化指导 - 针对不同学生的特点因材施教 8. 展示数学之美 - 让学生发现数学中的规律和美感。记住，兴趣是最好的老师！'
            }
          ],
          语文: [
            {
              input: '如何提高学生的阅读理解能力？',
              output: '阅读理解能力的提升需要系统训练：1. 阅读方法指导 - 教授预测、质疑、总结等阅读策略 2. 文本结构分析 - 帮助学生理解文章的组织结构 3. 关键信息提取 - 训练学生快速找到主题句、关键词 4. 语境理解 - 教会学生根据上下文理解词语含义 5. 情感体验 - 引导学生感受作者的情感和态度 6. 多元解读 - 鼓励学生从不同角度理解文本 7. 阅读积累 - 增加阅读量，提高阅读速度 8. 讨论交流 - 通过讨论加深理解。阅读是一个主动建构意义的过程！'
            }
          ]
        },
        doctor: {
          内科: [
            {
              input: '患者问：医生，我最近总是感觉疲劳，是什么原因？',
              output: '疲劳是一个常见症状，可能的原因很多：1. 生理性疲劳 - 睡眠不足、过度劳累、营养不良 2. 心理性疲劳 - 压力过大、焦虑抑郁 3. 疾病相关 - 贫血、甲状腺功能异常、糖尿病、心脏病等 4. 药物副作用 - 某些药物可能导致疲劳。建议您：保证充足睡眠、均衡饮食、适量运动、管理压力。如果疲劳持续超过2周且影响日常生活，建议来院检查血常规、甲状腺功能等相关项目，以排除器质性疾病。'
            }
          ]
        }
      }

      const professionTemplates = templates[profession] || {}
      const specializationTemplates = professionTemplates[specialization] || []

      // 如果没有专门的模板，生成通用的
      if (specializationTemplates.length === 0) {
        return generateGenericTrainingData(profession, specialization)
      }

      return specializationTemplates
    }

    // 生成通用训练数据
    const generateGenericTrainingData = (profession, specialization) => {
      const genericTemplates = {
        teacher: [
          {
            input: `作为${specialization}老师，如何激发学生的学习兴趣？`,
            output: `激发学生对${specialization}的兴趣需要多种方法：1. 联系生活实际，让学生看到${specialization}的实用性 2. 采用多样化的教学方法，如互动游戏、实验演示等 3. 设置适当的挑战，让学生体验成功的喜悦 4. 讲述相关的有趣故事和案例 5. 鼓励学生提问和探索 6. 创造积极的学习氛围。记住，兴趣是最好的老师！`
          },
          {
            input: `${specialization}学习中常见的困难有哪些？如何帮助学生克服？`,
            output: `${specialization}学习中的常见困难包括：1. 基础知识不扎实 2. 学习方法不当 3. 缺乏学习兴趣 4. 理解能力有限。帮助学生克服困难的方法：1. 查漏补缺，巩固基础 2. 教授有效的学习方法和技巧 3. 因材施教，针对性指导 4. 多鼓励，增强学生信心 5. 创设情境，帮助理解 6. 及时反馈，调整教学策略。`
          }
        ],
        doctor: [
          {
            input: `${specialization}常见的疾病有哪些？如何预防？`,
            output: `${specialization}领域的常见疾病需要专业的诊断和治疗。预防方面，建议：1. 保持健康的生活方式 2. 定期体检，早发现早治疗 3. 注意个人卫生 4. 合理饮食，适量运动 5. 避免危险因素 6. 遵医嘱用药。如有不适症状，请及时就医，不要自行诊断和治疗。`
          }
        ],
        lawyer: [
          {
            input: `在${specialization}领域，当事人应该注意哪些法律风险？`,
            output: `在${specialization}领域，常见的法律风险包括：1. 合同条款不明确或有漏洞 2. 证据保全不及时 3. 诉讼时效问题 4. 法律程序不当。建议：1. 签署重要文件前咨询专业律师 2. 保留完整的证据材料 3. 了解相关法律法规 4. 及时寻求法律帮助 5. 通过正当途径维护权益。`
          }
        ],
        consultant: [
          {
            input: `在${specialization}咨询中，如何为客户提供有价值的建议？`,
            output: `提供有价值的${specialization}咨询建议需要：1. 深入了解客户的具体情况和需求 2. 进行全面的分析和评估 3. 结合行业经验和专业知识 4. 提供切实可行的解决方案 5. 考虑实施的可行性和风险 6. 持续跟踪和调整建议。专业的咨询不仅要解决当前问题，更要为客户的长远发展考虑。`
          }
        ]
      }

      return genericTemplates[profession] || []
    }

    return {
      currentStep,
      selectedProfession,
      isTraining,
      trainingCompleted,
      trainingProgress,
      trainingStatusText,
      trainedModelName,
      trainingDuration,
      modelSize,
      isGenerating,
      trainingSteps,
      trainingConfig,
      availableProfessions,
      personalityOptions,
      currentSpecializations,
      smartSuggestions,
      selectProfession,
      nextStep,
      previousStep,
      canProceed,
      canStartTraining,
      addTrainingItem,
      removeTrainingItem,
      addSuggestion,
      isSuggestionAdded,
      startTraining,
      testModel,
      deployModel,
      getSelectedProfessionName,
      getSelectedPersonalityName,
      generateTrainingData
    }
  }
}
</script>

<style scoped>
.model-training-new {
  min-height: 100vh;
  background: #f8fafc;
}

/* 英雄区域 */
.hero-section {
  position: relative;
  height: 40vh;
  min-height: 300px;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-content {
  text-align: center;
  color: white;
  max-width: 800px;
  padding: 0 20px;
}

.hero-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
  font-weight: 300;
}

.hero-features {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
}

.feature-icon {
  font-size: 2rem;
}

.feature-text {
  font-size: 0.9rem;
  opacity: 0.9;
}

/* 主要内容 */
.main-content {
  position: relative;
  z-index: 2;
  margin-top: -2rem;
  padding-bottom: 4rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 流程指示器 */
.process-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 3rem;
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  flex: 1;
  max-width: 200px;
}

.process-step:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 20px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: #e2e8f0;
  z-index: 1;
}

.process-step.completed:not(:last-child)::after {
  background: #10b981;
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  position: relative;
  z-index: 2;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.process-step.active .step-circle {
  background: #667eea;
  color: white;
}

.process-step.completed .step-circle {
  background: #10b981;
  color: white;
}

.step-info {
  text-align: center;
}

.step-title {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.step-desc {
  font-size: 0.8rem;
  color: #64748b;
}

/* 步骤内容 */
.step-content-container {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
  min-height: 500px;
}

.content-header {
  text-align: center;
  margin-bottom: 2rem;
}

.content-header h2 {
  font-size: 2rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.5rem;
}

.content-header p {
  color: #64748b;
  font-size: 1.1rem;
}

/* 专业类型选择 */
.profession-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.profession-card {
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.profession-card:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.profession-card.selected {
  border-color: #667eea;
  background: #f0f4ff;
}

.profession-icon {
  font-size: 3rem;
  text-align: center;
  margin-bottom: 1rem;
}

.profession-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  text-align: center;
  margin-bottom: 0.5rem;
}

.profession-desc {
  color: #64748b;
  text-align: center;
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.profession-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  justify-content: center;
  margin-bottom: 1rem;
}

.feature-tag {
  background: #e0e7ff;
  color: #3730a3;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.profession-examples {
  border-top: 1px solid #f1f5f9;
  padding-top: 1rem;
}

.examples-title {
  font-size: 0.8rem;
  font-weight: 600;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.example-item {
  font-size: 0.75rem;
  color: #94a3b8;
  margin-bottom: 0.25rem;
}

/* 配置表单 */
.config-form {
  max-width: 800px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: #fafafa;
}

.form-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.form-item {
  margin-bottom: 1rem;
}

.form-item.full-width {
  grid-column: 1 / -1;
}

.form-item label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

/* 个性选择器 */
.personality-selector {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.personality-option {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}

.personality-option:hover {
  border-color: #667eea;
}

.personality-option.selected {
  border-color: #667eea;
  background: #f0f4ff;
}

.personality-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.personality-name {
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 0.25rem;
}

.personality-desc {
  font-size: 0.8rem;
  color: #64748b;
}

/* 训练数据 */
.training-data-section {
  max-width: 900px;
  margin: 0 auto;
}

/* AI智能生成器 */
.ai-generator {
  margin-bottom: 2rem;
  padding: 2rem;
  border: 2px solid #667eea;
  border-radius: 12px;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  text-align: center;
}

.ai-generator h3 {
  color: #667eea;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.ai-generator p {
  color: #4c51bf;
  margin-bottom: 1.5rem;
}

.generator-controls {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.generator-tip {
  font-size: 0.9rem;
  color: #6b7280;
  font-style: italic;
}

.smart-suggestions {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 1px solid #e0e7ff;
  border-radius: 12px;
  background: #f8fafc;
}

.smart-suggestions h3 {
  color: #667eea;
  margin-bottom: 0.5rem;
}

.suggestion-list {
  margin-top: 1rem;
}

.suggestion-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  background: white;
}

.suggestion-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.suggestion-title {
  font-weight: 600;
  color: #1a202c;
}

.suggestion-preview {
  font-size: 0.9rem;
}

.user-input {
  color: #1976d2;
  margin-bottom: 0.25rem;
}

.ai-output {
  color: #388e3c;
}

.custom-training-data {
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  background: white;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  margin: 0;
  color: #1a202c;
}

.training-items {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.training-item {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1rem;
  background: #fafafa;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.item-title {
  font-weight: 600;
  color: #1a202c;
}

.item-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.input-section,
.output-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.input-section label,
.output-section label {
  font-weight: 500;
  color: #374151;
}

/* 预览和训练 */
.preview-section {
  max-width: 600px;
  margin: 0 auto;
}

.preview-card {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  background: white;
  margin-bottom: 2rem;
}

.preview-card h3 {
  color: #1a202c;
  margin-bottom: 1rem;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
  border-bottom: 1px solid #f1f5f9;
}

.preview-item:last-child {
  border-bottom: none;
}

.label {
  font-weight: 500;
  color: #64748b;
}

.value {
  color: #1a202c;
}

.training-progress {
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 1.5rem;
  background: white;
  margin-bottom: 2rem;
}

.training-progress h3 {
  color: #1a202c;
  margin-bottom: 1rem;
}

.progress-text {
  text-align: center;
  margin-top: 1rem;
  color: #64748b;
}

.training-result {
  border: 1px solid #10b981;
  border-radius: 12px;
  padding: 1.5rem;
  background: #f0fdf4;
}

.training-result h3 {
  color: #10b981;
  margin-bottom: 1rem;
}

.result-info {
  margin-bottom: 1.5rem;
}

.result-item {
  display: flex;
  justify-content: space-between;
  padding: 0.5rem 0;
}

.result-label {
  font-weight: 500;
  color: #64748b;
}

.result-value {
  color: #1a202c;
}

.result-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
}

/* 导航按钮 */
.navigation-buttons {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.nav-spacer {
  flex: 1;
}

.nav-btn-secondary {
  background: #f8fafc;
  border-color: #e2e8f0;
  color: #64748b;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-title {
    font-size: 2rem;
  }

  .hero-features {
    gap: 1.5rem;
  }

  .process-indicator {
    flex-direction: column;
    gap: 1rem;
  }

  .process-step:not(:last-child)::after {
    display: none;
  }

  .profession-grid {
    grid-template-columns: 1fr;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .personality-selector {
    grid-template-columns: 1fr;
  }

  .navigation-buttons {
    flex-direction: column;
  }

  .nav-spacer {
    display: none;
  }
}
</style>
