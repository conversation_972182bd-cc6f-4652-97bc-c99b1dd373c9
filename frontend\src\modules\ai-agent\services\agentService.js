/**
 * 智能体基础服务
 * 处理智能体的基础操作和管理功能
 */

import api from '@/services/api.js';

class AgentService {
  constructor() {
    this.baseUrl = '/api/v1/ai-agent';
  }

  /**
   * 获取智能体列表
   */
  async getAgents(params = {}) {
    try {
      const response = await api.get(`${this.baseUrl}/list`, { params });
      return response.data;
    } catch (error) {
      console.error('获取智能体列表失败:', error);
      return this._getMockAgentList();
    }
  }

  /**
   * 获取我的智能体
   */
  async getMyAgents(params = {}) {
    try {
      const response = await api.get(`${this.baseUrl}/my`, { params });
      return response.data;
    } catch (error) {
      console.error('获取我的智能体失败:', error);
      return this._getMockMyAgents();
    }
  }

  /**
   * 获取智能体分类
   */
  async getCategories() {
    try {
      const response = await api.get(`${this.baseUrl}/categories`);
      return response.data;
    } catch (error) {
      console.error('获取智能体分类失败:', error);
      return this._getMockCategories();
    }
  }

  /**
   * 获取智能体详情
   */
  async getAgentDetail(id) {
    try {
      const response = await api.get(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error('获取智能体详情失败:', error);
      throw error;
    }
  }

  /**
   * 创建智能体
   */
  async createAgent(data) {
    try {
      const response = await api.post(`${this.baseUrl}/create`, data);
      return response.data;
    } catch (error) {
      console.error('创建智能体失败:', error);
      throw error;
    }
  }

  /**
   * 更新智能体
   */
  async updateAgent(id, data) {
    try {
      const response = await api.put(`${this.baseUrl}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('更新智能体失败:', error);
      throw error;
    }
  }

  /**
   * 删除智能体
   */
  async deleteAgent(id) {
    try {
      const response = await api.delete(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error('删除智能体失败:', error);
      throw error;
    }
  }

  /**
   * 搜索智能体
   */
  async searchAgents(query, filters = {}) {
    try {
      const params = {
        q: query,
        ...filters
      };
      const response = await api.get(`${this.baseUrl}/search`, { params });
      return response.data;
    } catch (error) {
      console.error('搜索智能体失败:', error);
      throw error;
    }
  }

  /**
   * 获取推荐智能体
   */
  async getRecommendedAgents(limit = 6) {
    try {
      const response = await api.get(`${this.baseUrl}/recommended`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error('获取推荐智能体失败:', error);
      return this._getMockRecommended();
    }
  }

  /**
   * 获取热门智能体
   */
  async getPopularAgents(limit = 10) {
    try {
      const response = await api.get(`${this.baseUrl}/popular`, {
        params: { limit }
      });
      return response.data;
    } catch (error) {
      console.error('获取热门智能体失败:', error);
      return this._getMockPopular();
    }
  }

  /**
   * 收藏/取消收藏智能体
   */
  async toggleFavorite(agentId) {
    try {
      const response = await api.post(`${this.baseUrl}/${agentId}/favorite`);
      return response.data;
    } catch (error) {
      console.error('切换收藏状态失败:', error);
      throw error;
    }
  }

  /**
   * 获取用户收藏的智能体
   */
  async getFavoriteAgents() {
    try {
      const response = await api.get(`${this.baseUrl}/favorites`);
      return response.data;
    } catch (error) {
      console.error('获取收藏智能体失败:', error);
      return { agents: [] };
    }
  }

  /**
   * 获取模拟智能体列表数据
   */
  _getMockAgentList() {
    return {
      success: true,
      data: {
        items: [
          {
            id: '1',
            name: '数学老师小明',
            description: '专业的数学教师，擅长解答各种数学问题，提供个性化学习指导',
            avatar: '👨‍🏫',
            category: 'teacher',
            status: 'online',
            tags: ['数学', '教育', '个性化'],
            chatCount: 1250,
            rating: 4.8,
            userCount: 320,
            created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
            creator: {
              id: 'user1',
              name: '张老师',
              avatar: '👨‍🏫'
            }
          },
          {
            id: '2',
            name: '客服小助手',
            description: '24小时在线客服，快速解决您的问题，提供专业的售后支持',
            avatar: '🎧',
            category: 'customer-service',
            status: 'online',
            tags: ['客服', '24小时', '专业'],
            chatCount: 2100,
            rating: 4.9,
            userCount: 580,
            created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
            creator: {
              id: 'user2',
              name: '李经理',
              avatar: '👩‍💼'
            }
          },
          {
            id: '3',
            name: '创意写手',
            description: '富有创意的写作助手，帮助您创作各种文案、故事和创意内容',
            avatar: '✍️',
            category: 'creative',
            status: 'busy',
            tags: ['写作', '创意', '文案'],
            chatCount: 890,
            rating: 4.7,
            userCount: 245,
            created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
            creator: {
              id: 'user3',
              name: '王作家',
              avatar: '👨‍💻'
            }
          },
          {
            id: '4',
            name: '数据分析师',
            description: '专业的数据分析专家，帮助您分析数据、生成报告和洞察',
            avatar: '📊',
            category: 'analyst',
            status: 'online',
            tags: ['数据分析', '报告', '洞察'],
            chatCount: 650,
            rating: 4.6,
            userCount: 180,
            created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
            creator: {
              id: 'user4',
              name: '陈分析师',
              avatar: '👩‍💻'
            }
          }
        ],
        total: 4,
        page: 1,
        limit: 10
      }
    };
  }

  /**
   * 获取模拟我的智能体数据
   */
  _getMockMyAgents() {
    return {
      success: true,
      data: {
        items: [
          {
            id: '5',
            name: '我的专属助手',
            description: '根据我的需求定制的个人助手',
            avatar: '🤖',
            category: 'assistant',
            status: 'online',
            tags: ['个人定制', '多功能'],
            chatCount: 45,
            rating: 5.0,
            userCount: 1,
            created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            creator: {
              id: 'current_user',
              name: '我',
              avatar: '👤'
            }
          }
        ],
        total: 1,
        page: 1,
        limit: 10
      }
    };
  }

  /**
   * 获取模拟分类数据
   */
  _getMockCategories() {
    return {
      success: true,
      data: [
        { id: 'all', name: '全部', icon: '🌟', count: 156 },
        { id: 'assistant', name: '助手', icon: '🤖', count: 45 },
        { id: 'teacher', name: '教师', icon: '👨‍🏫', count: 32 },
        { id: 'customer-service', name: '客服', icon: '🎧', count: 28 },
        { id: 'creative', name: '创意', icon: '🎨', count: 25 },
        { id: 'analyst', name: '分析师', icon: '📊', count: 26 }
      ]
    };
  }

  /**
   * 获取模拟推荐数据
   */
  _getMockRecommended() {
    return {
      agents: [
        {
          id: 1,
          name: 'GPT助手',
          description: '强大的通用AI助手',
          category: 'assistant',
          rating: 4.8,
          usage_count: 1500
        },
        {
          id: 2,
          name: '代码专家',
          description: '专业的编程助手',
          category: 'programming',
          rating: 4.9,
          usage_count: 1200
        }
      ]
    };
  }

  /**
   * 获取模拟热门数据
   */
  _getMockPopular() {
    return {
      agents: [
        {
          id: 3,
          name: '写作大师',
          description: '专业的写作助手',
          category: 'writing',
          rating: 4.7,
          usage_count: 2000
        }
      ]
    };
  }
}

// 创建单例实例
const agentService = new AgentService();

export default agentService;
