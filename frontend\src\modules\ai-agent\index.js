// ai-agent 模块统一导出

// 懒加载组件
const AgentMarketplace = () => import('./views/AgentMarketplace.vue');
const AgentChat = () => import('./views/AgentChat.vue');
// 使用全屏聊天界面
const TrueAgentChat = () => import('./views/FullScreenChat.vue');
const TrueAgentChatNew = () => import('./views/FullScreenChat.vue');
const AgentEditor = () => import('./views/AgentEditor.vue');
const AgentStudio = () => import('./views/AgentStudio.vue');
const DigitalHumanChat = () => import('./views/DigitalHumanChat.vue');

// AI智能体模块路由
const aiAgentRoutes = [
  {
    path: 'agents',
    name: 'Agents',
    component: AgentMarketplace,
    meta: { title: 'AI智能体市场', module: 'ai-agent' }
  },
  {
    path: 'agents/chat/:id',
    name: 'AgentC<PERSON>',
    component: AgentChat,
    meta: { title: 'AI智能体对话', module: 'ai-agent' }
  },
  {
    path: 'agents/true-chat',
    name: 'TrueAgentChat',
    component: TrueAgentChat,
    meta: { title: '真实智能体对话', module: 'ai-agent' }
  },
  {
    path: 'ai-agent/chat/:id',
    name: 'TrueAgentChatWithId',
    component: TrueAgentChatNew,
    meta: { title: '智能体对话', module: 'ai-agent' }
  },
  {
    path: 'agents/create',
    name: 'AgentCreate',
    component: AgentEditor,
    meta: { title: '创建智能体', module: 'ai-agent' }
  },
  {
    path: 'agents/studio',
    name: 'AgentStudio',
    component: AgentStudio,
    meta: { title: '智能体工作室', module: 'ai-agent' }
  },
  {
    path: 'agents/studio-new',
    name: 'AgentStudioNew',
    component: () => import('./views/AgentStudioNew.vue'),
    meta: { title: '智能体工作室 - 新版', module: 'ai-agent' }
  },
  {
    path: 'model-training',
    name: 'ModelTraining',
    component: () => import('./views/ModelTraining.vue'),
    meta: { title: '模型训练中心', module: 'ai-agent' }
  },
  {
    path: 'agents/marketplace-new',
    name: 'AgentMarketplaceNew',
    component: () => import('./views/AgentMarketplaceNew.vue'),
    meta: { title: '智能体市场 - 新版', module: 'ai-agent' }
  },
  {
    path: 'agents/editor-new',
    name: 'AgentEditorNew',
    component: () => import('./views/AgentEditorNew.vue'),
    meta: { title: '智能体编辑器 - 新版', module: 'ai-agent' }
  },
  {
    path: 'digital-human/chat',
    name: 'AIAgentDigitalHumanChat',
    component: DigitalHumanChat,
    meta: { title: '数字人对话', module: 'ai-agent' }
  }
];

export default aiAgentRoutes;
