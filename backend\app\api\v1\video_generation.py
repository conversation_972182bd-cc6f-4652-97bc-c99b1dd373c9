"""
视频生成 API - 集成 Wanx 2.1 模型 + Celery 异步处理
"""
import os
import uuid
import logging
from datetime import datetime
from typing import Optional
from pathlib import Path
from fastapi import APIRouter, HTTPException, UploadFile, File, Form
from pydantic import BaseModel

logger = logging.getLogger(__name__)

try:
    from app.tasks.video_generation import generate_text_to_video, wanx_text_to_video, wanx_image_to_video
except ImportError:
    # 如果Celery任务不存在，创建占位符
    wanx_text_to_video = None
    wanx_image_to_video = None

# 简单的内存任务管理器
class SimpleTaskManager:
    def __init__(self):
        self.tasks = {}

    def create_task(self, task_type, task_subtype, user_id, title, description, input_params, estimated_duration, tags):
        task_id = str(uuid.uuid4())
        self.tasks[task_id] = {
            "task_id": task_id,
            "task_type": task_type,
            "task_subtype": task_subtype,
            "user_id": user_id,
            "title": title,
            "description": description,
            "input_params": input_params,
            "status": "pending",
            "progress": 0,
            "message": "任务已创建",
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "estimated_duration": estimated_duration,
            "tags": tags,
            "output_data": None,
            "error_message": None
        }
        return task_id

    def update_task(self, task_id, **kwargs):
        if task_id in self.tasks:
            self.tasks[task_id].update(kwargs)
            self.tasks[task_id]["updated_at"] = datetime.now()

    def get_task(self, task_id):
        return self.tasks.get(task_id)

    def get_user_tasks(self, user_id, task_type=None, limit=20, offset=0):
        user_tasks = [task for task in self.tasks.values() if task["user_id"] == user_id]
        if task_type:
            user_tasks = [task for task in user_tasks if task["task_type"] == task_type]
        return user_tasks[offset:offset+limit]

task_manager = SimpleTaskManager()

class TaskTypes:
    VIDEO_GENERATION = "video_generation"
    class VideoGeneration:
        TEXT_TO_VIDEO = "text_to_video"
        IMAGE_TO_VIDEO = "image_to_video"

class TaskStatus:
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"

# Wanx 2.1 模型路径（后端统一位置）
WAN_MODEL_PATH = Path(__file__).parent.parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "Wan2.1"
WAN_OUTPUT_DIR = Path(__file__).parent.parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "outputs"
WAN_TEMP_DIR = Path(__file__).parent.parent.parent.parent / "storage" / "models" / "video_generation" / "wan" / "temp"

router = APIRouter()

class TextToVideoRequest(BaseModel):
    prompt: str
    model: str = "t2v-1.3B"  # t2v-1.3B 或 t2v-14B
    duration: int = 5
    resolution: str = "768x512"  # Wanx 2.1 支持的分辨率
    fps: int = 24
    guidance_scale: float = 7.5
    num_inference_steps: int = 50

class ImageToVideoRequest(BaseModel):
    prompt: str
    duration: int = 5
    resolution: str = "1280x720"
    fps: int = 24
    motion_strength: float = 0.5
    model: str = "i2v-14B"

class VideoGenerationStatus(BaseModel):
    task_id: str
    status: str  # pending, processing, completed, failed
    progress: int = 0
    message: str = ""
    video_url: Optional[str] = None
    error: Optional[str] = None
    created_at: datetime
    updated_at: datetime

# 旧的内存存储已移除，现在使用 PostgreSQL 数据库

# 旧的异步生成代码已移除，现在使用 Celery 任务处理

@router.post("/generate")
async def generate_video(request: TextToVideoRequest):
    """生成视频 - 兼容前端调用"""
    return await text_to_video(request)

@router.post("/text-to-video")
async def text_to_video(request: TextToVideoRequest):
    """文本转视频 - 使用统一任务管理和 Celery 异步处理"""
    try:
        # 在统一任务表中创建任务记录
        task_id = task_manager.create_task(
            task_type=TaskTypes.VIDEO_GENERATION,
            task_subtype=TaskTypes.VideoGeneration.TEXT_TO_VIDEO,
            user_id="demo-user",  # 后续可以从认证中获取
            title=f"文本转视频: {request.prompt[:50]}...",
            description=f"使用 {request.model} 模型生成视频",
            input_params={
                "prompt": request.prompt,
                "model": request.model,
                "duration": request.duration,
                "resolution": request.resolution,
                "fps": request.fps,
                "guidance_scale": request.guidance_scale,
                "num_inference_steps": request.num_inference_steps
            },
            estimated_duration=request.duration * 30,  # 估算每秒视频需要30秒处理
            tags=["wanx", "text-to-video", request.model]
        )

        # 启动 Celery 任务（如果可用）
        celery_task_id = None
        if wanx_text_to_video:
            celery_task = wanx_text_to_video.delay(
                task_id=task_id,
                prompt=request.prompt,
                model=request.model,
                duration=request.duration,
                resolution=request.resolution,
                fps=request.fps,
                guidance_scale=request.guidance_scale,
                num_inference_steps=request.num_inference_steps,
                user_id="demo-user"
            )
            celery_task_id = celery_task.id

            # 更新任务的 Celery ID
            task_manager.update_task(task_id, celery_task_id=celery_task_id)
        else:
            # 如果Celery不可用，直接标记任务为处理中
            task_manager.update_task(task_id, status=TaskStatus.RUNNING, progress=10, message="正在处理视频生成请求...")

        return {
            "success": True,
            "task_id": task_id,
            "celery_task_id": celery_task_id,
            "message": "Wanx 2.1 文本转视频任务已启动",
            "estimated_time": f"{request.duration * 30}秒"
        }

    except Exception as e:
        logger.error(f"启动视频生成失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动视频生成失败: {str(e)}")

@router.post("/image-to-video")
async def image_to_video(
    image: UploadFile = File(...),
    prompt: str = Form(...),
    duration: int = Form(5),
    resolution: str = Form("1280x720"),
    fps: int = Form(24),
    motion_strength: float = Form(0.5),
    model: str = Form("i2v-14B")
):
    """图片转视频 - 使用 Celery 异步处理"""
    try:
        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 保存上传的图片到临时目录
        WAN_TEMP_DIR.mkdir(parents=True, exist_ok=True)
        image_filename = f"{task_id}_{image.filename}"
        image_path = WAN_TEMP_DIR / image_filename

        with open(image_path, "wb") as f:
            content = await image.read()
            f.write(content)

        # 启动 Celery 任务（如果可用）
        celery_task_id = None
        if wanx_image_to_video:
            celery_task = wanx_image_to_video.delay(
                task_id=task_id,
                image_path=str(image_path),
                prompt=prompt,
                model=model,
                duration=duration,
                resolution=resolution,
                fps=fps,
                motion_strength=motion_strength,
                user_id="demo-user"  # 可以从请求中获取
            )
            celery_task_id = celery_task.id

        return {
            "success": True,
            "task_id": task_id,
            "celery_task_id": celery_task_id,
            "message": "Wanx 2.1 图片转视频任务已启动"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动图片转视频失败: {str(e)}")

@router.get("/task/{task_id}")
async def get_video_task(task_id: str):
    """获取视频任务详情 - 兼容前端调用"""
    return await get_video_status(task_id)

@router.get("/task/{task_id}/progress")
async def get_video_task_progress(task_id: str):
    """获取视频任务进度 - 兼容前端调用"""
    return await get_video_status(task_id)

@router.get("/tasks")
async def get_video_tasks(limit: int = 20, offset: int = 0):
    """获取视频任务列表 - 兼容前端调用"""
    try:
        # 使用demo-user作为默认用户ID
        user_id = "demo-user"

        # 从统一任务管理器查询用户的视频任务
        user_tasks_data = task_manager.get_user_tasks(
            user_id=user_id,
            task_type=TaskTypes.VIDEO_GENERATION,
            limit=limit,
            offset=offset
        )

        user_tasks = []
        for task_data in user_tasks_data:
            # 提取视频URL（如果有）
            video_url = None
            thumbnail_url = None
            if task_data.get('output_data'):
                video_url = task_data['output_data'].get('video_url')
                thumbnail_url = task_data['output_data'].get('thumbnail_url')

            # 从input_params中提取参数
            input_params = task_data.get("input_params", {})

            user_tasks.append({
                "id": task_data["task_id"],
                "task_id": task_data["task_id"],
                "prompt": input_params.get("prompt", ""),
                "duration": input_params.get("duration", 10),
                "resolution": input_params.get("resolution", "768x512"),
                "status": task_data["status"],
                "progress": task_data["progress"],
                "message": task_data["message"],
                "video_url": video_url,
                "thumbnail_url": thumbnail_url,
                "parameters": input_params,
                "created_at": task_data["created_at"],
                "updated_at": task_data["updated_at"]
            })

        return {
            "success": True,
            "data": user_tasks,
            "total": len(user_tasks)
        }
    except Exception as e:
        logger.error(f"获取视频任务列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取视频任务列表失败: {str(e)}")

@router.get("/status/{task_id}")
async def get_video_status(task_id: str):
    """获取视频生成状态"""
    try:
        # 首先尝试从简单任务管理器查询（内存）
        from app.core.celery_simple import simple_task_manager, celery_app

        task_info = simple_task_manager.get_task(task_id)

        # 如果内存中没有，尝试从 PostgreSQL 查询
        if not task_info:
            task_info = task_manager.get_task(task_id)

        # 如果都没有，尝试从 Celery 获取
        if not task_info:
            celery_result = celery_app.AsyncResult(task_id)
            if celery_result.state != 'PENDING':
                # 将 Celery 状态映射到我们的状态
                status_mapping = {
                    'SUCCESS': 'completed',
                    'FAILURE': 'failed',
                    'PROGRESS': 'running',
                    'STARTED': 'running',
                    'RETRY': 'running',
                    'REVOKED': 'failed'
                }

                mapped_status = status_mapping.get(celery_result.state, 'running')

                task_info = {
                    'task_id': task_id,
                    'status': mapped_status,
                    'progress': 100 if celery_result.ready() else 50,
                    'message': str(celery_result.result) if celery_result.ready() else 'Processing...',
                    'output_data': celery_result.result if celery_result.ready() and isinstance(celery_result.result, dict) else None
                }

        if task_info:
            # 提取视频URL（如果有）
            video_url = None
            if task_info.get('output_data'):
                video_url = task_info['output_data'].get('video_url')

            return {
                "success": True,
                "task_id": task_info["task_id"],
                "status": task_info["status"],
                "progress": task_info["progress"],
                "message": task_info["message"],
                "video_url": video_url,
                "error": task_info["error_message"],
                "created_at": task_info["created_at"],
                "updated_at": task_info["updated_at"],
                "task_type": task_info["task_type"],
                "task_subtype": task_info["task_subtype"]
            }
        else:
            raise HTTPException(status_code=404, detail="任务未找到")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查询任务状态失败: {str(e)}")

@router.get("/history/{user_id}")
async def get_video_history(user_id: str):
    """获取用户视频历史 - 使用统一任务管理"""
    try:
        # 从统一任务管理器查询用户的视频任务
        user_tasks_data = task_manager.get_user_tasks(
            user_id=user_id,
            task_type=TaskTypes.VIDEO_GENERATION,
            limit=50
        )

        user_tasks = []
        for task_data in user_tasks_data:
            # 提取视频URL（如果有）
            video_url = None
            if task_data.get('output_data'):
                video_url = task_data['output_data'].get('video_url')

            # 生成标题
            title = task_data.get('title') or f"Wanx_{task_data['task_subtype']}_{task_data['task_id'][:8]}"

            user_tasks.append({
                "id": task_data["task_id"],
                "title": title,
                "type": task_data["task_subtype"] or task_data["task_type"],
                "status": task_data["status"],
                "progress": task_data["progress"],
                "message": task_data["message"],
                "video_url": video_url,
                "parameters": task_data.get("input_params", {}),
                "created_at": task_data["created_at"],
                "updated_at": task_data["updated_at"],
                "estimated_duration": task_data.get("estimated_duration"),
                "actual_duration": task_data.get("actual_duration")
            })

        return {
            "success": True,
            "videos": user_tasks,
            "total": len(user_tasks)
        }
    except Exception as e:
        logger.error(f"获取视频历史失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取视频历史失败: {str(e)}")

@router.get("/download/{filename}")
async def download_video(filename: str):
    """下载生成的视频"""
    try:
        video_path = WAN_OUTPUT_DIR / filename
        if not video_path.exists():
            raise HTTPException(status_code=404, detail="视频文件不存在")

        from fastapi.responses import FileResponse
        return FileResponse(
            str(video_path),
            media_type="video/mp4",
            filename=filename
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载视频失败: {str(e)}")

@router.delete("/{video_id}")
async def delete_video(video_id: str):
    """删除视频"""
    try:
        # 查询和删除视频信息
        import psycopg2
        import psycopg2.extras

        try:
            conn = psycopg2.connect(os.getenv("DATABASE_URL"))
            cursor = conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor)

            # 查询视频信息
            cursor.execute("SELECT video_url FROM wanx_video_tasks WHERE task_id = %s", [video_id])
            result = cursor.fetchone()

            if result and result["video_url"]:
                # 删除视频文件
                filename = result["video_url"].split("/")[-1]
                video_path = WAN_OUTPUT_DIR / filename
                if video_path.exists():
                    video_path.unlink()

            # 删除数据库记录
            cursor.execute("DELETE FROM wanx_video_tasks WHERE task_id = %s", [video_id])
            conn.commit()

            cursor.close()
            conn.close()

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"删除操作失败: {str(e)}")

        return {
            "success": True,
            "message": "视频已删除"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除视频失败: {str(e)}")
