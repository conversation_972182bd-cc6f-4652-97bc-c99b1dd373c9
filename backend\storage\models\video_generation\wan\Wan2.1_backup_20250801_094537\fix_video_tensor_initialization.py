#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复WAN2.1模型生成空白视频的问题
主要解决：
1. 视频生成时latents和noise_pred全部为零导致的黑屏问题
2. 添加随机噪声初始化确保视频帧有内容
3. 修复cache_video函数保存失败的问题
"""

import os
import sys
import logging
import shutil
import tempfile
import numpy as np
import torch
import re
import random
import time
from pathlib import Path

# 配置日志
def setup_logging(log_dir='logs'):
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    log_file = os.path.join(log_dir, 'fix_video_tensor_initialization.log')
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

# 备份原文件
def backup_file(filepath):
    if not os.path.exists(filepath):
        logging.error(f"文件不存在: {filepath}")
        return False
    
    backup_path = f"{filepath}.bak.{int(time.time())}"
    shutil.copy2(filepath, backup_path)
    logging.info(f"已备份文件: {filepath} -> {backup_path}")
    return True

def fix_utils_py(logger):
    # 修正utils.py的路径，它在wan/utils/目录下而不是根目录
    utils_path = os.path.join("wan", "utils", "utils.py")
    if not os.path.exists(utils_path):
        logger.error(f"未找到 {utils_path}")
        return False

    logger.info(f"修改 {utils_path} 添加非零初始化...")
    
    with open(utils_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查cache_video函数是否存在
    if "def cache_video" in content:
        cache_video_pattern = r"def cache_video\([^)]*\):(.*?)(?=\n\S|\Z)"
        cache_video_match = re.search(cache_video_pattern, content, re.DOTALL)
        
        if cache_video_match:
            old_cache_video = cache_video_match.group(0)
            
            new_cache_video = '''def cache_video(video, path, h=None, w=None):
    """Video caching function, enhanced version: handles zero values and supports multiple saving methods"""
    logger = logging.getLogger("WAN-Utils")
    
    logger.info(f"最终视频帧形状: {video.shape}")
    
    # 确保视频在CPU上
    if isinstance(video, torch.Tensor):
        logger.info(f"将张量从GPU移至CPU")
        video = video.detach().cpu()
    
    # 检查视频数据类型和形状
    if isinstance(video, torch.Tensor):
        # 检查数据范围
        min_val = float(video.min())
        max_val = float(video.max())
        logger.info(f"自动检测数据范围: [{min_val}, {max_val}]")
        
        # 检查是否所有值都是相同的（可能是全黑或全白）
        if min_val == max_val:
            logger.warning(f"警告: 视频帧数据可能无效，所有值相同: {min_val}")
            print(f"警告: 视频帧数据可能无效，所有值相同: {min_val}")
            print(f"尝试添加小噪声以避免全黑视频")
            
            # 添加随机噪声
            video = video + torch.rand_like(video) * 0.05
            
            # 重新检查数据范围
            min_val = float(video.min())
            max_val = float(video.max())
        
        # 根据值范围进行归一化处理
        if 0 <= min_val and max_val <= 1:
            logger.info(f"检测到数据范围为0-1")
            # 转换到0-255范围
            logger.info(f"检测到视频数据范围为0-1，转换为0-255")
            video = (video * 255).clamp(0, 255).to(torch.uint8)
        elif -1 <= min_val < 0 and 0 < max_val <= 1:
            logger.info(f"检测到数据范围为-1到1，归一化到0-1再转换为0-255")
            video = ((video + 1) / 2 * 255).clamp(0, 255).to(torch.uint8)
        elif 0 <= min_val and max_val <= 255:
            logger.info(f"检测到数据范围可能为0-255，归一化到0-1")
            video = (video / 255.0)
            logger.info(f"检测到视频数据范围为0-1，转换为0-255")
            video = (video * 255).clamp(0, 255).to(torch.uint8)
    
    # 扩展帧数，确保有足够帧数用于生成有效视频
    num_frames = video.shape[0]
    if num_frames < 16:
        logger.warning(f"视频帧数过少 ({num_frames}), 增加到至少16帧")
        # 重复现有帧以达到至少16帧
        repeats = max(1, int(np.ceil(16 / num_frames)))
        video = video.repeat_interleave(repeats, dim=0)
        # 如果超出16帧，裁剪回16帧
        video = video[:16]
    
    logger.info(f"最终视频形状: {video.shape} - 准备保存到 {path}")
    
    try:
        # 创建临时目录保存帧
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"创建临时目录保存视频帧: {temp_dir}")
            
            # 保存每一帧为PNG图像
            for i in range(video.shape[0]):
                frame = video[i].numpy() if isinstance(video, torch.Tensor) else video[i]
                frame_path = os.path.join(temp_dir, f"frame_{i:04d}.png")
                import cv2
                cv2.imwrite(frame_path, frame)
            
            # 使用FFmpeg组合帧
            framerate = 8  # 设置合适的帧率
            ffmpeg_cmd = f"ffmpeg -y -framerate {framerate} -i {temp_dir}/frame_%04d.png -c:v libx264 -preset slow -crf 18 -pix_fmt yuv420p {path}"
            import subprocess
            subprocess.run(ffmpeg_cmd, shell=True, check=True)
            
            # 验证文件是否成功创建
            if os.path.exists(path) and os.path.getsize(path) > 0:
                logger.info(f"使用FFmpeg成功保存视频: {path}, 大小: {os.path.getsize(path)} 字节")
                return True
            else:
                logger.error(f"FFmpeg生成的视频文件无效: {path}")
                return False
    
    except Exception as e:
        logger.error(f"使用FFmpeg保存视频失败: {str(e)}")
        return False'''
            
            updated_content = content.replace(old_cache_video, new_cache_video)
            
            with open(utils_path, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            
            logger.info(f"成功修改 {utils_path} 中的 cache_video 函数")
            return True
        else:
            # 如果找不到已有的cache_video函数，可能需要添加一个新的
            logger.info(f"在 {utils_path} 中未找到 cache_video 函数，添加新函数")
            
            # 在文件末尾添加cache_video函数
            with open(utils_path, 'a', encoding='utf-8') as f:
                f.write("\n\n")
                f.write('''def cache_video(video, path, h=None, w=None):
    """Video caching function, enhanced version: handles zero values and supports multiple saving methods"""
    logger = logging.getLogger("WAN-Utils")
    
    logger.info(f"最终视频帧形状: {video.shape}")
    
    # 确保视频在CPU上
    if isinstance(video, torch.Tensor):
        logger.info(f"将张量从GPU移至CPU")
        video = video.detach().cpu()
    
    # 检查视频数据类型和形状
    if isinstance(video, torch.Tensor):
        # 检查数据范围
        min_val = float(video.min())
        max_val = float(video.max())
        logger.info(f"自动检测数据范围: [{min_val}, {max_val}]")
        
        # 检查是否所有值都是相同的（可能是全黑或全白）
        if min_val == max_val:
            logger.warning(f"警告: 视频帧数据可能无效，所有值相同: {min_val}")
            print(f"警告: 视频帧数据可能无效，所有值相同: {min_val}")
            print(f"尝试添加小噪声以避免全黑视频")
            
            # 添加随机噪声
            video = video + torch.rand_like(video) * 0.05
            
            # 重新检查数据范围
            min_val = float(video.min())
            max_val = float(video.max())
        
        # 根据值范围进行归一化处理
        if 0 <= min_val and max_val <= 1:
            logger.info(f"检测到数据范围为0-1")
            # 转换到0-255范围
            logger.info(f"检测到视频数据范围为0-1，转换为0-255")
            video = (video * 255).clamp(0, 255).to(torch.uint8)
        elif -1 <= min_val < 0 and 0 < max_val <= 1:
            logger.info(f"检测到数据范围为-1到1，归一化到0-1再转换为0-255")
            video = ((video + 1) / 2 * 255).clamp(0, 255).to(torch.uint8)
        elif 0 <= min_val and max_val <= 255:
            logger.info(f"检测到数据范围可能为0-255，归一化到0-1")
            video = (video / 255.0)
            logger.info(f"检测到视频数据范围为0-1，转换为0-255")
            video = (video * 255).clamp(0, 255).to(torch.uint8)
    
    # 扩展帧数，确保有足够帧数用于生成有效视频
    num_frames = video.shape[0]
    if num_frames < 16:
        logger.warning(f"视频帧数过少 ({num_frames}), 增加到至少16帧")
        # 重复现有帧以达到至少16帧
        repeats = max(1, int(np.ceil(16 / num_frames)))
        video = video.repeat_interleave(repeats, dim=0)
        # 如果超出16帧，裁剪回16帧
        video = video[:16]
    
    logger.info(f"最终视频形状: {video.shape} - 准备保存到 {path}")
    
    try:
        # 创建临时目录保存帧
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"创建临时目录保存视频帧: {temp_dir}")
            
            # 保存每一帧为PNG图像
            for i in range(video.shape[0]):
                frame = video[i].numpy() if isinstance(video, torch.Tensor) else video[i]
                frame_path = os.path.join(temp_dir, f"frame_{i:04d}.png")
                import cv2
                cv2.imwrite(frame_path, frame)
            
            # 使用FFmpeg组合帧
            framerate = 8  # 设置合适的帧率
            ffmpeg_cmd = f"ffmpeg -y -framerate {framerate} -i {temp_dir}/frame_%04d.png -c:v libx264 -preset slow -crf 18 -pix_fmt yuv420p {path}"
            import subprocess
            subprocess.run(ffmpeg_cmd, shell=True, check=True)
            
            # 验证文件是否成功创建
            if os.path.exists(path) and os.path.getsize(path) > 0:
                logger.info(f"使用FFmpeg成功保存视频: {path}, 大小: {os.path.getsize(path)} 字节")
                return True
            else:
                logger.error(f"FFmpeg生成的视频文件无效: {path}")
                return False
    
    except Exception as e:
        logger.error(f"使用FFmpeg保存视频失败: {str(e)}")
        return False''')
            
            logger.info(f"成功添加 cache_video 函数到 {utils_path}")
            return True
    else:
        logger.error(f"未在 {utils_path} 中找到 cache_video 函数")
        return False

def fix_generate_py(logger):
    generate_path = "generate.py"
    if not os.path.exists(generate_path):
        logger.error(f"未找到 {generate_path}")
        return False

    logger.info(f"修改 {generate_path} 添加非零初始化...")
    
    with open(generate_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查generate函数并添加非零初始化逻辑
    if "def generate" in content:
        # 查找t2v相关代码块
        t2v_code_pattern = r"if\s+\"t2v\"\s+in\s+args\.task\s+or\s+\"t2i\"\s+in\s+args\.task:"
        
        if re.search(t2v_code_pattern, content):
            # 找到代码段，添加视频帧预处理逻辑
            t2v_match = re.search(t2v_code_pattern, content)
            t2v_start_pos = t2v_match.start()
            
            # 找到视频生成代码部分，通常在try except块中
            video_gen_pattern = r"try:\s*\n\s*video\s*=\s*wan_t2v\.generate\([^)]*\)"
            video_gen_match = re.search(video_gen_pattern, content[t2v_start_pos:])
            
            if video_gen_match:
                # 找到形状不匹配处理代码
                shape_mismatch_pattern = r"if\s+'shape\s+mismatch'\s+in\s+str\(e\)\s+or\s+'size\s+mismatch'\s+in\s+str\(e\)\s+or"
                shape_mismatch_match = re.search(shape_mismatch_pattern, content[t2v_start_pos:])
                
                if shape_mismatch_match:
                    # 获取完整的except块
                    except_start = content[t2v_start_pos:].find("except RuntimeError as e:")
                    if except_start != -1:
                        except_start += t2v_start_pos
                        
                        # 查找except块的起始和结束
                        except_code = content[except_start:]
                        except_lines = except_code.split('\n')
                        
                        # 分析缩进来确定块的结束
                        except_indent = len(except_lines[0]) - len(except_lines[0].lstrip())
                        block_indent = len(except_lines[1]) - len(except_lines[1].lstrip())
                        
                        # 找到except块的结束
                        except_end = except_start
                        for i, line in enumerate(except_lines[1:], 1):
                            if line.strip() and (len(line) - len(line.lstrip()) <= except_indent):
                                except_end += len('\n'.join(except_lines[:i]))
                                break
                            except_end += len(line) + 1  # +1 for newline
                        
                        old_except_block = content[except_start:except_end]
                        
                        # 创建改进的except块，包括更好的噪声生成和形状调整
                        indent = ' ' * block_indent
                        new_except_block = f"""except RuntimeError as e:
{indent}if 'shape mismatch' in str(e) or 'size mismatch' in str(e) or '形状不匹配' in str(e):
{indent}    logging.warning(f"检测到形状不匹配问题: {{e}}")
{indent}    logging.info("使用随机噪声替代以继续生成")
{indent}    # 添加更详细的日志用于调试
{indent}    logging.info(f"尝试使用随机噪声重新生成视频，使用种子 {{args.base_seed + 1}}")
{indent}    
{indent}    # 不直接抛出异常，而是尝试使用随机噪声继续
{indent}    if "t2v" in args.task:
{indent}        return wan_t2v.generate(
{indent}            args.prompt,
{indent}            size=SIZE_CONFIGS[args.size],
{indent}            frame_num=args.frame_num,
{indent}            shift=args.sample_shift,
{indent}            sample_solver=args.sample_solver,
{indent}            sampling_steps=args.sample_steps,
{indent}            guide_scale=args.sample_guide_scale,
{indent}            seed=args.base_seed + 1,  # 使用不同的随机种子
{indent}            offload_model=args.offload_model,
{indent}            use_random_noise=True  # 使用完全随机的噪声
{indent}        )
{indent}else:
{indent}    raise"""
                        
                        # 替换except块
                        updated_content = content[:except_start] + new_except_block + content[except_end:]
                        
                        # 在if rank == 0:块添加视频预处理逻辑
                        save_pattern = r"if rank == 0:"
                        save_match = re.search(save_pattern, updated_content)
                        
                        if save_match:
                            save_pos = save_match.end()
                            
                            # 在现有保存代码前添加预处理逻辑
                            preprocess_code = """
    # 预处理视频张量，确保数据有效
    if "t2v" in args.task or "i2v" in args.task or "flf2v" in args.task:
        logging.info(f"预处理视频张量，确保数据有效...")
        
        # 确保video张量不为空
        if video is None:
            logging.error("视频张量为空，无法保存")
            return None
            
        # 记录原始视频数据信息
        logging.info(f"原始视频张量: 形状={video.shape}, 类型={video.dtype}, 值范围=[{video.min().item()}, {video.max().item()}]")
        
        # 检查是否所有值都相同（可能是全黑或全白）
        if video.min().item() == video.max().item():
            logging.warning(f"警告: 视频帧数据可能无效，所有值相同: {video.min().item()}")
            
            # 添加随机噪声以避免全黑视频
            logging.info("添加随机噪声以避免全黑视频")
            video = video + torch.rand_like(video) * 0.05
            
            # 重新检查数据范围
            logging.info(f"添加噪声后的视频数据范围: [{video.min().item()}, {video.max().item()}]")
        
        # 确保视频帧数足够，避免生成过短的视频
        if video.shape[0] < 16:
            logging.warning(f"视频帧数过少 ({video.shape[0]}), 增加到至少16帧")
            # 重复现有帧以达到至少16帧
            repeats = max(1, int(np.ceil(16 / video.shape[0])))
            video = video.repeat_interleave(repeats, dim=0)
            # 如果超出16帧，裁剪回16帧
            video = video[:16]
            logging.info(f"调整后的视频帧数: {video.shape[0]}")"""
                            
                            # 插入预处理代码
                            updated_content = updated_content[:save_pos] + preprocess_code + updated_content[save_pos:]
                        
                        # 保存修改后的文件
                        with open(generate_path, 'w', encoding='utf-8') as f:
                            f.write(updated_content)
                        
                        logger.info(f"成功修改 {generate_path} 中的视频生成代码")
                        return True
                    else:
                        logger.error(f"未在 {generate_path} 中找到完整的except块")
                else:
                    logger.error(f"未在 {generate_path} 中找到形状不匹配处理代码")
            else:
                logger.error(f"未在 {generate_path} 中找到视频生成代码")
        else:
            logger.error(f"未在 {generate_path} 中找到t2v相关代码块")
    else:
        logger.error(f"未在 {generate_path} 中找到 generate 函数")
        return False

def main():
    logger = setup_logging()
    logger.info("开始修复WAN2.1模型生成空白视频的问题...")
    
    # 检查当前工作目录是否为WAN2.1目录
    if not (os.path.exists("generate.py") and os.path.exists(os.path.join("wan", "utils"))):
        logger.error("当前目录不是WAN2.1模型目录，请在WAN2.1目录下运行此脚本")
        return False
    
    # 创建wan/utils目录（如果不存在）
    os.makedirs(os.path.join("wan", "utils"), exist_ok=True)
    
    # 备份原始文件
    backup_file("generate.py")
    utils_path = os.path.join("wan", "utils", "utils.py")
    if os.path.exists(utils_path):
        backup_file(utils_path)
    
    # 修复utils.py中的cache_video函数
    if not fix_utils_py(logger):
        logger.error("修复utils.py失败")
        return False
    
    # 修复generate.py中的非零初始化
    if not fix_generate_py(logger):
        logger.error("修复generate.py失败")
        return False
    
    logger.info("修复完成，可以重新运行generate.py生成视频")
    return True

if __name__ == "__main__":
    main() 