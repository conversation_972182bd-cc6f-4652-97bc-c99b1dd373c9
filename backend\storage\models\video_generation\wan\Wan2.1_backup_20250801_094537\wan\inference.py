import torch
import logging
import os
import random
import numpy as np
from .pretrained import build_model, prepare_inputs

# 初始化日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('WAN-Inference')

def inference_wan(
    text, 
    height=576, 
    width=1024, 
    length=16, 
    version="2.1", 
    cfg=8.0, 
    steps=50, 
    use_chinese=False, 
    seed=None, 
    enhance=False,
    device="cuda"
):
    """
    使用WAN模型根据文本生成视频
    
    参数:
        text (str): 描述视频内容的文本
        height (int): 视频高度，必须是64的倍数
        width (int): 视频宽度，必须是64的倍数
        length (int): 视频帧数
        version (str): 模型版本，默认"2.1"
        cfg (float): 分类器自由引导系数，默认8.0
        steps (int): 采样步数，默认50
        use_chinese (bool): 是否使用中文提示词，默认False
        seed (int): 随机种子，默认None随机生成
        enhance (bool): 是否启用增强处理，默认False
        device (str): 计算设备，默认"cuda"
        
    返回:
        torch.Tensor: 生成的视频张量，格式为(T, H, W, C)
    """
    # 参数验证
    if height % 64 != 0 or width % 64 != 0:
        raise ValueError("高度和宽度必须是64的倍数")
    
    # 设置随机种子
    if seed is None:
        seed = random.randint(0, 2**32 - 1)
    
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    
    logger.info(f"使用随机种子: {seed}")
    logger.info(f"生成视频尺寸: {width}x{height}, {length}帧")
    
    # 构建模型参数
    model_kwargs = {
        "version": version,
        "device": device,
        "dtype": torch.float16 if device == "cuda" else torch.float32
    }
    
    # 载入模型
    logger.info(f"加载WAN {version}模型")
    model = build_model(**model_kwargs)
    
    # 准备输入
    prompt_kwargs = {
        "prompt": text,
        "lang": "zh" if use_chinese else "en",
        "width": width,
        "height": height,
        "frames": length,
        "enhance": enhance
    }
    
    # 准备生成参数
    sampling_kwargs = {
        "steps": steps,
        "cfg_scale": cfg,
        "seed": seed
    }
    
    # 准备输入
    logger.info(f"准备输入 (文本: '{text}')")
    x = prepare_inputs(**prompt_kwargs)
    
    # 执行生成
    logger.info(f"开始生成 (步数: {steps}, 引导系数: {cfg})")
    with torch.no_grad():
        output = model.generate(x, **sampling_kwargs)
    
    # 转换输出格式
    if hasattr(output, "permute"):
        # 如果是张量，确保格式为 (T, H, W, C)
        if output.shape[0] == 3 and output.shape[-1] != 3:  # (C, T, H, W) 格式
            output = output.permute(1, 2, 3, 0)
        elif output.shape[1] == 3 and output.shape[-1] != 3:  # (T, C, H, W) 格式
            output = output.permute(0, 2, 3, 1)
    
    logger.info(f"生成完成，输出张量形状: {output.shape}")
    return output 