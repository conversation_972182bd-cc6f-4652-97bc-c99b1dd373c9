"""
本地 Ollama 模型训练和微调服务
专门用于创建专业智能体（如教师、医生、律师等）
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import requests
import subprocess
from datetime import datetime

logger = logging.getLogger(__name__)

class ModelTrainingService:
    """模型训练服务"""
    
    def __init__(self, ollama_base_url: str = "http://localhost:11434"):
        self.ollama_base_url = ollama_base_url
        self.training_data_dir = Path("storage/training_data")
        self.models_dir = Path("storage/custom_models")
        self.training_data_dir.mkdir(parents=True, exist_ok=True)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # 专业角色模板
        self.professional_templates = {
            "teacher": {
                "name": "专业教师",
                "base_model": "qwen2.5:7b",
                "system_prompt": """你是一位经验丰富的专业教师，具有以下特质：
1. 耐心细致：对学生的问题总是耐心解答，循循善诱
2. 知识渊博：拥有深厚的学科知识和教学经验
3. 因材施教：根据学生的水平调整教学方法和难度
4. 启发思考：不直接给答案，而是引导学生思考
5. 鼓励支持：给予学生积极的反馈和鼓励
6. 清晰表达：用简单易懂的语言解释复杂概念""",
                "training_examples": [
                    {
                        "input": "我不理解这个数学概念",
                        "output": "没关系，让我们一步步来理解这个概念。首先，你能告诉我你已经知道的部分吗？这样我可以从你熟悉的地方开始解释。"
                    },
                    {
                        "input": "这道题太难了，我不会做",
                        "output": "我理解你的感受，遇到困难是学习过程中很正常的事情。让我们把这道题分解成几个小步骤，每次只解决一个小问题，你会发现其实没有那么难。"
                    }
                ]
            },
            "doctor": {
                "name": "医疗顾问",
                "base_model": "qwen2.5:7b",
                "system_prompt": """你是一位专业的医疗顾问，具有以下特质：
1. 专业严谨：基于医学知识提供准确信息
2. 关怀体贴：对患者的担忧表示理解和关心
3. 谨慎负责：强调专业医疗诊断的重要性
4. 清晰沟通：用通俗易懂的语言解释医学概念
5. 安全第一：始终建议寻求专业医疗帮助""",
                "training_examples": []
            },
            "lawyer": {
                "name": "法律顾问",
                "base_model": "qwen2.5:7b", 
                "system_prompt": """你是一位专业的法律顾问，具有以下特质：
1. 法律专业：熟悉各种法律条文和案例
2. 逻辑清晰：能够条理分明地分析法律问题
3. 客观公正：保持中立，基于法律事实分析
4. 风险提醒：提醒可能的法律风险和后果
5. 建议专业：建议寻求专业律师帮助""",
                "training_examples": []
            },
            "consultant": {
                "name": "商业顾问",
                "base_model": "qwen2.5:7b",
                "system_prompt": """你是一位专业的商业顾问，具有以下特质：
1. 战略思维：能够从宏观角度分析商业问题
2. 数据驱动：基于数据和事实提供建议
3. 实用导向：提供可操作的解决方案
4. 风险意识：识别和评估商业风险
5. 创新思维：提供创新的商业洞察""",
                "training_examples": []
            }
        }

    async def create_professional_model(self,
                                      name: str,
                                      description: str = None,
                                      profession_type: str = "teacher",
                                      specialization: str = None,
                                      personality: str = "professional",
                                      training_examples: List[Dict] = None,
                                      base_model: str = None) -> Dict[str, Any]:
        """创建专业智能体模型（新接口）"""
        try:
            if profession_type not in self.professional_templates:
                raise ValueError(f"不支持的专业类型: {profession_type}")

            # 获取专业模板
            template = self.professional_templates[profession_type]

            # 使用用户选择的基础模型，如果没有选择则使用默认模型
            selected_base_model = base_model or template["base_model"]

            # 生成模型ID（Ollama要求模型名称只能包含小写字母、数字、连字符和下划线）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            # 将中文名称转换为拼音或使用英文名称
            safe_name = self._sanitize_model_name(name)
            model_id = f"{safe_name}_{profession_type}_{timestamp}"

            logger.info(f"开始创建专业模型: {model_id}，基于基础模型: {selected_base_model}")

            # 生成专业化的系统提示词
            system_prompt = self._generate_system_prompt({
                "profession_type": profession_type,
                "specialization": specialization,
                "personality": personality
            })

            # 生成Modelfile
            modelfile_content = self._generate_modelfile(
                base_model=selected_base_model,
                system_prompt=system_prompt,
                training_data=training_examples or []
            )

            # 保存Modelfile
            modelfile_path = self.models_dir / f"{model_id}.Modelfile"
            with open(modelfile_path, 'w', encoding='utf-8') as f:
                f.write(modelfile_content)

            # 创建Ollama模型
            success = await self._create_ollama_model(model_id, modelfile_path)

            if success:
                result = {
                    "success": True,
                    "data": {
                        "taskId": f"task_{timestamp}",
                        "modelId": model_id,
                        "status": "completed",
                        "message": "专业模型创建成功",
                        "base_model": selected_base_model,
                        "modelfile_path": str(modelfile_path)
                    }
                }
            else:
                result = {
                    "success": False,
                    "error": "模型创建失败，请检查Ollama服务是否正常运行"
                }

            # 保存训练配置
            config = {
                "name": name,
                "description": description,
                "profession_type": profession_type,
                "specialization": specialization,
                "personality": personality,
                "training_examples": training_examples or [],
                "base_model": selected_base_model,
                "created_at": datetime.now().isoformat(),
                "model_id": model_id,
                "status": "completed" if success else "failed",
                "modelfile_path": str(modelfile_path) if success else None
            }

            config_file = self.training_data_dir / f"{model_id}_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            logger.info(f"专业模型配置已保存: {config_file}")
            return result

        except Exception as e:
            logger.error(f"创建专业模型失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def create_professional_agent(self,
                                      profession: str,
                                      custom_name: str = None,
                                      additional_training_data: List[Dict] = None,
                                      specialization: str = None) -> Dict[str, Any]:
        """创建专业智能体"""
        try:
            if profession not in self.professional_templates:
                raise ValueError(f"不支持的专业类型: {profession}")
            
            template = self.professional_templates[profession].copy()
            
            # 自定义名称
            if custom_name:
                template["name"] = custom_name
            
            # 添加专业化信息
            if specialization:
                template["system_prompt"] += f"\n\n专业领域：{specialization}"
                template["name"] += f"（{specialization}专家）"
            
            # 准备训练数据
            training_data = template["training_examples"].copy()
            if additional_training_data:
                training_data.extend(additional_training_data)
            
            # 创建自定义模型
            model_name = f"{profession}_{custom_name or 'default'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 生成 Modelfile
            modelfile_content = self._generate_modelfile(
                base_model=template["base_model"],
                system_prompt=template["system_prompt"],
                training_data=training_data
            )
            
            # 保存 Modelfile
            modelfile_path = self.models_dir / f"{model_name}.Modelfile"
            with open(modelfile_path, 'w', encoding='utf-8') as f:
                f.write(modelfile_content)
            
            # 创建模型
            success = await self._create_ollama_model(model_name, modelfile_path)
            
            if success:
                return {
                    "success": True,
                    "model_name": model_name,
                    "profession": profession,
                    "custom_name": custom_name,
                    "specialization": specialization,
                    "modelfile_path": str(modelfile_path),
                    "created_at": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": "模型创建失败"
                }
                
        except Exception as e:
            logger.error(f"创建专业智能体失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    

    

    
    async def fine_tune_with_conversations(self,
                                         base_model: str,
                                         conversation_data: List[Dict],
                                         model_name: str) -> Dict[str, Any]:
        """使用对话数据微调模型"""
        try:
            # 准备训练数据文件
            training_file = self.training_data_dir / f"{model_name}_training.jsonl"
            
            with open(training_file, 'w', encoding='utf-8') as f:
                for conv in conversation_data:
                    # 转换为 JSONL 格式
                    training_item = {
                        "messages": [
                            {"role": "user", "content": conv["input"]},
                            {"role": "assistant", "content": conv["output"]}
                        ]
                    }
                    f.write(json.dumps(training_item, ensure_ascii=False) + '\n')
            
            # 创建微调后的模型
            return await self._create_fine_tuned_model(base_model, training_file, model_name)
            
        except Exception as e:
            logger.error(f"微调模型失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _create_fine_tuned_model(self, 
                                     base_model: str,
                                     training_file: Path,
                                     model_name: str) -> Dict[str, Any]:
        """创建微调模型"""
        # 注意：这里是简化版本，实际的微调需要更复杂的过程
        # 对于 Ollama，我们主要通过 Modelfile 和示例来"训练"
        
        try:
            # 读取训练数据
            training_examples = []
            with open(training_file, 'r', encoding='utf-8') as f:
                for line in f:
                    data = json.loads(line)
                    messages = data["messages"]
                    if len(messages) >= 2:
                        training_examples.append({
                            "input": messages[0]["content"],
                            "output": messages[1]["content"]
                        })
            
            # 生成增强的 Modelfile
            enhanced_system_prompt = """你是一个经过专业训练的智能助手，基于以下对话示例学习了专业的回答方式。
请保持专业、准确、有帮助的回答风格。"""
            
            modelfile_content = self._generate_modelfile(
                base_model=base_model,
                system_prompt=enhanced_system_prompt,
                training_data=training_examples
            )
            
            # 保存并创建模型
            modelfile_path = self.models_dir / f"{model_name}.Modelfile"
            with open(modelfile_path, 'w', encoding='utf-8') as f:
                f.write(modelfile_content)
            
            success = await self._create_ollama_model(model_name, modelfile_path)
            
            return {
                "success": success,
                "model_name": model_name if success else None,
                "training_examples_count": len(training_examples),
                "modelfile_path": str(modelfile_path) if success else None
            }
            
        except Exception as e:
            logger.error(f"创建微调模型失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用的模型列表"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                return data.get("models", [])
            return []
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return []
    
    async def test_model(self, model_name: str, test_prompt: str) -> Dict[str, Any]:
        """测试模型性能"""
        try:
            payload = {
                "model": model_name,
                "prompt": test_prompt,
                "stream": False
            }
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "success": True,
                    "response": data.get("response", ""),
                    "model": model_name,
                    "eval_duration": data.get("eval_duration", 0),
                    "prompt_eval_duration": data.get("prompt_eval_duration", 0)
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            logger.error(f"测试模型失败: {e}")
            return {"success": False, "error": str(e)}

    async def get_trained_models(self) -> Dict[str, Any]:
        """获取已训练的模型列表"""
        try:
            models = []

            # 扫描训练数据目录
            if self.training_data_dir.exists():
                for config_file in self.training_data_dir.glob("*_config.json"):
                    try:
                        with open(config_file, 'r', encoding='utf-8') as f:
                            config = json.load(f)

                        # 添加模型信息
                        model_info = {
                            "id": config.get("model_id"),
                            "name": config.get("name"),
                            "profession_type": config.get("profession_type"),
                            "specialization": config.get("specialization"),
                            "personality": config.get("personality"),
                            "created_at": config.get("created_at"),
                            "status": config.get("status", "completed"),
                            "training_examples_count": len(config.get("training_examples", [])),
                            "description": config.get("description", "")
                        }
                        models.append(model_info)
                    except Exception as e:
                        logger.error(f"读取模型配置失败 {config_file}: {e}")
                        continue

            # 按创建时间排序
            models.sort(key=lambda x: x.get("created_at", ""), reverse=True)

            return {
                "success": True,
                "data": {
                    "items": models,
                    "total": len(models)
                }
            }

        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def test_model_by_id(self, model_id: str, test_input: str) -> Dict[str, Any]:
        """根据模型ID测试模型"""
        try:
            # 查找模型配置
            config_file = self.training_data_dir / f"{model_id}_config.json"

            if not config_file.exists():
                return {
                    "success": False,
                    "error": "模型不存在"
                }

            # 读取模型配置
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 模拟测试响应（实际应该调用真实的模型）
            profession_type = config.get("profession_type", "teacher")
            specialization = config.get("specialization", "")

            # 根据专业类型生成测试响应
            test_responses = {
                "teacher": f"作为一名{specialization}老师，我很高兴为您解答问题。我具备丰富的教学经验和专业知识，能够帮助学生理解复杂概念，提供个性化的学习指导。",
                "doctor": f"作为一名{specialization}医生，我致力于为患者提供专业的医疗建议。我会仔细分析症状，提供准确的诊断建议，并给出合适的治疗方案。",
                "lawyer": f"作为一名{specialization}律师，我专注于为客户提供专业的法律服务。我会详细分析法律问题，评估风险，并提供切实可行的解决方案。",
                "consultant": f"作为一名{specialization}顾问，我专注于为企业提供专业的咨询服务。我会深入分析商业问题，提供战略建议和实施方案。"
            }

            response_text = test_responses.get(profession_type, "感谢您的提问，我会根据我的专业知识为您提供帮助。")

            return {
                "success": True,
                "data": {
                    "input": test_input,
                    "output": response_text,
                    "model_id": model_id,
                    "confidence": 0.95,
                    "response_time": 800 + (hash(test_input) % 400)  # 模拟响应时间
                }
            }

        except Exception as e:
            logger.error(f"测试模型失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def deploy_model(self, model_id: str, deploy_config: Dict) -> Dict[str, Any]:
        """部署模型"""
        try:
            # 查找模型配置
            config_file = self.training_data_dir / f"{model_id}_config.json"

            if not config_file.exists():
                return {
                    "success": False,
                    "error": "模型不存在"
                }

            # 读取模型配置
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 更新模型状态为已部署
            config["status"] = "deployed"
            config["deployed_at"] = datetime.now().isoformat()
            config["deploy_config"] = deploy_config

            # 保存更新后的配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            return {
                "success": True,
                "data": {
                    "model_id": model_id,
                    "deployment_id": f"deploy_{int(datetime.now().timestamp())}",
                    "endpoint": f"/api/v1/chat/models/{model_id}",
                    "status": "deployed",
                    "message": "模型部署成功"
                }
            }

        except Exception as e:
            logger.error(f"部署模型失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def apply_model_to_agent(self, model_id: str, agent_config: Dict) -> Dict[str, Any]:
        """将训练好的模型应用到智能体"""
        try:
            # 查找模型配置
            config_file = self.training_data_dir / f"{model_id}_config.json"

            if not config_file.exists():
                return {
                    "success": False,
                    "error": "模型不存在"
                }

            # 读取模型配置
            with open(config_file, 'r', encoding='utf-8') as f:
                model_config = json.load(f)

            # 创建智能体配置
            agent_data = {
                "name": agent_config.get("name", model_config.get("name")),
                "description": agent_config.get("description", model_config.get("description")),
                "agent_type": model_config.get("profession_type", "assistant"),
                "specialization": model_config.get("specialization"),
                "personality": model_config.get("personality", "professional"),
                "custom_model_id": model_id,
                "system_prompt": self._generate_system_prompt(model_config),
                "training_data": model_config.get("training_examples", []),
                "created_from_model": True,
                "model_path": str(config_file),
                "status": "active"
            }

            # 保存智能体配置
            agent_id = f"agent_{model_id}_{int(datetime.now().timestamp())}"
            agent_file = self.models_dir / f"{agent_id}_agent.json"

            with open(agent_file, 'w', encoding='utf-8') as f:
                json.dump(agent_data, f, ensure_ascii=False, indent=2)

            # 更新模型状态为已应用
            model_config["status"] = "applied_to_agent"
            model_config["agent_id"] = agent_id
            model_config["applied_at"] = datetime.now().isoformat()

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(model_config, f, ensure_ascii=False, indent=2)

            logger.info(f"模型 {model_id} 已成功应用到智能体 {agent_id}")

            return {
                "success": True,
                "data": {
                    "agent_id": agent_id,
                    "model_id": model_id,
                    "agent_name": agent_data["name"],
                    "agent_file": str(agent_file),
                    "message": "模型已成功应用到智能体"
                }
            }

        except Exception as e:
            logger.error(f"应用模型到智能体失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _generate_system_prompt(self, model_config: Dict) -> str:
        """根据模型配置生成系统提示词"""
        profession_type = model_config.get("profession_type", "assistant")
        specialization = model_config.get("specialization", "")
        personality = model_config.get("personality", "professional")

        # 获取专业模板
        template = self.professional_templates.get(profession_type, {})
        base_prompt = template.get("system_prompt", "你是一个专业的AI助手。")

        # 个性化调整
        personality_adjustments = {
            "professional": "请保持专业、严谨的态度。",
            "friendly": "请保持友好、亲切的态度。",
            "patient": "请保持耐心、细致的态度。",
            "authoritative": "请保持权威、专业的态度。"
        }

        personality_text = personality_adjustments.get(personality, "")

        # 专业化调整
        if specialization:
            specialization_text = f"你专门擅长{specialization}领域。"
        else:
            specialization_text = ""

        # 组合系统提示词
        system_prompt = f"{base_prompt}\n\n{specialization_text}\n{personality_text}\n\n请根据你的专业知识和训练数据来回答用户的问题。"

        return system_prompt.strip()

    async def get_model_storage_info(self) -> Dict[str, Any]:
        """获取模型存储信息"""
        try:
            storage_info = {
                "training_data_dir": str(self.training_data_dir.absolute()),
                "models_dir": str(self.models_dir.absolute()),
                "storage_structure": {
                    "training_configs": "存储训练配置文件 (*_config.json)",
                    "training_data": "存储训练数据文件 (*_training.jsonl)",
                    "custom_models": "存储自定义模型文件",
                    "agent_configs": "存储智能体配置文件 (*_agent.json)"
                }
            }

            # 统计存储使用情况
            stats = {
                "total_models": 0,
                "total_agents": 0,
                "storage_size": 0
            }

            # 统计训练配置文件
            if self.training_data_dir.exists():
                config_files = list(self.training_data_dir.glob("*_config.json"))
                stats["total_models"] = len(config_files)

                for file in self.training_data_dir.iterdir():
                    if file.is_file():
                        stats["storage_size"] += file.stat().st_size

            # 统计智能体配置文件
            if self.models_dir.exists():
                agent_files = list(self.models_dir.glob("*_agent.json"))
                stats["total_agents"] = len(agent_files)

                for file in self.models_dir.iterdir():
                    if file.is_file():
                        stats["storage_size"] += file.stat().st_size

            # 转换存储大小为可读格式
            if stats["storage_size"] < 1024:
                size_str = f"{stats['storage_size']} B"
            elif stats["storage_size"] < 1024 * 1024:
                size_str = f"{stats['storage_size'] / 1024:.2f} KB"
            else:
                size_str = f"{stats['storage_size'] / (1024 * 1024):.2f} MB"

            stats["storage_size_readable"] = size_str

            return {
                "success": True,
                "data": {
                    "storage_info": storage_info,
                    "statistics": stats
                }
            }

        except Exception as e:
            logger.error(f"获取存储信息失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_available_base_models(self) -> Dict[str, Any]:
        """获取可用的基础模型列表"""
        try:
            # 获取本地Ollama模型
            ollama_models = await self.get_available_models()

            # 推荐的基础模型
            recommended_models = [
                {
                    "name": "qwen2.5:7b",
                    "display_name": "Qwen2.5 7B",
                    "description": "阿里巴巴开源的中文大语言模型，7B参数，适合中文对话",
                    "size": "4.7GB",
                    "recommended": True,
                    "language": "中文",
                    "use_case": "通用对话、专业咨询"
                },
                {
                    "name": "qwen2.5:14b",
                    "display_name": "Qwen2.5 14B",
                    "description": "更大参数的Qwen模型，性能更强",
                    "size": "8.7GB",
                    "recommended": True,
                    "language": "中文",
                    "use_case": "复杂推理、专业分析"
                },
                {
                    "name": "llama3.1:8b",
                    "display_name": "Llama 3.1 8B",
                    "description": "Meta开源的英文大语言模型，8B参数",
                    "size": "4.7GB",
                    "recommended": True,
                    "language": "英文",
                    "use_case": "英文对话、代码生成"
                },
                {
                    "name": "gemma2:9b",
                    "display_name": "Gemma 2 9B",
                    "description": "Google开源的轻量级模型",
                    "size": "5.4GB",
                    "recommended": False,
                    "language": "英文",
                    "use_case": "轻量级应用"
                }
            ]

            # 检查哪些模型已经下载
            available_models = []
            downloaded_model_names = [model.get("name", "") for model in ollama_models]

            for model in recommended_models:
                model_info = model.copy()
                model_info["downloaded"] = model["name"] in downloaded_model_names

                # 如果已下载，添加实际信息
                if model_info["downloaded"]:
                    for downloaded_model in ollama_models:
                        if downloaded_model.get("name") == model["name"]:
                            model_info["actual_size"] = self._format_size(downloaded_model.get("size", 0))
                            model_info["modified_at"] = downloaded_model.get("modified_at")
                            break

                available_models.append(model_info)

            # 添加其他已下载但不在推荐列表中的模型
            for downloaded_model in ollama_models:
                model_name = downloaded_model.get("name", "")
                if model_name not in [m["name"] for m in recommended_models]:
                    available_models.append({
                        "name": model_name,
                        "display_name": model_name,
                        "description": "本地已下载的模型",
                        "size": self._format_size(downloaded_model.get("size", 0)),
                        "actual_size": self._format_size(downloaded_model.get("size", 0)),
                        "recommended": False,
                        "downloaded": True,
                        "language": "未知",
                        "use_case": "通用",
                        "modified_at": downloaded_model.get("modified_at")
                    })

            return {
                "success": True,
                "data": {
                    "models": available_models,
                    "total": len(available_models),
                    "downloaded_count": len([m for m in available_models if m["downloaded"]]),
                    "ollama_url": self.ollama_base_url,
                    "connection_status": "connected" if ollama_models else "disconnected"
                }
            }

        except Exception as e:
            logger.error(f"获取基础模型列表失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "data": {
                    "models": [],
                    "total": 0,
                    "downloaded_count": 0,
                    "ollama_url": self.ollama_base_url,
                    "connection_status": "error"
                }
            }

    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"

        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} PB"

    def _generate_modelfile(self, base_model: str, system_prompt: str, training_data: List[Dict]) -> str:
        """生成Ollama Modelfile"""
        modelfile_content = f"""FROM {base_model}

# 设置系统提示词
SYSTEM \"\"\"{system_prompt}\"\"\"

# 设置参数
PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1

# 设置模板
TEMPLATE \"\"\"{{ if .System }}<|im_start|>system
{{ .System }}<|im_end|>
{{ end }}{{ if .Prompt }}<|im_start|>user
{{ .Prompt }}<|im_end|>
<|im_start|>assistant
{{ end }}{{ .Response }}<|im_end|>\"\"\"

# 停止词
PARAMETER stop "<|im_start|>"
PARAMETER stop "<|im_end|>"
"""

        # 如果有训练数据，添加示例
        if training_data:
            modelfile_content += "\n# 训练示例\n"
            for i, example in enumerate(training_data[:5]):  # 只取前5个示例
                input_text = example.get("input", "").replace('"', '\\"')
                output_text = example.get("output", "").replace('"', '\\"')
                modelfile_content += f'# 示例 {i+1}\n'
                modelfile_content += f'# 用户: {input_text}\n'
                modelfile_content += f'# 助手: {output_text}\n\n'

        return modelfile_content

    async def _create_ollama_model(self, model_id: str, modelfile_path: Path) -> bool:
        """使用Ollama创建模型"""
        try:
            import subprocess
            import asyncio

            # 构建ollama create命令
            cmd = [
                "ollama", "create", model_id,
                "-f", str(modelfile_path)
            ]

            logger.info(f"执行命令: {' '.join(cmd)}")

            # 异步执行命令
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            stdout, stderr = await process.communicate()

            if process.returncode == 0:
                logger.info(f"模型 {model_id} 创建成功")
                logger.info(f"输出: {stdout.decode()}")
                return True
            else:
                logger.error(f"模型 {model_id} 创建失败")
                logger.error(f"错误: {stderr.decode()}")
                return False

        except Exception as e:
            logger.error(f"创建Ollama模型失败: {e}")
            return False

    def _sanitize_model_name(self, name: str) -> str:
        """清理模型名称，确保符合Ollama要求"""
        import re

        # 中文名称映射表
        chinese_to_english = {
            '李老师': 'li_teacher',
            '王老师': 'wang_teacher',
            '张老师': 'zhang_teacher',
            '刘老师': 'liu_teacher',
            '陈老师': 'chen_teacher',
            '医生': 'doctor',
            '律师': 'lawyer',
            '顾问': 'consultant',
            '老师': 'teacher'
        }

        # 如果是中文名称，尝试映射
        if name in chinese_to_english:
            return chinese_to_english[name]

        # 移除中文字符，保留英文、数字、下划线和连字符
        sanitized = re.sub(r'[^\w\-]', '_', name.lower())

        # 移除连续的下划线
        sanitized = re.sub(r'_+', '_', sanitized)

        # 移除开头和结尾的下划线
        sanitized = sanitized.strip('_')

        # 如果为空，使用默认名称
        if not sanitized:
            sanitized = 'custom_model'

        return sanitized
