"""
本地 Ollama 模型训练和微调服务
专门用于创建专业智能体（如教师、医生、律师等）
"""

import os
import json
import asyncio
import logging
from typing import Dict, List, Any, Optional
from pathlib import Path
import requests
import subprocess
from datetime import datetime

logger = logging.getLogger(__name__)

class ModelTrainingService:
    """模型训练服务"""
    
    def __init__(self, ollama_base_url: str = "http://localhost:11434"):
        self.ollama_base_url = ollama_base_url
        self.training_data_dir = Path("storage/training_data")
        self.models_dir = Path("storage/custom_models")
        self.training_data_dir.mkdir(parents=True, exist_ok=True)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # 专业角色模板
        self.professional_templates = {
            "teacher": {
                "name": "专业教师",
                "base_model": "qwen2.5:7b",
                "system_prompt": """你是一位经验丰富的专业教师，具有以下特质：
1. 耐心细致：对学生的问题总是耐心解答，循循善诱
2. 知识渊博：拥有深厚的学科知识和教学经验
3. 因材施教：根据学生的水平调整教学方法和难度
4. 启发思考：不直接给答案，而是引导学生思考
5. 鼓励支持：给予学生积极的反馈和鼓励
6. 清晰表达：用简单易懂的语言解释复杂概念""",
                "training_examples": [
                    {
                        "input": "我不理解这个数学概念",
                        "output": "没关系，让我们一步步来理解这个概念。首先，你能告诉我你已经知道的部分吗？这样我可以从你熟悉的地方开始解释。"
                    },
                    {
                        "input": "这道题太难了，我不会做",
                        "output": "我理解你的感受，遇到困难是学习过程中很正常的事情。让我们把这道题分解成几个小步骤，每次只解决一个小问题，你会发现其实没有那么难。"
                    }
                ]
            },
            "doctor": {
                "name": "医疗顾问",
                "base_model": "qwen2.5:7b",
                "system_prompt": """你是一位专业的医疗顾问，具有以下特质：
1. 专业严谨：基于医学知识提供准确信息
2. 关怀体贴：对患者的担忧表示理解和关心
3. 谨慎负责：强调专业医疗诊断的重要性
4. 清晰沟通：用通俗易懂的语言解释医学概念
5. 安全第一：始终建议寻求专业医疗帮助""",
                "training_examples": []
            },
            "lawyer": {
                "name": "法律顾问",
                "base_model": "qwen2.5:7b", 
                "system_prompt": """你是一位专业的法律顾问，具有以下特质：
1. 法律专业：熟悉各种法律条文和案例
2. 逻辑清晰：能够条理分明地分析法律问题
3. 客观公正：保持中立，基于法律事实分析
4. 风险提醒：提醒可能的法律风险和后果
5. 建议专业：建议寻求专业律师帮助""",
                "training_examples": []
            },
            "consultant": {
                "name": "商业顾问",
                "base_model": "qwen2.5:7b",
                "system_prompt": """你是一位专业的商业顾问，具有以下特质：
1. 战略思维：能够从宏观角度分析商业问题
2. 数据驱动：基于数据和事实提供建议
3. 实用导向：提供可操作的解决方案
4. 风险意识：识别和评估商业风险
5. 创新思维：提供创新的商业洞察""",
                "training_examples": []
            }
        }

    async def create_professional_model(self,
                                      name: str,
                                      description: str = None,
                                      profession_type: str = "teacher",
                                      specialization: str = None,
                                      personality: str = "professional",
                                      training_examples: List[Dict] = None) -> Dict[str, Any]:
        """创建专业智能体模型（新接口）"""
        try:
            if profession_type not in self.professional_templates:
                raise ValueError(f"不支持的专业类型: {profession_type}")

            # 生成模型ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            model_id = f"{name}_{profession_type}_{timestamp}"

            # 模拟训练过程
            logger.info(f"开始创建专业模型: {model_id}")

            # 这里可以添加真实的模型训练逻辑
            # 目前返回模拟结果
            result = {
                "success": True,
                "data": {
                    "taskId": f"task_{timestamp}",
                    "modelId": model_id,
                    "status": "training",
                    "message": "专业模型创建已开始"
                }
            }

            # 保存训练配置
            config = {
                "name": name,
                "description": description,
                "profession_type": profession_type,
                "specialization": specialization,
                "personality": personality,
                "training_examples": training_examples or [],
                "created_at": datetime.now().isoformat(),
                "model_id": model_id,
                "status": "training"
            }

            config_file = self.training_data_dir / f"{model_id}_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)

            logger.info(f"专业模型配置已保存: {config_file}")
            return result

        except Exception as e:
            logger.error(f"创建专业模型失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def create_professional_agent(self,
                                      profession: str,
                                      custom_name: str = None,
                                      additional_training_data: List[Dict] = None,
                                      specialization: str = None) -> Dict[str, Any]:
        """创建专业智能体"""
        try:
            if profession not in self.professional_templates:
                raise ValueError(f"不支持的专业类型: {profession}")
            
            template = self.professional_templates[profession].copy()
            
            # 自定义名称
            if custom_name:
                template["name"] = custom_name
            
            # 添加专业化信息
            if specialization:
                template["system_prompt"] += f"\n\n专业领域：{specialization}"
                template["name"] += f"（{specialization}专家）"
            
            # 准备训练数据
            training_data = template["training_examples"].copy()
            if additional_training_data:
                training_data.extend(additional_training_data)
            
            # 创建自定义模型
            model_name = f"{profession}_{custom_name or 'default'}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            # 生成 Modelfile
            modelfile_content = self._generate_modelfile(
                base_model=template["base_model"],
                system_prompt=template["system_prompt"],
                training_data=training_data
            )
            
            # 保存 Modelfile
            modelfile_path = self.models_dir / f"{model_name}.Modelfile"
            with open(modelfile_path, 'w', encoding='utf-8') as f:
                f.write(modelfile_content)
            
            # 创建模型
            success = await self._create_ollama_model(model_name, modelfile_path)
            
            if success:
                return {
                    "success": True,
                    "model_name": model_name,
                    "profession": profession,
                    "custom_name": custom_name,
                    "specialization": specialization,
                    "modelfile_path": str(modelfile_path),
                    "created_at": datetime.now().isoformat()
                }
            else:
                return {
                    "success": False,
                    "error": "模型创建失败"
                }
                
        except Exception as e:
            logger.error(f"创建专业智能体失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _generate_modelfile(self, 
                           base_model: str,
                           system_prompt: str,
                           training_data: List[Dict]) -> str:
        """生成 Ollama Modelfile"""
        
        modelfile = f"""FROM {base_model}

# 设置系统提示词
SYSTEM \"\"\"{system_prompt}\"\"\"

# 设置参数
PARAMETER temperature 0.7
PARAMETER top_p 0.9
PARAMETER top_k 40
PARAMETER repeat_penalty 1.1
PARAMETER num_ctx 4096

# 设置停止词
PARAMETER stop "Human:"
PARAMETER stop "Assistant:"

"""
        
        # 添加训练示例作为模板
        if training_data:
            modelfile += "# 训练示例模板\n"
            for i, example in enumerate(training_data[:5]):  # 限制示例数量
                modelfile += f"""TEMPLATE \"\"\"User: {example['input']}
Assistant: {example['output']}\"\"\"

"""
        
        return modelfile
    
    async def _create_ollama_model(self, model_name: str, modelfile_path: Path) -> bool:
        """使用 Ollama 创建自定义模型"""
        try:
            # 使用 ollama create 命令
            cmd = f"ollama create {model_name} -f {modelfile_path}"
            
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info(f"成功创建模型: {model_name}")
                return True
            else:
                logger.error(f"创建模型失败: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"创建模型异常: {e}")
            return False
    
    async def fine_tune_with_conversations(self,
                                         base_model: str,
                                         conversation_data: List[Dict],
                                         model_name: str) -> Dict[str, Any]:
        """使用对话数据微调模型"""
        try:
            # 准备训练数据文件
            training_file = self.training_data_dir / f"{model_name}_training.jsonl"
            
            with open(training_file, 'w', encoding='utf-8') as f:
                for conv in conversation_data:
                    # 转换为 JSONL 格式
                    training_item = {
                        "messages": [
                            {"role": "user", "content": conv["input"]},
                            {"role": "assistant", "content": conv["output"]}
                        ]
                    }
                    f.write(json.dumps(training_item, ensure_ascii=False) + '\n')
            
            # 创建微调后的模型
            return await self._create_fine_tuned_model(base_model, training_file, model_name)
            
        except Exception as e:
            logger.error(f"微调模型失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def _create_fine_tuned_model(self, 
                                     base_model: str,
                                     training_file: Path,
                                     model_name: str) -> Dict[str, Any]:
        """创建微调模型"""
        # 注意：这里是简化版本，实际的微调需要更复杂的过程
        # 对于 Ollama，我们主要通过 Modelfile 和示例来"训练"
        
        try:
            # 读取训练数据
            training_examples = []
            with open(training_file, 'r', encoding='utf-8') as f:
                for line in f:
                    data = json.loads(line)
                    messages = data["messages"]
                    if len(messages) >= 2:
                        training_examples.append({
                            "input": messages[0]["content"],
                            "output": messages[1]["content"]
                        })
            
            # 生成增强的 Modelfile
            enhanced_system_prompt = """你是一个经过专业训练的智能助手，基于以下对话示例学习了专业的回答方式。
请保持专业、准确、有帮助的回答风格。"""
            
            modelfile_content = self._generate_modelfile(
                base_model=base_model,
                system_prompt=enhanced_system_prompt,
                training_data=training_examples
            )
            
            # 保存并创建模型
            modelfile_path = self.models_dir / f"{model_name}.Modelfile"
            with open(modelfile_path, 'w', encoding='utf-8') as f:
                f.write(modelfile_content)
            
            success = await self._create_ollama_model(model_name, modelfile_path)
            
            return {
                "success": success,
                "model_name": model_name if success else None,
                "training_examples_count": len(training_examples),
                "modelfile_path": str(modelfile_path) if success else None
            }
            
        except Exception as e:
            logger.error(f"创建微调模型失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_available_models(self) -> List[Dict[str, Any]]:
        """获取可用的模型列表"""
        try:
            response = requests.get(f"{self.ollama_base_url}/api/tags")
            if response.status_code == 200:
                data = response.json()
                return data.get("models", [])
            return []
        except Exception as e:
            logger.error(f"获取模型列表失败: {e}")
            return []
    
    async def test_model(self, model_name: str, test_prompt: str) -> Dict[str, Any]:
        """测试模型性能"""
        try:
            payload = {
                "model": model_name,
                "prompt": test_prompt,
                "stream": False
            }
            
            response = requests.post(
                f"{self.ollama_base_url}/api/generate",
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "success": True,
                    "response": data.get("response", ""),
                    "model": model_name,
                    "eval_duration": data.get("eval_duration", 0),
                    "prompt_eval_duration": data.get("prompt_eval_duration", 0)
                }
            else:
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}"
                }
                
        except Exception as e:
            logger.error(f"测试模型失败: {e}")
            return {"success": False, "error": str(e)}
