import os
import torch
import logging
from pathlib import Path
import sys

# 初始化日志配置
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger('WAN-Pretrained')

def build_model(version="2.1", device="cuda", dtype=torch.float16):
    """
    构建预训练的WAN模型
    
    参数:
        version (str): 模型版本，默认"2.1"
        device (str): 计算设备，默认"cuda"
        dtype (torch.dtype): 模型数据类型，默认float16
        
    返回:
        model: 预加载的WAN模型
    """
    # 修正当前路径，确保能正确导入
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    if parent_dir not in sys.path:
        sys.path.insert(0, parent_dir)
    
    logger.info(f"初始化WAN {version}模型 (设备: {device}, 类型: {dtype})")
    
    # 导入相关模块
    if version == "2.1":
        from .modules.model import build_model as _build_model
        
        # 构建模型参数
        model_kwargs = {
            "device": device,
            "dtype": dtype,
        }
        
        # 构建模型
        model = _build_model(**model_kwargs)
        logger.info(f"成功加载WAN {version}模型")
        return model
    else:
        raise ValueError(f"不支持的模型版本: {version}")

def prepare_inputs(
    prompt="一个美丽的花园", 
    lang="zh", 
    width=1024, 
    height=576, 
    frames=16,
    enhance=False
):
    """
    准备模型输入
    
    参数:
        prompt (str): 描述视频内容的文本
        lang (str): 语言，'zh'或'en'
        width (int): 视频宽度
        height (int): 视频高度
        frames (int): 视频帧数
        enhance (bool): 是否启用增强
        
    返回:
        dict: 包含处理后的输入数据
    """
    # 记录参数
    logger.info(f"准备输入参数: 尺寸={width}x{height}, 帧数={frames}, 语言={lang}")
    logger.info(f"提示词: '{prompt}'")
    
    # 处理语言
    if lang == "zh":
        # 中文提示词处理逻辑
        logger.info("使用中文提示词处理")
    elif lang == "en":
        # 英文提示词处理逻辑
        logger.info("使用英文提示词处理")
    else:
        logger.warning(f"未知语言: {lang}，使用默认处理")
    
    # 构建输入字典
    inputs = {
        "prompt": prompt,
        "width": width,
        "height": height,
        "frames": frames,
        "lang": lang,
        "enhance": enhance
    }
    
    return inputs 