# Copyright 2024-2025 The Alibaba Wan Team Authors. All rights reserved.
import argparse
import binascii
import os
import os.path as osp
import shutil

import imageio
import torch
import torchvision

__all__ = ['cache_video', 'cache_image', 'str2bool']


def rand_name(length=8, suffix=''):
    name = binascii.b2a_hex(os.urandom(length)).decode('utf-8')
    if suffix:
        if not suffix.startswith('.'):
            suffix = '.' + suffix
        name += suffix
    return name


def cache_video(tensor,
                save_file=None,
                fps=30,
                suffix='.mp4',
                nrow=8,
                normalize=True,
                value_range=(-1, 1),
                retry=5,
                quality=8,  # quality参数范围在1-10之间
                bitrate=None):  # 可选的比特率参数，单位kbps
    """
    将视频张量保存为MP4文件。
    
    Args:
        tensor: 要保存的视频张量，支持多种格式：
            - (T, C, H, W): 帧数, 通道数, 高度, 宽度
            - (T, H, W, C): 帧数, 高度, 宽度, 通道数
            - (B, T, C, H, W): 批次, 帧数, 通道数, 高度, 宽度
            - 其他格式也会尝试自动处理
        save_file: 保存路径，如果为None，则保存到临时文件
        fps: 帧率，默认30
        suffix: 文件后缀，默认'.mp4'
        nrow: 网格排列时每行的图像数，默认8
        normalize: 是否归一化像素值，默认True
        value_range: 归一化的值范围，默认(-1, 1)
        retry: 重试次数，默认5
        quality: 视频质量参数(1-10)，值越大质量越高，默认8
        bitrate: 视频比特率，单位kbps，默认为None(使用默认比特率)
    
    Returns:
        成功时返回保存的文件路径，失败时返回None
    """
    import tempfile
    import subprocess
    import cv2
    import numpy as np
    
    # 确保quality在有效范围内
    quality = max(1, min(10, quality))
    
    # cache file
    cache_file = osp.join('/tmp', rand_name(
        suffix=suffix)) if save_file is None else save_file

    # 准备额外的编码器参数
    extra_args = {}
    if bitrate is not None:
        extra_args['bitrate'] = int(bitrate) * 1000  # 转换为bps
    
    # save to cache
    error = None
    for attempt in range(retry):
        try:
            # 预处理张量
            if tensor.dim() == 3:  # (H, W, C)
                # 单帧图像，转换为单帧视频
                tensor = tensor.unsqueeze(0)  # 添加时间维度 (1, H, W, C)
            
            if tensor.dim() == 4:  # 可能是(B, H, W, C)或(T, C, H, W)
                if tensor.size(1) == 3 or tensor.size(1) == 1:  # 应该是(T, C, H, W)格式
                    tensor = tensor
                else:  # 应该是(B, H, W, C)格式，需要转换
                    tensor = tensor.permute(0, 3, 1, 2)
            
            if tensor.dim() == 5:  # (B, T, C, H, W)
                # 将batch维度展开为时间维度
                b, t, c, h, w = tensor.size()
                tensor = tensor.view(b * t, c, h, w)
            
            # 处理单一帧的情况
            if tensor.size(0) < 16:  # 如果帧数小于16，重复帧
                orig_frames = tensor.size(0)
                repeat_times = max(1, 16 // orig_frames)
                tensor = tensor.repeat(repeat_times, 1, 1, 1)
                print(f"帧数过少，从 {orig_frames} 重复到 {tensor.size(0)} 帧", flush=True)
            
            # 处理网格排列
            # 确保数据是浮点型且值在有效范围内
            tensor = tensor.float()  # 确保是浮点型
            
            # 处理网格排列前先确保数值范围合适
            if normalize:
                # 在网格排列前先规范化到0-1范围
                tensor = (tensor - min(value_range)) / (max(value_range) - min(value_range))
                tensor = torch.clamp(tensor, 0.0, 1.0)  # 安全裁剪确保0-1范围
            
            # 处理网格排列
            try:
                # 使用修改后的方法创建网格
                tensor_list = []
                for u in tensor.unbind(0):  # 假设张量是(T, C, H, W)格式
                    # 对每帧应用make_grid，但不再在这里做normalize (已经在上面完成)
                    grid = torchvision.utils.make_grid(
                        u, nrow=nrow, normalize=False, value_range=(0, 1))
                    tensor_list.append(grid)
                
                tensor = torch.stack(tensor_list, dim=0).permute(0, 2, 3, 1)  # 变为(T, H, W, C)
            except Exception as e:
                print(f"网格创建失败: {e}，尝试直接转换格式", flush=True)
                # 如果网格创建失败，直接转换格式
                if tensor.size(1) == 3 or tensor.size(1) == 1:  # (T, C, H, W)
                    tensor = tensor.permute(0, 2, 3, 1)  # 转为(T, H, W, C)
            # 检查数据是否有效
            if tensor.max().item() == tensor.min().item():
                print(f"警告: 视频帧数据可能无效，所有值相同: {tensor.max().item()}", flush=True)
                # 添加一些噪声以避免全黑视频
                print("尝试添加小噪声以避免全黑视频", flush=True)
                tensor = tensor + torch.rand_like(tensor) * 0.01
                tensor = torch.clamp(tensor, 0.0, 1.0)
            
            # 确保数据类型正确
            tensor = tensor.float()  # 确保是浮点型
            tensor = torch.clamp(tensor, 0, 1)  # 确保值在0-1范围内
            tensor = torch.clamp(tensor, 0.0, 1.0)
            tensor = (tensor * 255.0).round()  # 四舍五入到最接近的整数
            tensor = tensor.to(torch.uint8).cpu()  # 转换为uint8类型

            # 使用临时目录存储帧
            temp_dir = tempfile.mkdtemp()
            try:
                print(f"创建临时目录保存视频帧: {temp_dir}", flush=True)
                
                # 保存每帧为PNG
                frames_saved = 0
                for i, frame in enumerate(tensor.numpy()):
                    # 保存为PNG
                    frame_path = os.path.join(temp_dir, f"frame_{i:04d}.png")
                    
                    # OpenCV期望BGR格式，所以从RGB转换
                    frame_bgr = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                    
                    # 保存图像
                    cv2.imwrite(frame_path, frame_bgr)
                    frames_saved += 1
                
                print(f"成功保存 {frames_saved} 帧到临时目录", flush=True)
                
                # 确保目标目录存在
                os.makedirs(os.path.dirname(os.path.abspath(cache_file)), exist_ok=True)
                
                # 使用FFmpeg合成视频
                ffmpeg_cmd = [
                    "ffmpeg", "-y",
                    "-framerate", str(fps),
                    "-i", os.path.join(temp_dir, "frame_%04d.png"),
                    "-c:v", "libx264",
                    "-preset", "medium",
                    "-crf", "23",
                    "-pix_fmt", "yuv420p",
                    cache_file
                ]
                
                print(f"执行FFmpeg命令: {' '.join(ffmpeg_cmd)}", flush=True)
                process = subprocess.run(
                    ffmpeg_cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    check=False
                )
                
                # 检查FFmpeg是否成功
                if process.returncode == 0 and os.path.exists(cache_file) and os.path.getsize(cache_file) > 5000:
                    print(f"成功生成视频: {cache_file}，大小: {os.path.getsize(cache_file)} 字节", flush=True)
                    
                    # 清理临时文件
                    shutil.rmtree(temp_dir)
                    print(f"已清理临时目录: {temp_dir}", flush=True)
                    
                    # 记录尝试次数信息
                    if attempt > 0:
                        print(f'成功保存视频，尝试次数: {attempt+1}/{retry}', flush=True)
                    
                    return cache_file
                else:
                    error_msg = f"使用FFmpeg生成视频失败: {process.stderr}"
                    print(error_msg, flush=True)
                    error = RuntimeError(error_msg)
                    continue
                    
            except Exception as e:
                print(f"保存视频帧时出错: {e}", flush=True)
                import traceback
                print(traceback.format_exc(), flush=True)
                error = e
                continue
            finally:
                # 确保临时目录被清理
                if os.path.exists(temp_dir):
                    try:
                        shutil.rmtree(temp_dir)
                    except Exception as e:
                        print(f"清理临时目录时出错: {e}", flush=True)
                
        except Exception as e:
            error = e
            print(f'cache_video出错，尝试次数: {attempt+1}/{retry}, 错误: {e}', flush=True)
            import traceback
            print(traceback.format_exc(), flush=True)
            continue
    
    print(f'cache_video完全失败，最后错误: {error}', flush=True)
    return None
def cache_image(tensor,
                save_file,
                nrow=8,
                normalize=True,
                value_range=(-1, 1),
                retry=5):
    # cache file
    suffix = osp.splitext(save_file)[1]
    if suffix.lower() not in [
            '.jpg', '.jpeg', '.png', '.tiff', '.gif', '.webp'
    ]:
        suffix = '.png'

    # save to cache
    error = None
    for attempt in range(retry):
        try:
            tensor = tensor.clamp(min(value_range), max(value_range))
            torchvision.utils.save_image(
                tensor,
                save_file,
                nrow=nrow,
                normalize=normalize,
                value_range=value_range)
            return save_file
        except Exception as e:
            error = e
            continue


def str2bool(v):
    """
    Convert a string to a boolean.

    Supported true values: 'yes', 'true', 't', 'y', '1'
    Supported false values: 'no', 'false', 'f', 'n', '0'

    Args:
        v (str): String to convert.

    Returns:
        bool: Converted boolean value.

    Raises:
        argparse.ArgumentTypeError: If the value cannot be converted to boolean.
    """
    if isinstance(v, bool):
        return v
    v_lower = v.lower()
    if v_lower in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v_lower in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected (True/False)')
