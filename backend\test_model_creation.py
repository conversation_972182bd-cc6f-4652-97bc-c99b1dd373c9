#!/usr/bin/env python3
"""
测试模型创建功能
"""

import asyncio
import subprocess
from pathlib import Path

async def test_create_model():
    # 创建测试Modelfile
    modelfile_content = """FROM qwen2.5:7b

# 设置系统提示词
SYSTEM \"\"\"你是一位专业的英语教师\"\"\"

# 设置参数
PARAMETER temperature 0.7
PARAMETER top_p 0.9
"""
    
    # 保存测试Modelfile
    modelfile_path = Path('storage/custom_models/test_model.Modelfile')
    modelfile_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(modelfile_path, 'w', encoding='utf-8') as f:
        f.write(modelfile_content)
    
    print(f'Modelfile已保存到: {modelfile_path}')
    print(f'内容:\n{modelfile_content}')
    
    # 测试ollama create命令
    model_name = 'test_teacher_model'
    cmd = ['ollama', 'create', model_name, '-f', str(modelfile_path)]
    
    print(f'执行命令: {" ".join(cmd)}')
    
    try:
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        print(f'返回码: {process.returncode}')
        print(f'标准输出: {stdout.decode()}')
        print(f'错误输出: {stderr.decode()}')
        
        if process.returncode == 0:
            print('✅ 模型创建成功')
            
            # 列出模型
            list_cmd = ['ollama', 'list']
            list_process = await asyncio.create_subprocess_exec(
                *list_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            list_stdout, list_stderr = await list_process.communicate()
            print(f'模型列表:\n{list_stdout.decode()}')
            
        else:
            print('❌ 模型创建失败')
            
    except Exception as e:
        print(f'异常: {e}')

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_create_model())
