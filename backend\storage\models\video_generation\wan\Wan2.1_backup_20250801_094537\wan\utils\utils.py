# Copyright 2024-2025 The Alibaba Wan Team Authors. All rights reserved.
import argparse
import binascii
import os
import os.path as osp
import shutil
import logging  # 添加logging导入
import tempfile
import subprocess
import numpy as np
import torch
import torchvision
import random
import cv2
import time
from pathlib import Path
import re
import string
from typing import Dict, List, Optional, Tuple, Union

import torch.nn.functional as F
from PIL import Image
from torch import Tensor

# 配置基本日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
# 创建logger实例
logger = logging.getLogger("WAN-Utils")

import imageio

__all__ = ['cache_video', 'cache_image', 'str2bool']


def rand_name(length=8, suffix=''):
    name = binascii.b2a_hex(os.urandom(length)).decode('utf-8')
    if suffix:
        if not suffix.startswith('.'):
            suffix = '.' + suffix
        name += suffix
    return name


def cache_video(video, path, h=None, w=None):
    """
    Enhanced video tensor handling and caching function
    Handles zero values, adds noise to prevent black videos, and ensures proper format
    """
    logger = logging.getLogger("WAN-Utils")
    
    logger.info(f"Processing video frames with shape: {video.shape if hasattr(video, 'shape') else 'unknown'}")
    
    # Ensure video is a tensor and on CPU
    if isinstance(video, torch.Tensor):
        logger.info(f"Converting tensor from {video.device} to CPU")
        video = video.detach().cpu()
    elif not isinstance(video, torch.Tensor):
        logger.error(f"Video is not a tensor but {type(video)}")
        if hasattr(video, '__iter__'):
            # Try to convert list/tuple to tensor
            try:
                video = torch.stack(list(video)) if len(video) > 0 else torch.rand((16, 720, 1280, 3)) * 0.6 - 0.3
                logger.info(f"Converted iterable to tensor with shape {video.shape}")
            except Exception as e:
                logger.error(f"Failed to convert to tensor: {str(e)}")
                # Create random tensor instead of zeros
                video = torch.rand((16, 720, 1280, 3)) * 0.6 - 0.3
        else:
            video = torch.rand((16, 720, 1280, 3)) * 0.6 - 0.3
    
    # Ensure proper dimensionality - expect (T, H, W, C) or (C, T, H, W)
    if len(video.shape) != 4:
        logger.warning(f"Unexpected tensor shape: {video.shape}, reshaping...")
        if len(video.shape) == 3:
            # Add singleton dimension based on shape
            if video.shape[0] == 3:  # (C, H, W)
                video = video.unsqueeze(1)  # -> (C, 1, H, W)
            else:  # (H, W, C)
                video = video.unsqueeze(0)  # -> (1, H, W, C)
        else:
            # Create random tensor for invalid shapes
            logger.error(f"Cannot reshape tensor with shape {video.shape}")
            video = torch.rand((16, 720, 1280, 3)) * 0.6 - 0.3
    
    # Standardize to (T, H, W, C) format if needed
    if video.shape[0] == 3 and video.shape[-1] != 3:  # (C, T, H, W) format
        logger.info(f"Converting from (C,T,H,W) to (T,H,W,C) format")
        video = video.permute(1, 2, 3, 0)
    elif video.shape[1] == 3 and video.shape[-1] != 3:  # (T, C, H, W) format
        logger.info(f"Converting from (T,C,H,W) to (T,H,W,C) format")
        video = video.permute(0, 2, 3, 1)
    
    # Check data range and type
    min_val = float(video.min())
    max_val = float(video.max())
    logger.info(f"Video data range: [{min_val}, {max_val}]")
    
    # Check for uniform values (potential black frames)
    if max_val - min_val < 1e-3:
        logger.warning(f"Video has uniform values ({min_val}), adding noise to prevent black frames")
        # Add significant noise to make frames visible
        video = video + torch.rand_like(video) * 0.5
        # Recompute range
        min_val = float(video.min())
        max_val = float(video.max())
        logger.info(f"After adding noise, range: [{min_val}, {max_val}]")
    
    # Normalize based on value range
    if -1.0 <= min_val < 0 and 0 < max_val <= 1.0:
        logger.info(f"Normalizing from [-1,1] to [0,1] range")
        video = (video + 1) / 2
    
    # Ensure data is in 0-1 range
    if min_val < 0 or max_val > 1.0:
        logger.info(f"Clamping values to [0,1] range")
        video = torch.clamp(video, 0.0, 1.0)
    
    # 增强视频对比度和亮度
    logger.info(f"增强视频画面品质")
    min_val = video.min()
    max_val = video.max()
    if max_val - min_val < 0.3:  # 对比度太低
        logger.info(f"增强视频对比度，原范围: [{min_val:.4f}, {max_val:.4f}]")
        # 对比度增强，但保留一些暗部和亮部细节
        video = ((video - min_val) / max(max_val - min_val, 1e-5)) * 0.9 + 0.05
        logger.info(f"增强后范围: [{video.min().item():.4f}, {video.max().item():.4f}]")
    
    # 如果画面整体偏暗，适当提亮
    if video.mean() < 0.4:
        logger.info(f"提高整体亮度，原亮度均值: {video.mean().item():.4f}")
        # 增加亮度但避免过曝
        brightness_boost = 0.6 - video.mean().item()
        video = torch.clamp(video + brightness_boost * 0.5, 0.0, 1.0)
        logger.info(f"增强后亮度均值: {video.mean().item():.4f}")
    
    # 如果颜色不丰富，增加色彩饱和度
    if video.shape[-1] == 3:  # 确保是RGB格式
        # 计算色彩饱和度（RGB通道间的标准差）
        r, g, b = video[..., 0], video[..., 1], video[..., 2]
        saturation = torch.stack([r, g, b], dim=-1).std(dim=-1).mean()
        
        if saturation < 0.1:  # 色彩饱和度太低
            logger.info(f"增强色彩饱和度，原饱和度: {saturation.item():.4f}")
            # 计算灰度
            gray = (r + g + b) / 3.0
            gray = gray.unsqueeze(-1).expand_as(video)
            
            # 增加色彩饱和度（向RGB方向推动，远离灰度）
            boost_factor = 1.5  # 饱和度增强因子
            video = gray + (video - gray) * boost_factor
            video = torch.clamp(video, 0.0, 1.0)
            
            # 重新计算饱和度
            r, g, b = video[..., 0], video[..., 1], video[..., 2]
            new_saturation = torch.stack([r, g, b], dim=-1).std(dim=-1).mean()
            logger.info(f"增强后饱和度: {new_saturation.item():.4f}")
    
    # 增加适度锐化效果
    if len(video.shape) == 4 and video.shape[0] > 1:  # 至少有2帧
        # 使用简单的卷积模拟锐化
        logger.info("应用锐化效果增强细节")
        try:
            # 转换到NCHW格式以便于卷积操作
            video_nchw = video.permute(0, 3, 1, 2)
            
            # 创建锐化卷积核
            kernel = torch.tensor([
                [0, -1, 0],
                [-1, 5, -1],
                [0, -1, 0]
            ], dtype=torch.float32, device=video.device).view(1, 1, 3, 3).expand(3, 1, 3, 3)
            
            # 对每个通道分别应用锐化
            sharpened = F.conv2d(
                video_nchw, 
                kernel, 
                padding=1, 
                groups=3
            )
            
            # 转回原始格式
            sharpened = sharpened.permute(0, 2, 3, 1)
            
            # 混合原始视频和锐化版本
            video = video * 0.7 + sharpened * 0.3
            video = torch.clamp(video, 0.0, 1.0)
        except Exception as e:
            logger.warning(f"锐化处理失败: {str(e)}")
    
    # 确保最终值在0-1范围内
    video = torch.clamp(video, 0.0, 1.0)
    
    # Convert to uint8 for saving
    logger.info(f"Converting to uint8 format")
    video = (video * 255).to(torch.uint8)
    
    # Ensure minimum number of frames for valid video
    num_frames = video.shape[0]
    if num_frames < 16:
        logger.warning(f"Too few frames ({num_frames}), extending to 16")
        # Repeat frames to reach minimum length
        repeats = max(1, int(16 / num_frames) + 1)
        video = video.repeat_interleave(repeats, dim=0)
        # Trim to 16 frames if we went over
        video = video[:16]
        logger.info(f"Extended to {video.shape[0]} frames")
    
    # Save video with multiple fallback options
    try:
        # Create a temporary directory to save frames
        with tempfile.TemporaryDirectory() as temp_dir:
            logger.info(f"Saving video frames to temporary directory: {temp_dir}")
            
            # Save each frame as an image
            for i in range(video.shape[0]):
                frame = video[i].numpy()
                frame_path = os.path.join(temp_dir, f"frame_{i:04d}.png")
                
                import cv2
                # OpenCV expects BGR, but our tensor is RGB
                if frame.shape[-1] == 3:
                    frame = cv2.cvtColor(frame, cv2.COLOR_RGB2BGR)
                cv2.imwrite(frame_path, frame)
            
            # Ensure directory for output video exists
            os.makedirs(os.path.dirname(os.path.abspath(path)), exist_ok=True)
            
            # Use FFmpeg to combine frames with higher bitrate
            framerate = 12  # 12 FPS for smoother video
            ffmpeg_cmd = f"ffmpeg -y -framerate {framerate} -i {temp_dir}/frame_%04d.png -c:v libx264 -preset medium -crf 18 -pix_fmt yuv420p -b:v 4M {path}"
            logger.info(f"Running FFmpeg: {ffmpeg_cmd}")
            
            result = subprocess.run(ffmpeg_cmd, shell=True, capture_output=True, text=True)
            if result.returncode != 0:
                logger.error(f"FFmpeg error: {result.stderr}")
                raise RuntimeError(f"FFmpeg failed: {result.stderr}")
            
            # Verify the file exists and has content
            if os.path.exists(path) and os.path.getsize(path) > 1000:  # Minimum size for valid video
                logger.info(f"Successfully saved video to {path}, size: {os.path.getsize(path)} bytes")
                return True
            else:
                logger.warning(f"Video file exists but may be too small: {os.path.getsize(path) if os.path.exists(path) else 'not found'}")
                raise ValueError(f"Generated video may be invalid (too small)")
    
    except Exception as e:
        logger.error(f"Failed to save video using primary method: {str(e)}")
        
        # Fallback method using imageio
        try:
            logger.info("Attempting fallback with imageio...")
            import imageio
            
            # Convert tensor to numpy for imageio
            if isinstance(video, torch.Tensor):
                video_np = video.numpy()
            else:
                video_np = video
            
            # Ensure correct format
            if video_np.shape[-1] != 3:
                logger.warning(f"Unexpected frame format: {video_np.shape}")
                # Try to correct format
                if video_np.shape[0] == 3:  # (C,T,H,W)
                    video_np = np.transpose(video_np, (1, 2, 3, 0))
            
            # Save with imageio
            imageio.mimwrite(path, video_np, fps=12, quality=8)
            
            # Verify file
            if os.path.exists(path) and os.path.getsize(path) > 1000:
                logger.info(f"Successfully saved video using imageio fallback, size: {os.path.getsize(path)}")
                return True
            else:
                logger.error(f"Imageio fallback produced invalid file")
                return False
        
        except Exception as e2:
            logger.error(f"All video saving methods failed: {str(e2)}")
            return False


def cache_image(tensor,
                save_file,
                nrow=8,
                normalize=True,
                value_range=(-1, 1),
                retry=5):
    # cache file
    suffix = osp.splitext(save_file)[1]
    if suffix.lower() not in [
            '.jpg', '.jpeg', '.png', '.tiff', '.gif', '.webp'
    ]:
        suffix = '.png'

    # save to cache
    error = None
    for attempt in range(retry):
        try:
            tensor = tensor.clamp(min(value_range), max(value_range))
            torchvision.utils.save_image(
                tensor,
                save_file,
                nrow=nrow,
                normalize=normalize,
                value_range=value_range)
            return save_file
        except Exception as e:
            error = e
            continue


def str2bool(v):
    """
    Convert a string to a boolean.

    Supported true values: 'yes', 'true', 't', 'y', '1'
    Supported false values: 'no', 'false', 'f', 'n', '0'

    Args:
        v (str): String to convert.

    Returns:
        bool: Converted boolean value.

    Raises:
        argparse.ArgumentTypeError: If the value cannot be converted to boolean.
    """
    if isinstance(v, bool):
        return v
    v_lower = v.lower()
    if v_lower in ('yes', 'true', 't', 'y', '1'):
        return True
    elif v_lower in ('no', 'false', 'f', 'n', '0'):
        return False
    else:
        raise argparse.ArgumentTypeError('Boolean value expected (True/False)')
