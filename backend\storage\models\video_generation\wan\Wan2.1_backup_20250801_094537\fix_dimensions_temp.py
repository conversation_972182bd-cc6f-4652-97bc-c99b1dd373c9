import sys, os 
with open("generate.py", "r", encoding="utf-8") as f: 
    content = f.read() 
 
# 备份原始文件 
if not os.path.exists("generate.py.backup"): 
    with open("generate.py.backup", "w", encoding="utf-8") as f: 
        f.write(content) 
    print("已备份原始generate.py文件") 
 
# 直接修改视频宽高比参数 
if "--frame_num" not in sys.argv: 
    sys.argv.extend(["--frame_num", "16"]) 
    print("添加参数: --frame_num 16") 
 
if "--sample_steps" not in sys.argv: 
    sys.argv.extend(["--sample_steps", "30"]) 
    print("添加参数: --sample_steps 30") 
 
