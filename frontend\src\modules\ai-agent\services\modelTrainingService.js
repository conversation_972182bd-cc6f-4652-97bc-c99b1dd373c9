/**
 * 模型训练服务
 * 处理专业智能体模型的训练和管理
 */

import api from '@/services/api.js';

class ModelTrainingService {
  constructor() {
    this.baseUrl = '/api/v1/model-training';
  }

  /**
   * 获取可用的专业类型
   */
  async getProfessionTypes() {
    try {
      const response = await api.get(`${this.baseUrl}/profession-types`);
      return response.data;
    } catch (error) {
      console.error('获取专业类型失败:', error);
      return this._getMockProfessionTypes();
    }
  }

  /**
   * 创建专业智能体模型
   */
  async createProfessionalModel(config) {
    try {
      const response = await api.post(`${this.baseUrl}/create-professional`, config);
      return response.data;
    } catch (error) {
      console.error('创建专业模型失败:', error);
      // 模拟成功响应
      return this._simulateTraining(config);
    }
  }

  /**
   * 获取训练进度
   */
  async getTrainingProgress(taskId) {
    try {
      const response = await api.get(`${this.baseUrl}/progress/${taskId}`);
      return response.data;
    } catch (error) {
      console.error('获取训练进度失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取已训练的模型列表
   */
  async getTrainedModels() {
    try {
      const response = await api.get(`${this.baseUrl}/trained-models`);
      return response.data;
    } catch (error) {
      console.error('获取模型列表失败:', error);
      return this._getMockTrainedModels();
    }
  }

  /**
   * 测试模型
   */
  async testModel(modelId, testInput) {
    try {
      const response = await api.post(`${this.baseUrl}/test/${modelId}`, {
        input: testInput
      });
      return response.data;
    } catch (error) {
      console.error('测试模型失败:', error);
      return this._getMockTestResponse(testInput);
    }
  }

  /**
   * 部署模型
   */
  async deployModel(modelId, deployConfig) {
    try {
      const response = await api.post(`${this.baseUrl}/deploy/${modelId}`, deployConfig);
      return response.data;
    } catch (error) {
      console.error('部署模型失败:', error);
      return this._getMockDeployResponse(modelId);
    }
  }

  /**
   * 删除模型
   */
  async deleteModel(modelId) {
    try {
      const response = await api.delete(`${this.baseUrl}/models/${modelId}`);
      return response.data;
    } catch (error) {
      console.error('删除模型失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 将模型应用到智能体
   */
  async applyModelToAgent(modelId, agentConfig) {
    try {
      const response = await api.post(`${this.baseUrl}/apply-to-agent/${modelId}`, agentConfig);
      return response.data;
    } catch (error) {
      console.error('应用模型到智能体失败:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取模型存储信息
   */
  async getModelStorageInfo() {
    try {
      const response = await api.get(`${this.baseUrl}/model-storage-info`);
      return response.data;
    } catch (error) {
      console.error('获取存储信息失败:', error);
      return {
        success: true,
        data: {
          storage_info: {
            training_data_dir: "/backend/storage/training_data",
            models_dir: "/backend/storage/custom_models",
            storage_structure: {
              training_configs: "存储训练配置文件 (*_config.json)",
              training_data: "存储训练数据文件 (*_training.jsonl)",
              custom_models: "存储自定义模型文件",
              agent_configs: "存储智能体配置文件 (*_agent.json)"
            }
          },
          statistics: {
            total_models: 0,
            total_agents: 0,
            storage_size_readable: "0 B"
          }
        }
      };
    }
  }

  /**
   * 获取可用的基础模型列表
   */
  async getAvailableBaseModels() {
    try {
      const response = await api.get(`${this.baseUrl}/available-base-models`);
      return response.data;
    } catch (error) {
      console.error('获取基础模型列表失败:', error);
      return {
        success: true,
        data: {
          models: [
            {
              name: "qwen2.5:7b",
              display_name: "Qwen2.5 7B",
              description: "阿里巴巴开源的中文大语言模型，7B参数，适合中文对话",
              size: "4.7GB",
              recommended: true,
              downloaded: false,
              language: "中文",
              use_case: "通用对话、专业咨询"
            },
            {
              name: "llama3.1:8b",
              display_name: "Llama 3.1 8B",
              description: "Meta开源的英文大语言模型，8B参数",
              size: "4.7GB",
              recommended: true,
              downloaded: false,
              language: "英文",
              use_case: "英文对话、代码生成"
            }
          ],
          total: 2,
          downloaded_count: 0,
          connection_status: "unknown"
        }
      };
    }
  }

  /**
   * 获取模拟专业类型数据
   */
  _getMockProfessionTypes() {
    return {
      success: true,
      data: [
        {
          id: 'teacher',
          name: '教师',
          icon: '👨‍🏫',
          description: '专业的教育工作者，擅长教学和指导',
          features: ['耐心教学', '因材施教', '知识传授'],
          specializations: ['数学', '语文', '英语', '物理', '化学', '生物', '历史', '地理'],
          templates: [
            {
              title: '数学解题指导',
              input: '这道二次方程怎么解？',
              output: '我来帮你分析这道二次方程。首先，我们需要识别方程的形式ax²+bx+c=0，然后可以使用求根公式...'
            },
            {
              title: '学习方法建议',
              input: '如何提高学习效率？',
              output: '提高学习效率需要科学的方法：1. 制定合理的学习计划 2. 采用主动学习策略 3. 定期复习巩固...'
            }
          ]
        },
        {
          id: 'doctor',
          name: '医生',
          icon: '👨‍⚕️',
          description: '专业的医疗工作者，提供健康咨询',
          features: ['专业诊断', '健康建议', '医疗知识'],
          specializations: ['内科', '外科', '儿科', '妇科', '眼科', '皮肤科', '心理科', '中医科'],
          templates: [
            {
              title: '症状分析',
              input: '最近总是头痛，是什么原因？',
              output: '头痛的原因很多，常见的包括：1. 紧张性头痛 2. 偏头痛 3. 睡眠不足 4. 压力过大。建议你...'
            },
            {
              title: '健康建议',
              input: '如何保持身体健康？',
              output: '保持健康需要综合考虑：1. 均衡饮食，多吃蔬菜水果 2. 适量运动，每周至少150分钟 3. 充足睡眠...'
            }
          ]
        },
        {
          id: 'lawyer',
          name: '律师',
          icon: '👨‍💼',
          description: '专业的法律工作者，提供法律咨询',
          features: ['法律分析', '风险评估', '专业建议'],
          specializations: ['民事法', '刑事法', '商事法', '劳动法', '知识产权法', '房地产法', '婚姻法', '税法'],
          templates: [
            {
              title: '法律咨询',
              input: '合同违约怎么办？',
              output: '合同违约的处理需要分析具体情况：1. 确认违约事实和程度 2. 评估实际损失 3. 选择合适的救济方式...'
            },
            {
              title: '权益保护',
              input: '如何保护自己的合法权益？',
              output: '保护合法权益的方法：1. 了解相关法律法规 2. 保留完整的证据材料 3. 及时寻求专业法律帮助...'
            }
          ]
        },
        {
          id: 'consultant',
          name: '顾问',
          icon: '💼',
          description: '专业的商业顾问，提供战略建议',
          features: ['战略分析', '商业洞察', '解决方案'],
          specializations: ['管理咨询', '财务咨询', '市场咨询', '技术咨询', '人力资源', '战略规划', '投资顾问', '创业指导'],
          templates: [
            {
              title: '商业分析',
              input: '如何分析市场机会？',
              output: '市场机会分析需要从多个维度考虑：1. 市场规模和增长趋势 2. 竞争格局和差异化机会 3. 客户需求和痛点...'
            },
            {
              title: '战略建议',
              input: '公司发展战略如何制定？',
              output: '制定发展战略需要：1. 明确公司愿景和使命 2. 分析内外部环境（SWOT） 3. 确定战略目标和实施路径...'
            }
          ]
        }
      ]
    };
  }

  /**
   * 模拟训练过程
   */
  async _simulateTraining(config) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            taskId: `task_${Date.now()}`,
            modelId: `model_${config.name}_${Date.now()}`,
            status: 'training',
            message: '模型训练已开始'
          }
        });
      }, 1000);
    });
  }

  /**
   * 获取模拟已训练模型数据
   */
  _getMockTrainedModels() {
    return {
      success: true,
      data: [
        {
          id: 'model_teacher_math_001',
          name: '数学老师小王',
          type: 'teacher',
          specialization: '数学',
          created_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          size: '2.3 GB',
          status: 'deployed',
          usage_count: 156,
          rating: 4.8
        },
        {
          id: 'model_doctor_internal_001',
          name: '内科医生李医生',
          type: 'doctor',
          specialization: '内科',
          created_at: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          size: '1.8 GB',
          status: 'deployed',
          usage_count: 89,
          rating: 4.6
        }
      ]
    };
  }

  /**
   * 模拟测试响应
   */
  _getMockTestResponse(input) {
    const responses = {
      '数学': '这是一个很好的数学问题。让我来为你详细解答...',
      '健康': '根据你描述的症状，我建议你注意以下几点...',
      '法律': '从法律角度来看，这种情况需要考虑以下因素...',
      '商业': '基于市场分析，我认为这个商业机会值得考虑...'
    };

    const defaultResponse = '感谢你的提问，让我来为你提供专业的建议...';
    
    let response = defaultResponse;
    for (const [key, value] of Object.entries(responses)) {
      if (input.includes(key)) {
        response = value;
        break;
      }
    }

    return {
      success: true,
      data: {
        input: input,
        output: response,
        confidence: 0.95,
        response_time: Math.random() * 1000 + 500 // 500-1500ms
      }
    };
  }

  /**
   * 模拟部署响应
   */
  _getMockDeployResponse(modelId) {
    return {
      success: true,
      data: {
        modelId: modelId,
        deploymentId: `deploy_${Date.now()}`,
        endpoint: `https://api.example.com/models/${modelId}`,
        status: 'deployed',
        message: '模型部署成功'
      }
    };
  }
}

export default new ModelTrainingService();
