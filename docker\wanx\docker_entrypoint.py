#!/usr/bin/env python3
"""
Wanx Docker容器入口点
处理视频生成请求
"""

import os
import sys
import json
import time
import subprocess
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WanxDockerService:
    """Wanx Docker服务"""
    
    def __init__(self):
        self.models_dir = Path("/app/models")
        self.output_dir = Path("/app/output")
        self.wanx_script = self.models_dir / "generate.py"
        
        # 确保目录存在
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    def check_environment(self):
        """检查环境"""
        logger.info("检查Docker环境...")
        
        # 检查CUDA
        try:
            import torch
            logger.info(f"PyTorch: {torch.__version__}")
            logger.info(f"CUDA available: {torch.cuda.is_available()}")
            if torch.cuda.is_available():
                logger.info(f"CUDA version: {torch.version.cuda}")
                logger.info(f"GPU count: {torch.cuda.device_count()}")
                for i in range(torch.cuda.device_count()):
                    gpu_name = torch.cuda.get_device_name(i)
                    logger.info(f"GPU {i}: {gpu_name}")
        except Exception as e:
            logger.error(f"CUDA检查失败: {e}")
        
        # 检查Wanx脚本
        if not self.wanx_script.exists():
            logger.error(f"Wanx脚本不存在: {self.wanx_script}")
            return False
        
        # 检查模型目录
        model_dir = self.models_dir / "Wan2.1-T2V-1.3B"
        if not model_dir.exists():
            logger.error(f"模型目录不存在: {model_dir}")
            return False
        
        logger.info("环境检查通过")
        return True
    
    def generate_video(self, request_data):
        """生成视频"""
        try:
            # 解析请求
            prompt = request_data.get("prompt", "test")
            model = request_data.get("model", "t2v-1.3B")
            size = request_data.get("size", "832*480")
            guidance_scale = request_data.get("guidance_scale", 6.0)
            steps = request_data.get("steps", 15)
            frames = request_data.get("frames", 9)
            seed = request_data.get("seed", 12345)
            output_filename = request_data.get("output_filename", f"output_{int(time.time())}.mp4")
            
            logger.info(f"生成视频请求: {prompt}")
            
            # 构建命令
            cmd = [
                "python",
                str(self.wanx_script),
                "--task", model,
                "--size", size,
                "--ckpt_dir", "./Wan2.1-T2V-1.3B",
                "--prompt", prompt,
                "--offload_model", "True",
                "--t5_cpu",
                "--sample_shift", "8",
                "--sample_guide_scale", str(guidance_scale),
                "--sample_steps", str(steps),
                "--frame_num", str(frames),
                "--base_seed", str(seed),
                "--save_file", f"./{output_filename}"
            ]
            
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            # 执行生成
            start_time = time.time()
            result = subprocess.run(
                cmd,
                cwd=str(self.models_dir),
                capture_output=True,
                text=True,
                timeout=180  # 3分钟超时
            )
            
            elapsed_time = time.time() - start_time
            
            # 检查结果
            output_path = self.models_dir / output_filename
            final_output_path = self.output_dir / output_filename
            
            if result.returncode == 0 and output_path.exists():
                # 移动文件到输出目录
                import shutil
                shutil.move(str(output_path), str(final_output_path))
                
                logger.info(f"视频生成成功: {final_output_path} (耗时: {elapsed_time:.1f}秒)")
                
                return {
                    "success": True,
                    "output_filename": output_filename,
                    "elapsed_time": elapsed_time,
                    "message": "生成成功"
                }
            else:
                logger.error(f"视频生成失败: {result.stderr}")
                return {
                    "success": False,
                    "error": result.stderr,
                    "elapsed_time": elapsed_time,
                    "message": "生成失败"
                }
                
        except subprocess.TimeoutExpired:
            logger.error("视频生成超时")
            return {
                "success": False,
                "error": "生成超时",
                "message": "生成超时"
            }
        except Exception as e:
            logger.error(f"视频生成异常: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "生成异常"
            }
    
    def run_server(self):
        """运行服务器模式"""
        logger.info("启动Wanx Docker服务器...")
        
        if not self.check_environment():
            logger.error("环境检查失败")
            return
        
        # 简单的文件监听服务
        request_dir = Path("/app/requests")
        request_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info("等待生成请求...")
        
        while True:
            try:
                # 检查请求文件
                for request_file in request_dir.glob("*.json"):
                    logger.info(f"处理请求: {request_file}")
                    
                    try:
                        # 读取请求
                        with open(request_file, 'r', encoding='utf-8') as f:
                            request_data = json.load(f)
                        
                        # 生成视频
                        result = self.generate_video(request_data)
                        
                        # 写入结果
                        result_file = request_file.with_suffix('.result.json')
                        with open(result_file, 'w', encoding='utf-8') as f:
                            json.dump(result, f, ensure_ascii=False, indent=2)
                        
                        # 删除请求文件
                        request_file.unlink()
                        
                        logger.info(f"请求处理完成: {result_file}")
                        
                    except Exception as e:
                        logger.error(f"处理请求失败: {e}")
                        # 创建错误结果
                        error_result = {
                            "success": False,
                            "error": str(e),
                            "message": "请求处理失败"
                        }
                        result_file = request_file.with_suffix('.error.json')
                        with open(result_file, 'w', encoding='utf-8') as f:
                            json.dump(error_result, f, ensure_ascii=False, indent=2)
                        request_file.unlink()
                
                time.sleep(1)  # 每秒检查一次
                
            except KeyboardInterrupt:
                logger.info("服务器停止")
                break
            except Exception as e:
                logger.error(f"服务器异常: {e}")
                time.sleep(5)
    
    def run_single_generation(self):
        """运行单次生成"""
        logger.info("单次生成模式")
        
        if not self.check_environment():
            logger.error("环境检查失败")
            return
        
        # 从环境变量获取参数
        request_data = {
            "prompt": os.getenv("PROMPT", "a cat running"),
            "model": os.getenv("MODEL", "t2v-1.3B"),
            "size": os.getenv("SIZE", "832*480"),
            "guidance_scale": float(os.getenv("GUIDANCE_SCALE", "6.0")),
            "steps": int(os.getenv("STEPS", "15")),
            "frames": int(os.getenv("FRAMES", "9")),
            "seed": int(os.getenv("SEED", "12345")),
            "output_filename": os.getenv("OUTPUT_FILENAME", "output.mp4")
        }
        
        result = self.generate_video(request_data)
        
        # 输出结果
        print(json.dumps(result, ensure_ascii=False, indent=2))

def main():
    service = WanxDockerService()
    
    # 检查运行模式
    mode = os.getenv("MODE", "server")
    
    if mode == "server":
        service.run_server()
    elif mode == "single":
        service.run_single_generation()
    else:
        logger.error(f"未知模式: {mode}")
        sys.exit(1)

if __name__ == "__main__":
    main()
