﻿<template>
  <div class="video-generation-container">
    <!-- 页面标题 -->
    <PageHeaderGradient 
      title="文生视频创作"
      description="使用先进的WAN 2.1技术，将您的文字描述转换为高质量视�?
    />

    <div class="main-content">
      <!-- 左侧输入区域 -->
      <div class="input-section">
        <el-card class="input-card">
          <template #title>
            <div class="card-title">
              <span>创作参数</span>
              <el-tag type="primary">1.3B模型</el-tag>
            </div>
          </template>
          
          <el-form label-position="top">
            <!-- 提示词输�?-->
            <el-form-item label="提示�? required>
              <el-input
                v-model="prompt"
                type="textarea"
                :rows="5"
                :maxlength="500"
                placeholder="详细描述您想要生成的视频场景、人物、动作和环境..."
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="负面提示�?>
              <el-input
                v-model="negativePrompt"
                type="textarea"
                :rows="2"
                :maxlength="200"
                placeholder="输入不希望在视频中出现的元素..."
                show-word-limit
              />
            </el-form-item>
            
            <el-divider />

            <!-- 视频参数设置 -->
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="视频时长">
                  <el-select v-model="videoParams.duration">
                    <el-option :value="5" label="5�? />
                    <el-option :value="10" label="10�? />
                    <el-option :value="15" label="15�? />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="分辨�?>
                  <el-radio-group v-model="videoParams.resolution">
                    <el-radio-button value="480p">480p</el-radio-button>
                    <el-radio-button value="720p">720p</el-radio-button>
                    <el-radio-button value="1080p">1080p</el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="引导系数">
                  <el-slider
                    v-model="videoParams.guidanceScale"
                    :min="1"
                    :max="10"
                    :step="0.5"
                    :marks="{
                      1: '�?,
                      5: '�?,
                      10: '�?
                    }"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="帧率">
                  <el-select v-model="videoParams.fps">
                    <el-option :value="8" label="8 FPS" />
                    <el-option :value="16" label="16 FPS" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item>
              <el-button
                type="primary"
                style="width: 100%"
                size="large"
                :loading="isGenerating"
                :disabled="!prompt.trim() || isGenerating"
                @click="generateVideo"
              >
                <el-icon><VideoCamera /></el-icon>
                开始生成视�?
              </el-button>
            </el-form-item>

            <el-form-item>
              <el-button
                style="width: 100%"
                :disabled="isGenerating"
                @click="resetForm"
              >
                <el-icon><Refresh /></el-icon>
                重置参数
              </el-button>
            </el-form-item>

            <el-form-item v-if="videoUrl && !videoWorks">
              <el-button
                type="danger"
                style="width: 100%"
                @click="fixBlackScreenIssue"
              >
                <el-icon><Tools /></el-icon>
                修复黑屏问题
              </el-button>
            </el-form-item>
          </el-form>
          
          <el-alert
            v-if="!isGenerating"
            type="info"
            show-icon
            title="提示"
          >
            <p>1. 描述应包含场景、动作、风格等详细信息</p>
            <p>2. 生成过程可能需�?-3分钟，请耐心等待</p>
            <p>3. 使用负面提示词可以排除不需要的元素</p>
          </el-alert>
        </el-card>
      </div>
      
      <!-- 右侧预览区域 -->
      <div class="preview-section">
        <el-card class="preview-card">
          <template #header>
            <div class="card-title">
              <span>视频预览</span>
              <el-tag v-if="generationStatus" :type="generationStatus.type">{{ generationStatus.text }}</el-tag>
            </div>
          </template>
          
          <!-- 空状�?-->
          <div v-if="!videoUrl && !isGenerating" class="empty-container">
            <el-empty description="尚未生成视频">
              <template #image>
                <VideoPlay class="empty-icon" />
              </template>
              <template #description>
                <p>填写左侧创作参数，点�?开始生成视�?按钮开始创�?/p>
              </template>
            </el-empty>
          </div>

          <!-- 生成中状�?-->
          <div v-if="isGenerating" class="generating-container">
            <div class="loading-spinner">
              <el-icon class="is-loading" size="48"><Loading /></el-icon>
            </div>
            <div class="generation-info">
              <h3>视频生成中，请稍�?..</h3>
              <el-progress :percentage="progress" :status="progress === 100 ? 'success' : ''" />
              <p class="progress-text">{{ progressMessage }}</p>
              <p class="time-text">预计剩余时间: {{ remainingTime }} �?/p>
            </div>
          </div>
          
          <!-- 视频预览 -->
          <div v-if="videoUrl && !isGenerating" class="video-container">
            <video
              ref="videoPlayer"
              controls
              class="video-player"
              :src="videoUrl"
              @error="handleVideoError"
              @loadeddata="handleVideoLoaded"
            ></video>
            
            <div class="video-controls">
              <el-space>
                <el-button type="primary" @click="downloadVideo">
                  <el-icon><Download /></el-icon>
                  下载视频
                </el-button>
                <el-button @click="regenerateVideo">
                  <el-icon><Refresh /></el-icon>
                  重新生成
                </el-button>
                <el-button @click="copyPrompt">
                  <el-icon><CopyDocument /></el-icon>
                  复制提示�?
                </el-button>
              </el-space>
            </div>
          </div>
        </el-card>

        <!-- 历史记录 -->
        <el-card v-if="videoHistory.length > 0" class="history-card" header="生成历史">
          <div class="video-grid">
            <div
              v-for="item in videoHistory"
              :key="item.id"
              class="video-item"
              @click="loadHistoryItem(item)"
            >
              <el-card class="video-card" :body-style="{ padding: '0px' }">
                <div class="history-thumbnail">
                  <img
                    :src="item.thumbnailUrl || defaultThumbnail"
                    :alt="item.prompt"
                    @error="handleImageError"
                  />
                  <div class="history-duration">{{ formatDuration(item.duration) }}</div>
                </div>
                <div class="video-info">
                  <div class="video-title">{{ truncateText(item.prompt, 30) }}</div>
                  <div class="video-meta">
                    <div>{{ formatDate(item.createdAt) }}</div>
                    <div>{{ item.resolution }}</div>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  VideoCamera,
  VideoPlay,
  Refresh,
  Download,
  CopyDocument,
  Tools,
  Loading
} from '@element-plus/icons-vue';
import videoService from '../../../api/videoGenerationService.js';
import PageHeaderGradient from '@/components/PageHeaderGradient.vue';

export default defineComponent({
  name: 'VideoGeneration',
  components: {
    VideoCamera,
    VideoPlay,
    Refresh,
    Download,
    CopyDocument,
    Tools,
    Loading,
    PageHeaderGradient
  },
  setup() {
    // 基本状�?
    const prompt = ref('');
    const negativePrompt = ref('');
    const videoUrl = ref('');
    const isGenerating = ref(false);
    const progress = ref(0);
    const progressMessage = ref('准备�?..');
    const currentTaskId = ref('');
    const videoHistory = ref([]);
    const defaultThumbnail = ref('/assets/thumbnails/default.jpg');
    const videoWorks = ref(true);
    
    // 视频参数 - 明确使用1.3B模型
    const videoParams = reactive({
      duration: 10,
      resolution: '480p',
      guidanceScale: 5.0,
      fps: 8,
      model: 'wan2.1',
      modelSize: '1.3B'  // 明确指定使用1.3B模型
    });
    
    // 计算属�?
    const remainingTime = computed(() => {
      // 基于1.3B模型的估计时间计�?
      const baseTime = 30; // 基础时间（秒�?
      const durationFactor = videoParams.duration / 10;
      const resolutionFactor = videoParams.resolution === '1080p' ? 1.5 : 1;
      const estimatedTime = Math.round(baseTime * durationFactor * resolutionFactor);
      
      if (progress.value <= 0) return estimatedTime;
      
      const elapsed = estimatedTime * (progress.value / 100);
      return Math.max(0, Math.round(estimatedTime - elapsed));
    });
    
    const generationStatus = computed(() => {
      if (isGenerating.value) {
        return { text: '生成�?, color: 'processing' };
      }
      if (videoUrl.value) {
        return { text: '已完�?, color: 'success' };
      }
      return null;
    });
    
    // 轮询定时�?
    let progressTimer = null;
    
    // 生命周期钩子
    onMounted(() => {
      // 恢复历史记录加载功能
      loadVideoHistory();
      
      // 监听视频元素的加载完成事�?
      document.addEventListener('loadedmetadata', checkVideoValidity, true);
    });
    
    onUnmounted(() => {
      if (progressTimer) {
        clearInterval(progressTimer);
        progressTimer = null;
      }
      
      // 清除事件监听
      document.removeEventListener('loadedmetadata', checkVideoValidity, true);
    });
    
    // 视频生成方法
    const generateVideo = async () => {
      if (!prompt.value.trim()) {
        ElMessage.warning('请输入提示词');
        return;
      }
      
      try {
        // 清除任何之前可能存在的任务ID
        currentTaskId.value = '';
        
        // 设置生成状�?
        isGenerating.value = true;
        progress.value = 0;
        progressMessage.value = '正在初始化模�?..';
        videoUrl.value = '';
        
        // 映射分辨率格�?
        const resolutionMap = {
          '480p': '768x512',
          '720p': '1280x720',
          '1080p': '1920x1080'
        };

        // 准备API参数 - 映射�?Wanx 2.1 API 格式
        const params = {
          prompt: prompt.value,
          model: 't2v-1.3B',  // 使用 Wanx 2.1 �?1.3B 模型
          duration: videoParams.duration,
          resolution: resolutionMap[videoParams.resolution] || '768x512',
          fps: videoParams.fps,
          guidance_scale: videoParams.guidanceScale,
          num_inference_steps: 50  // 默认推理步数
        };
        
        // 显示提示消息
        ElMessage.info(`正在使用 Wanx 2.1 模型生成视频 (${params.resolution}, ${params.fps}fps, ${params.duration}�?`);
        
        console.log('生成视频参数:', params);
        
        // 发送API请求
        const response = await videoService.generateVideoFromText(params);
        
        if (!response || !response.task_id) {
          throw new Error('服务器返回无效响�?);
        }
        
        // 保存任务ID并开始轮询进�?
        currentTaskId.value = response.task_id;
        console.log('视频生成任务已创�?', currentTaskId.value);
        
        // 只有在获取到有效任务ID时才开始轮�?
        if (currentTaskId.value && !currentTaskId.value.includes('undefined')) {
          startProgressPolling(currentTaskId.value);
        } else {
          console.error('无效的任务ID:', currentTaskId.value);
          handleGenerationError(new Error('获取视频生成任务ID失败'));
        }
        
      } catch (error) {
        handleGenerationError(error);
      }
    };
    
    // 生成完成处理
    const handleGenerationComplete = async (taskId) => {
      try {
        console.log('开始获取任务详�?', taskId);
        const taskDetails = await videoService.getVideoTask(taskId);
        console.log('获取到任务详�?', taskDetails);
        
        // 检查任务状态和视频URL
        if (!taskDetails || !taskDetails.data) {
          throw new Error('获取任务详情失败: 返回数据为空');
        }
        
        const taskData = taskDetails.data;
        console.log('任务数据:', taskData);
        
        // 确认视频URL存在
        if (!taskData.video_url) {
          console.warn('警告: 任务完成但视频URL为空');
          
          // 尝试构造默认URL
          const defaultVideoUrl = `/api/media/videos/${taskId}.mp4`;
          console.log('尝试使用默认视频URL:', defaultVideoUrl);
          
          // 检查默认URL是否可访�?
          try {
            const response = await fetch(defaultVideoUrl, { method: 'HEAD' });
            if (!response.ok) {
              console.warn(`默认视频URL不可访问: ${response.status} ${response.statusText}`);
              
              // 尝试其他可能的URL格式
              const alternativeUrls = [
                `/api/media/video/${taskId}.mp4`,
                `/api/media/files/${taskId}.mp4`,
                `/media/videos/${taskId}.mp4`,
                `/media/video/${taskId}.mp4`
              ];
              
              let accessibleUrl = null;
              for (const url of alternativeUrls) {
                try {
                  console.log(`尝试备用URL: ${url}`);
                  const altResponse = await fetch(url, { method: 'HEAD' });
                  if (altResponse.ok) {
                    console.log(`找到可访问的备用URL: ${url}`);
                    accessibleUrl = url;
                    break;
                  }
                } catch (e) {
                  console.warn(`检查备用URL ${url} 失败:`, e);
                }
              }
              
              if (accessibleUrl) {
                // 更新状�?
                isGenerating.value = false;
                videoUrl.value = accessibleUrl;
                ElMessage.success('找到可访问的视频URL!');
                loadVideoHistory();
                return;
              } else {
                // 如果所有URL都不可访问，尝试重新请求任务进度
                console.log('所有URL都不可访问，等待5秒后重试...');
                ElMessage.info('视频可能仍在处理中，正在重试...');
                await new Promise(resolve => setTimeout(resolve, 5000));
                
                const progressResult = await videoService.getVideoTaskProgress(taskId);
                if (progressResult.status === 'completed' && progressResult.result && progressResult.result.video_url) {
                  console.log('重试成功，获取到视频URL:', progressResult.result.video_url);
                  
                  // 更新状�?
                  isGenerating.value = false;
                  videoUrl.value = progressResult.result.video_url;
                  ElMessage.success('视频生成成功!');
                  loadVideoHistory();
                  return;
                } else {
                  ElMessage.warning('视频文件不可访问，可能尚未生成完成，请稍后刷新页面重试�?);
                }
              }
            }
          } catch (e) {
            console.error('检查视频URL可访问性失�?', e);
          }
          
          // 更新状�?
          isGenerating.value = false;
          videoUrl.value = defaultVideoUrl;
          
          // 显示警告消息
          ElMessage.warning('未获取到视频URL，使用默认URL。如果视频无法播放，请稍后刷新页面重试�?);
          
          // 恢复历史记录刷新
          loadVideoHistory();
          return;
        }
        
        // 处理视频URL
        let finalVideoUrl = taskData.video_url;
        
        // 确保URL是绝对路�?
        if (!finalVideoUrl.startsWith('http')) {
          // 获取API基础URL
          const apiBaseUrl = videoService.getApiBaseUrl();
          
          // 构建完整URL
          if (finalVideoUrl.startsWith('/')) {
            finalVideoUrl = `${apiBaseUrl}${finalVideoUrl}`;
          } else {
            finalVideoUrl = `${apiBaseUrl}/${finalVideoUrl}`;
          }
          
          console.log('转换为绝对URL:', finalVideoUrl);
        }
        
        // 检查视频URL是否可访�?
        try {
          const response = await fetch(finalVideoUrl, { method: 'HEAD' });
          if (!response.ok) {
            console.warn(`视频URL不可访问: ${response.status} ${response.statusText}`);
            // 尝试使用备用URL
            finalVideoUrl = `/api/media/videos/${taskId}.mp4`;
            console.log('使用备用视频URL:', finalVideoUrl);
            
            // 再次检查备用URL是否可访�?
            try {
              const altResponse = await fetch(finalVideoUrl, { method: 'HEAD' });
              if (!altResponse.ok) {
                console.warn(`备用视频URL也不可访�? ${altResponse.status} ${altResponse.statusText}`);
                ElMessage.warning('视频文件可能尚未准备好，请稍后刷新页面重试�?);
              }
            } catch (e) {
              console.error('检查备用URL可访问性失�?', e);
            }
          }
        } catch (e) {
          console.error('检查视频URL可访问性失�?', e);
          // 出错时也尝试使用备用URL
          finalVideoUrl = `/api/media/videos/${taskId}.mp4`;
          console.log('检查失败，使用备用视频URL:', finalVideoUrl);
        }
        
        // 更新状�?
        isGenerating.value = false;
        videoUrl.value = finalVideoUrl;
        
        // 检查生成过程中是否发生了张量形状不匹配问题
        if (taskData.logs && Array.isArray(taskData.logs) && hasTensorShapeMismatch(taskData.logs)) {
          console.warn('视频生成过程中出现张量形状不匹配问题，可能影响视频质�?);
          // 显示带有警告的成功消�?
          ElMessage.success({
            content: '视频生成完成，但过程中存在形状不匹配问题，可能影响视频质�?,
            duration: 5
          });
        } else {
          // 显示普通成功消�?
          ElMessage.success('视频生成成功!');
        }
        
        // 恢复历史记录刷新
        loadVideoHistory();
        
      } catch (error) {
        console.error('获取任务详情失败:', error);
        
        // 尝试使用默认URL
        const defaultVideoUrl = `/api/media/videos/${currentTaskId.value}.mp4`;
        console.log('错误恢复：尝试使用默认视频URL:', defaultVideoUrl);
        
        // 更新状�?
        isGenerating.value = false;
        videoUrl.value = defaultVideoUrl;
        
        // 显示警告消息
        ElMessage.warning('获取视频详情失败，使用默认URL。如果视频无法播放，请稍后刷新页面重试�?);
        
        // 恢复历史记录刷新
        loadVideoHistory();
      }
    };
    
    // 进度轮询
    const startProgressPolling = async (taskId) => {
      // 清除现有定时�?
      if (progressTimer) {
        clearInterval(progressTimer);
      }
      
      // 设置初始进度�?
      progress.value = 0;
      
      try {
        console.log('开始任务轮�?', taskId);
        progressMessage.value = '正在启动任务...';
        
        // 直接使用自动轮询模式获取任务进度，不需要先检查状�?
        // 这将持续轮询直到任务完成或失�?
        progress.value = 5; // 设置初始进度指示
        progressMessage.value = '任务已创建，等待处理...';
        
        console.log('启动自动轮询模式，将持续到任务完�?);
        const finalStatus = await videoService.getVideoTaskProgress(taskId, true);
        
        console.log('自动轮询完成，最终状�?', finalStatus);
        
        // 任务完成，处理结�?
        if (finalStatus.status === 'completed' || finalStatus.status === 'COMPLETED' || 
            finalStatus.status === 'SUCCESS') {
          progress.value = 100;
          await handleGenerationComplete(taskId);
        } else if (finalStatus.status === 'failed' || finalStatus.status === 'FAILED' || 
                   finalStatus.status === 'ERROR') {
          handleGenerationFailed(finalStatus.error || '生成失败');
        } else {
          // 如果状态仍然不是完成或失败，回退到常规轮�?
          console.log('自动轮询未能完成任务，回退到常规轮�?);
          setUpRegularPolling(taskId);
        }
      } catch (error) {
        console.error('自动轮询失败，回退到常规轮�?', error);
        setUpRegularPolling(taskId);
      }
    };
    
    // 设置常规轮询
    const setUpRegularPolling = (taskId) => {
      // 创建一个轮询状态对象，跟踪轮询次数和最后进�?
      const pollState = {
        count: 0,
        lastProgress: 0,
        lastUpdate: Date.now(),
        sameProgressCount: 0,
        noResponseCount: 0,
        maxRetries: 60,  // 最大重试次�?120�?
        maxSameProgress: 15,  // 最大相同进度次�?30�?
        progressSteps: [25, 50, 75, 90, 95, 98, 100], // 用于进度卡住时的进度估算�?
        currentStepIndex: 0,  // 当前进度估算点索�?
        stuckDetectionThreshold: 10, // 检测卡住的阈�?轮询次数)
        // 用于进度值推进的参数
        minProgressIncrement: 0.5, // 最小进度增�?
        maxProgressIncrement: 2.0  // 最大进度增�?
      };
      
      // 定义检查函�?
      const checkProgress = async () => {
        try {
          // 确保taskId有效
          if (!taskId || typeof taskId !== 'string' || taskId.includes('undefined')) {
            console.error('无效的任务ID，无法检查进�?', taskId);
            return false;
          }
          
          pollState.count++;
          
          // 记录开始时间，用于计算请求时长
          const requestStart = Date.now();
          
          const result = await videoService.getVideoTaskProgress(taskId);
          
          // 记录请求时长
          const requestTime = Date.now() - requestStart;
          console.log(`进度请求耗时: ${requestTime}ms`);
          
          // 验证结果是否有效
          if (!result) {
            console.error('进度查询返回空数�?);
            // 继续轮询，不报错中断
            return false;
          }
          
          // 请求成功，重置无响应计数
          pollState.noResponseCount = 0;
          
          // 检查任务是否不存在或已被删�?
          if (result._not_found || result.status === 'not_found') {
            console.error('任务不存在或已被删除:', taskId);
            handleGenerationFailed('任务不存在或已被删除');
            return true;
          }
          
          // 更新进度
          if (typeof result.progress === 'number') {
            progress.value = result.progress || 0;
          } else {
            // 如果进度不是数字，尝试一些启发式方法估算进度
            if (result.status === 'completed') {
              progress.value = 100;
            } else if (result.status === 'processing') {
              // 如果状态是处理中但没有进度，使用启发式值或递增进度
              progress.value = Math.min(progress.value + 1, 99); // 不超�?9%
            }
          }
          
          // 检查日志中是否有张量形状不匹配的问�?
          if (result.logs && Array.isArray(result.logs) && hasTensorShapeMismatch(result.logs)) {
            console.warn('检测到WAN生成过程中出现张量形状不匹配问题');
            // 在进度消息中添加提示信息
            let originalMessage = progressMessage.value;
            if (!originalMessage.includes('形状不匹�?)) {
              progressMessage.value = `${originalMessage} (检测到形状不匹配问题，使用随机噪声替代)`;
            }
          }
          
          // 检查进度是否增�?
          if (progress.value > pollState.lastProgress) {
            // 进度增加，更新lastProgress和lastUpdate
            pollState.lastProgress = progress.value;
            pollState.lastUpdate = Date.now();
            pollState.sameProgressCount = 0;
            
            // 如果进度超过了当前进度点，更新进度点索引
            while (pollState.currentStepIndex < pollState.progressSteps.length && 
                    progress.value >= pollState.progressSteps[pollState.currentStepIndex]) {
              pollState.currentStepIndex++;
            }
          } else if (progress.value === pollState.lastProgress) {
            // 进度没变，增加相同计�?
            pollState.sameProgressCount++;
          }
          
          // 更新进度消息
          progressMessage.value = result.message || 
                                result.logs?.[result.logs.length-1]?.message || 
                                `处理�?.. (${result.status || 'pending'})`;
          
          console.log(`进度更新: ${progress.value}%, ${progressMessage.value}, 状�? ${result.status}, 轮询次数: ${pollState.count}`);
          
          // 处理进度长时间不变的情况
          if (pollState.sameProgressCount >= pollState.stuckDetectionThreshold && progress.value > 0 && progress.value < 100) {
            console.warn(`警告: 进度已在${progress.value}%停滞${pollState.sameProgressCount * 2}秒，可能存在问题`);
            
            // 根据进度值决定如何处�?
            if (progress.value >= 80) {
              try {
                // 高进度卡住，检查任务是否已完成但未收到通知
                console.log('进度已经很高且长时间未变化，检查任务是否已实际完成');
                const taskDetails = await videoService.getVideoTask(taskId);
                if (taskDetails?.data?.status === 'completed') {
                  console.log('检测到任务已实际完成，但进度未更新�?00%');
                  progress.value = 100;
                  await handleGenerationComplete(taskId);
                  return true;
                }
              } catch (error) {
                console.error('检查任务详情失�?', error);
              }
            }
            
            // 增加进度值以提供反馈
            if (progress.value < 98) {
              // 计算应该到达的下一个进度点
              const nextStep = pollState.progressSteps.find(step => step > progress.value) || 99;
              
              // 计算距离下一个进度点的距�?
              const distanceToNextStep = nextStep - progress.value;
              
              // 计算一个合理的增量，进度越接近目标点，增量越小
              let increment = Math.max(
                pollState.minProgressIncrement,
                Math.min(
                  distanceToNextStep * 0.1, // 最多一次增加距离的10%
                  pollState.maxProgressIncrement
                )
              );
              
              // 应用增量
              progress.value = Math.min(progress.value + increment, nextStep - 0.5);
              console.log(`手动增加进度�?${progress.value.toFixed(1)}% 以提供用户反馈，目标进度�? ${nextStep}%`);
            }
          }
          
          // 检查状�?
          if (result.status === 'completed') {
            console.log('任务标记为已完成，准备获取视�?);
            // 确保进度显示�?00%
            progress.value = 100;
            await handleGenerationComplete(taskId);
            return true;
          } else if (result.status === 'failed') {
            console.error('任务失败:', result.error || '未知错误');
            handleGenerationFailed(result.error || '生成失败');
            return true;
          } else if (result.status === 'pending' && pollState.count > 5) {
            // 如果5次请求后仍然是pending状态，可能存在问题
            console.warn(`任务连续${pollState.count}次仍在pending状�?..`);
            
            // 如果长时间pending(>30�?，提供一些进度反�?
            if (pollState.count > 15) {
              // 轻微增加进度值，不超�?5%
              progress.value = Math.min(progress.value + 0.5, 25);
              console.log(`任务长时间pending，手动增加进度至 ${progress.value}% 以提供用户反馈`);
            }
          }
          
          // 超过最大重试次数但任务仍在处理�?
          if (pollState.count >= pollState.maxRetries && result.status === 'processing') {
            console.warn(`已达到最大轮询次�?${pollState.maxRetries})，但任务仍在处理中`);
            
            try {
              // 尝试直接查询任务详情
              const taskDetails = await videoService.getVideoTask(taskId);
              if (taskDetails?.data?.status === 'completed') {
                console.log('通过直接查询发现任务已完�?);
                progress.value = 100;
                await handleGenerationComplete(taskId);
                return true;
              } else if (progress.value >= 50) {
                // 如果进度已经超过50%，假设任务正常进行中，给出友好提示并继续轮询
                ElMessage.info('视频生成仍在进行中，可能需要更长时�?..');
                
                // 延长轮询时间，减少频�?
                if (progressTimer) {
                  clearInterval(progressTimer);
                }
                progressTimer = setInterval(async () => {
                  const isFinished = await checkProgress();
                  if (isFinished && progressTimer) {
                    clearInterval(progressTimer);
                    progressTimer = null;
                  }
                }, 5000); // 改为5秒检查一�?
                
                return false;
              }
            } catch (error) {
              console.error('直接查询任务详情失败:', error);
            }
            
            // 如果上述尝试都失败，停止轮询并通知用户
            ElMessage.warning('任务处理时间超过预期，请稍后在历史记录中查看结果');
            isGenerating.value = false;
            if (progressTimer) {
              clearInterval(progressTimer);
              progressTimer = null;
            }
            return true;
          }
          
          return false;
        } catch (error) {
          console.error('获取进度失败:', error);
          pollState.noResponseCount++;
          
          // 检查是否包含张量形状不匹配的错误信�?
          if (error.response?.data?.logs && hasTensorShapeMismatch(error.response.data.logs)) {
            console.warn('检测到张量形状不匹配问题，显示相关提示');
            progressMessage.value = '模型生成过程中存在张量形状不匹配，正在使用随机噪声替代，生成质量可能受影�?;
          }
          
          // 连续3次无响应，考虑是网络问题或后端问题
          if (pollState.noResponseCount >= 3) {
            console.warn('连续多次请求失败，可能是网络问题或后端服务问�?);
            progressMessage.value = '连接服务器失败，正在重试...';
          }
          
          // 如果连续多次请求失败，可能需要终止轮�?
          if (pollState.noResponseCount >= 10) {
            console.error('连续10次请求失败，终止轮询');
            ElMessage.error('无法连接到服务器，请稍后在历史记录中查看结果');
            isGenerating.value = false;
            return true;
          }
          
          return false;
        }
      };
      
      // 立即执行一次检�?
      checkProgress().then(finished => {
        if (finished) {
          return;
        }
        
        // 设置轮询定时�?
        progressTimer = setInterval(async () => {
          try {
            const isFinished = await checkProgress();
            if (isFinished && progressTimer) {
              clearInterval(progressTimer);
              progressTimer = null;
            }
          } catch (error) {
            console.error('轮询出错:', error);
          }
        }, 2000);
      });
    };
    
    // 辅助函数：检查日志中是否包含张量形状不匹配的信息
    const hasTensorShapeMismatch = (logs) => {
      if (!logs || typeof logs !== 'object') return false;
      
      // 如果是数组，遍历检查每条日�?
      if (Array.isArray(logs)) {
        return logs.some(log => {
          if (typeof log === 'string') {
            return log.includes('形状不匹�?) || 
                   log.includes('shape mismatch') || 
                   log.includes('size mismatch') ||
                   log.includes('tensor') && log.includes('shape');
          }
          if (typeof log === 'object' && log?.message) {
            return log.message.includes('形状不匹�?) || 
                   log.message.includes('shape mismatch') || 
                   log.message.includes('size mismatch') ||
                   log.message.includes('tensor') && log.message.includes('shape');
          }
          return false;
        });
      }
      
      // 如果是单个日志对�?
      if (typeof logs === 'string') {
        return logs.includes('形状不匹�?) || 
               logs.includes('shape mismatch') || 
               logs.includes('size mismatch') ||
               logs.includes('tensor') && logs.includes('shape');
      }
      
      // 如果logs有日志信息字�?
      if (logs.logs && Array.isArray(logs.logs)) {
        return hasTensorShapeMismatch(logs.logs);
      }
      
      return false;
    };
    
    // 错误处理
    const handleGenerationFailed = (errorMsg) => {
      isGenerating.value = false;
      progress.value = 0;
      ElMessage.error(errorMsg || '视频生成失败');
    };
    
    const handleGenerationError = (error) => {
      console.error('生成视频错误:', error);
      isGenerating.value = false;
      progress.value = 0;
      
      const errorMessage = error.response?.data?.detail || 
                         error.response?.data?.message || 
                         error.message || 
                         '生成视频请求失败';
                         
      ElMessage.error(errorMessage);
    };
    
    const handleVideoError = (e) => {
      console.error('视频加载失败:', e);
      ElMessage.error('视频加载失败');
      videoWorks.value = false; // 标记视频不可�?
    };
    
    const handleImageError = (e) => {
      e.target.src = defaultThumbnail.value;
    };
    
    // 表单操作
    const resetForm = () => {
      prompt.value = '';
      negativePrompt.value = '';
      videoParams.duration = 10;
      videoParams.resolution = '480p';
      videoParams.guidanceScale = 5.0;
      videoParams.fps = 8;
      
      if (!isGenerating.value) {
        videoUrl.value = '';
      }
    };
    
    const regenerateVideo = () => {
      if (isGenerating.value) return;
      
      videoUrl.value = '';
      generateVideo();
    };
    
    // 视频操作
    const downloadVideo = () => {
      if (!videoUrl.value) return;
      
      const a = document.createElement('a');
      a.href = videoUrl.value;
      a.download = `WAN2.1_视频_${Date.now()}.mp4`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      
      ElMessage.success('开始下载视�?);
    };
    
    const copyPrompt = () => {
      navigator.clipboard.writeText(prompt.value)
        .then(() => ElMessage.success('提示词已复制到剪贴板'))
        .catch(() => ElMessage.error('复制失败'));
    };
    
    // 历史记录
    const loadVideoHistory = async () => {
      try {
        console.log('开始加载视频历史记�?..');
        const response = await videoService.getVideoTasks({ limit: 12 });
        console.log('获取到视频任务列表响�?', response);
        
        let tasks = [];
        // 更强健的响应处理
        if (!response) {
          console.warn('获取历史记录失败: 响应为空');
          videoHistory.value = [];
          return;
        }
        
        // 处理不同格式的响�?
        if (Array.isArray(response)) {
          tasks = response;
          console.log('使用数组响应:', tasks.length, '条记�?);
        } else if (response?.videos && Array.isArray(response.videos)) {
          tasks = response.videos;
          console.log('使用response.videos数组:', tasks.length, '条记�?);
        } else if (response?.data && Array.isArray(response.data)) {
          tasks = response.data;
          console.log('使用response.data数组:', tasks.length, '条记�?);
        } else if (response?.items && Array.isArray(response.items)) {
          tasks = response.items;
          console.log('使用response.items数组:', tasks.length, '条记�?);
        } else {
          console.warn('无法解析历史记录响应:', response);
          videoHistory.value = [];
          return;
        }
        
        // 如果任务列表为空，直接返�?
        if (!tasks || tasks.length === 0) {
          console.log('任务列表为空');
          videoHistory.value = [];
          return;
        }
        
        console.log('准备处理', tasks.length, '条任务记�?);
        
        videoHistory.value = tasks.map(task => {
          if (!task) {
            console.warn('跳过无效任务记录');
            return null;
          }
          
          // 确保parameters字段是对象而不是字符串
          let parameters = {};
          if (task.parameters) {
            if (typeof task.parameters === 'string') {
              try {
                parameters = JSON.parse(task.parameters);
                console.log('成功将parameters字符串解析为对象');
              } catch (e) {
                console.error('解析parameters失败:', e);
                // 创建一个基本的参数对象
                parameters = {
                  num_frames: 80,
                  height: 720,
                  width: 1280,
                  guidance_scale: 5.0,
                  fps: 8,
                  model_size: '1.3B',
                  resolution: '480p',
                  model: 'wan2.1'
                };
              }
            } else {
              parameters = task.parameters;
            }
          } else {
            // 如果没有parameters字段，创建一个默认对�?
            parameters = {
              num_frames: 80,
              height: 720,
              width: 1280,
              guidance_scale: 5.0,
              fps: 8,
              model_size: '1.3B',
              resolution: '480p',
              model: 'wan2.1'
            };
          }
          
          return {
            id: task.task_id || task.id,
            prompt: task.prompt || '未知提示�?,
            thumbnailUrl: task.thumbnail_url || '',
            videoUrl: task.video_url || '',
            createdAt: task.created_at || new Date().toISOString(),
            status: task.status || 'unknown',
            // 使用处理后的parameters对象
            parameters,
            // 计算视频时长和分辨率
            duration: parameters?.num_frames ? parameters.num_frames / (parameters.fps || 8) : 10,
            resolution: parameters?.width ? `${parameters.width}x${parameters.height}` : '1280x720'
          };
        }).filter(item => item !== null); // 过滤掉无效记�?
        
        console.log('历史记录加载完成, �?, videoHistory.value.length, '条有效记�?);
      } catch (error) {
        console.error('加载历史记录失败:', error);
        // 出错时清空历史记�?
        videoHistory.value = [];
        // 显示错误消息
        ElMessage.error('加载历史记录失败，请稍后重试');
      }
    };
    
    const loadHistoryItem = (item) => {
      if (isGenerating.value) {
        ElMessage.warning('正在生成视频，请稍后再试');
        return;
      }
      
      if (item.status === 'completed' && item.videoUrl) {
        prompt.value = item.prompt;
        videoUrl.value = item.videoUrl;
        
        // 如果有负面提示词，也加载�?
        if (item.parameters && item.parameters.negative_prompt) {
          negativePrompt.value = item.parameters.negative_prompt;
        }
        
        // 从历史记录中加载参数
        if (item.parameters) {
          // 设置分辨�?
          if (item.parameters.height === 1080 || item.parameters.height === 1920) {
            videoParams.resolution = '1080p';
          } else {
            videoParams.resolution = '720p';
          }
          
          // 设置帧率
          if (item.parameters.fps) {
            videoParams.fps = item.parameters.fps;
          }
          
          // 设置引导比例
          if (item.parameters.guidance_scale) {
            videoParams.guidanceScale = item.parameters.guidance_scale;
          }
          
          // 设置时长
          if (item.parameters.num_frames && item.parameters.fps) {
            videoParams.duration = Math.round(item.parameters.num_frames / item.parameters.fps);
          }
        }
      } else {
        ElMessage.info(`该任务状�? ${item.status}`);
      }
    };
    
    // 辅助函数
    const truncateText = (text, length) => {
      if (!text) return '';
      return text.length > length ? text.substring(0, length) + '...' : text;
    };
    
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return date.toLocaleDateString();
    };
    
    const formatDuration = (seconds) => {
      if (!seconds) return '0:00';
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    };
    
    // 检查视频是否有效（处理黑屏情况�?
    const checkVideoValidity = () => {
      const video = document.querySelector('video');
      if (video) {
        // 检查视频播放时长是否小�?.5秒（黑屏通常只有1秒左右）
        if (video.duration && video.duration < 1.5) {
          console.warn('检测到视频异常短（可能是黑屏）');
          ElMessage.warning({
            content: '检测到视频异常短（可能是黑屏），可能是生成出现问题，建议增加帧数再�?,
            duration: 5
          });
        }
        
        // 检查视频第一帧是否接近全�?
        try {
          const canvas = document.createElement('canvas');
          const context = canvas.getContext('2d');
          if (context) {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            context.drawImage(video, 0, 0, canvas.width, canvas.height);
            const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;
            
            // 计算平均亮度
            let totalBrightness = 0;
            const pixelCount = data.length / 4; // RGBA数据，每4个值表示一个像�?
            
            for (let i = 0; i < data.length; i += 4) {
              const r = data[i];
              const g = data[i + 1];
              const b = data[i + 2];
              // 计算亮度: (0.299*R + 0.587*G + 0.114*B)
              const brightness = 0.299 * r + 0.587 * g + 0.114 * b;
              totalBrightness += brightness;
            }
            
            const averageBrightness = totalBrightness / pixelCount;
            
            // 如果平均亮度低于30（在0-255范围内），可能是黑屏
            if (averageBrightness < 30) {
              console.warn(`检测到视频可能是黑屏（平均亮度: ${averageBrightness.toFixed(2)}）`);
              ElMessage.warning({
                content: '检测到视频可能是黑屏，建议增加帧数（至�?4帧）和采样步数（至少40步）再试',
                duration: 7
              });
            }
          }
        } catch (e) {
          console.error('检查视频亮度时出错:', e);
        }
      }
    };

    // 在视频加载完成后检查其有效�?
    const handleVideoLoaded = () => {
      checkVideoValidity();
    };
    
    // 修复黑屏问题
    const fixBlackScreenIssue = async () => {
      try {
        ElMessage.loading('正在应用修复...');
        
        // 先获取系统状态，检查修复是否已应用
        try {
          const statusResponse = await fetch('/api/video-generation/system-status');
          if (statusResponse.ok) {
            const statusInfo = await statusResponse.json();
            console.log('系统状�?', statusInfo);
            
            // 检查修复是否已应用
            if (statusInfo.fixes_applied && statusInfo.fixes_applied.fix_dimensions) {
              ElMessage.info('修复已应用，正在检查并更新配置...');
            } else {
              ElMessage.warning('未检测到修复模块，将尝试安装修复');
            }
            
            // 检查WAN模型配置
            if (statusInfo.wan_model) {
              console.log('WAN模型信息:', statusInfo.wan_model);
              // 显示确认对话框，显示发现的模型信�?
              const displayModelInfo = `
                已检测到WAN 2.1模型:
                - 基本配置: ${statusInfo.wan_model.base_dir_exists ? '正常' : '异常'}
                - 1.3B模型: ${statusInfo.wan_model.models['1.3B'].exists ? '已安�? : '未安�?}
                - 14B模型: ${statusInfo.wan_model.models['14B'].exists ? '已安�? : '未安�?}
                - 生成脚本修复: ${statusInfo.wan_model.fix_script_applied ? '已应�? : '未应�?}
                
                点击确认继续应用修复...
              `;
              
              // 使用确认框展示信�?
              if (window.confirm(displayModelInfo)) {
                console.log('用户确认应用修复');
              } else {
                ElMessage.info('用户取消了修复操�?);
                return;
              }
            }
          }
        } catch (statusError) {
          console.error('获取系统状态失�?', statusError);
          // 即使获取状态失败，也继续尝试修�?
        }
        
        // 使用API修复黑屏问题
        const response = await fetch('/api/video-generation/fix-wan-video', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            task_id: currentTaskId.value,
            prompt: prompt.value,
            negative_prompt: negativePrompt.value
          })
        });
        
        if (!response.ok) {
          throw new Error('服务器修复请求失�?);
        }
        
        const result = await response.json();
        
        if (result.success) {
          ElMessage.success('修复已应用，请重新生成视�?);
          // 重置视频URL让用户可以重�?
          videoUrl.value = '';
        } else {
          ElMessage.error(result.message || '修复失败，请联系管理�?);
        }
      } catch (error) {
        console.error('修复黑屏问题出错:', error);
        ElMessage.error('修复过程中出错，尝试以下步骤�?. 使用更简单的提示�?2. 增加帧数 3. 降低分辨�?);
      }
    };
    
    return {
      // 状�?
      prompt,
      negativePrompt,
      videoParams,
      isGenerating,
      progress,
      progressMessage,
      videoUrl,
      videoHistory,
      defaultThumbnail,
      videoWorks,
      
      // 计算属�?
      remainingTime,
      generationStatus,
      
      // 方法
      generateVideo,
      resetForm,
      regenerateVideo,
      downloadVideo,
      copyPrompt,
      loadHistoryItem,
      handleVideoError,
      handleImageError,
      fixBlackScreenIssue,
      
      // 辅助函数
      truncateText,
      formatDate,
      formatDuration
    };
  }
});
</script>

<style scoped>
.video-generation-container {
  padding: 24px;
}

.main-content {
  display: grid;
  grid-template-columns: 400px 1fr;
  gap: 24px;
  margin-top: 24px;
}

.input-section {
  min-width: 400px;
}

.preview-section {
  min-width: 0;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.page-header {
  margin-bottom: 24px;
  text-align: center;
}

.page-header h1 {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  background: linear-gradient(90deg, #1890ff, #722ed1);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-card,
.preview-card,
.history-card {
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.empty-container {
  padding: 48px 0;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  color: #d9d9d9;
}

.generating-container {
  padding: 48px 0;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.generation-info {
  width: 80%;
  max-width: 500px;
}

.progress-text {
  margin-top: 16px;
  color: rgba(0, 0, 0, 0.65);
}

.time-text {
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
}

.video-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.video-player {
  width: 100%;
  max-height: 500px;
  background: #000;
  border-radius: 4px;
}

.video-controls {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

.history-card {
  margin-top: 24px;
}

.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.video-item {
  cursor: pointer;
}

.video-card {
  transition: all 0.3s ease;
}

.video-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-info {
  padding: 12px;
}

.video-title {
  font-weight: 500;
  margin-bottom: 8px;
  color: #303133;
}

.video-meta {
  font-size: 12px;
  color: #909399;
}

.video-meta div {
  margin-bottom: 4px;
}

.history-thumbnail {
  position: relative;
  height: 120px;
  overflow: hidden;
  background: #f0f0f0;
}

.history-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.history-duration {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
}

@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .input-section {
    min-width: auto;
  }
}

@media (max-width: 768px) {
  .video-generation-container {
    padding: 12px;
  }
}
</style> 
