<template>
  <div class="video-generation-container">
    <!-- 页面标题 -->
    <PageHeaderGradient 
      title="文生视频创作"
      description="使用先进的WAN 2.1技术，将您的文字描述转换为高质量视频"
    />

    <div class="main-content">
      <!-- 左侧输入区域 -->
      <div class="input-section">
        <el-card class="input-card">
          <template #title>
            <div class="card-title">
              <span>创作参数</span>
              <el-tag type="primary">1.3B模型</el-tag>
            </div>
          </template>
          
          <el-form label-position="top">
            <!-- 提示词输入 -->
            <el-form-item label="提示词" required>
              <el-input
                v-model="prompt"
                type="textarea"
                :rows="4"
                placeholder="描述您想要生成的视频内容，例如：一只可爱的小猫在花园里玩耍"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>

            <el-form-item label="负面提示词">
              <el-input
                v-model="negativePrompt"
                type="textarea"
                :rows="2"
                placeholder="描述您不希望出现在视频中的内容，例如：模糊、低质量、变形"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>

            <!-- 视频参数 -->
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="视频时长">
                  <el-select v-model="videoParams.duration">
                    <el-option :value="5" label="5秒" />
                    <el-option :value="10" label="10秒" />
                    <el-option :value="15" label="15秒" />
                  </el-select>
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="分辨率">
                  <el-radio-group v-model="videoParams.resolution">
                    <el-radio-button value="480p">480p</el-radio-button>
                    <el-radio-button value="720p">720p</el-radio-button>
                    <el-radio-button value="1080p">1080p</el-radio-button>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="引导强度">
              <el-slider
                v-model="videoParams.guidance"
                :min="1"
                :max="10"
                :step="0.5"
                :marks="{
                  1: '低',
                  5: '中',
                  10: '高'
                }"
              />
            </el-form-item>

            <!-- 生成按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="isGenerating"
                @click="generateVideo"
                :disabled="!prompt.trim()"
                class="generate-btn"
              >
                <el-icon><VideoCamera /></el-icon>
                开始生成视频
              </el-button>
            </el-form-item>

            <!-- 操作按钮 -->
            <el-form-item>
              <el-space>
                <el-button @click="loadVideoHistory">
                  <el-icon><Refresh /></el-icon>
                  刷新历史
                </el-button>
                <el-button @click="fixBlackScreenIssue">
                  <el-icon><Tools /></el-icon>
                  修复黑屏
                </el-button>
              </el-space>
            </el-form-item>
          </el-form>

          <!-- 提示信息 -->
          <el-alert
            title="生成提示"
            type="info"
            :closable="false"
            show-icon
          >
            <p>1. 描述应包含场景、动作、风格等详细信息</p>
            <p>2. 生成过程可能需要2-3分钟，请耐心等待</p>
            <p>3. 使用负面提示词可以排除不需要的元素</p>
          </el-alert>
        </el-card>
      </div>

      <!-- 右侧预览区域 -->
      <div class="preview-section">
        <el-card class="preview-card">
          <template #title>
            <div class="card-title">
              <span>视频预览</span>
              <el-tag v-if="generationStatus" :type="generationStatus.color">
                {{ generationStatus.text }}
              </el-tag>
            </div>
          </template>
          
          <!-- 空状态 -->
          <div v-if="!videoUrl && !isGenerating" class="empty-container">
            <el-empty description="尚未生成视频">
              <template #image>
                <VideoPlay class="empty-icon" />
              </template>
              <template #description>
                <p>填写左侧创作参数，点击"开始生成视频"按钮开始创作</p>
              </template>
            </el-empty>
          </div>

          <!-- 生成中状态 -->
          <div v-if="isGenerating" class="generating-container">
            <div class="loading-spinner">
              <el-icon class="is-loading"><Loading /></el-icon>
            </div>
            <div class="generation-info">
              <h3>视频生成中，请稍候...</h3>
              <el-progress :percentage="progress" :status="progress === 100 ? 'success' : ''" />
              <p class="progress-text">{{ progressMessage }}</p>
              <p class="time-text">预计剩余时间: {{ remainingTime }} 秒</p>
            </div>
          </div>

          <!-- 视频预览 -->
          <div v-if="videoUrl && !isGenerating" class="video-container">
            <video
              :src="videoUrl"
              controls
              autoplay
              loop
              muted
              class="preview-video"
              @loadedmetadata="handleVideoLoaded"
              @error="handleVideoError"
            />
            
            <div class="video-actions">
              <el-space>
                <el-button @click="downloadVideo">
                  <el-icon><Download /></el-icon>
                  下载视频
                </el-button>
                <el-button @click="regenerateVideo">
                  <el-icon><Refresh /></el-icon>
                  重新生成
                </el-button>
                <el-button @click="copyPrompt">
                  <el-icon><CopyDocument /></el-icon>
                  复制提示词
                </el-button>
              </el-space>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 历史记录 -->
    <div class="history-section">
      <el-card>
        <template #title>
          <div class="card-title">
            <span>历史记录</span>
            <el-tag>{{ videoHistory.length }} 个视频</el-tag>
          </div>
        </template>
        
        <div v-if="videoHistory.length === 0" class="empty-history">
          <el-empty description="暂无历史记录" />
        </div>
        
        <div v-else class="history-grid">
          <div
            v-for="item in videoHistory"
            :key="item.id"
            class="history-item"
            @click="loadHistoryItem(item)"
          >
            <div class="history-thumbnail">
              <img v-if="item.thumbnailUrl" :src="item.thumbnailUrl" alt="缩略图" />
              <div v-else class="placeholder-thumbnail">
                <VideoPlay />
              </div>
            </div>
            <div class="history-info">
              <p class="history-prompt">{{ item.prompt }}</p>
              <div class="history-meta">
                <span>{{ item.duration }}s</span>
                <span>{{ item.resolution }}</span>
                <span>{{ item.createdAt }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, computed, onMounted, onUnmounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  VideoCamera,
  VideoPlay,
  Refresh,
  Download,
  CopyDocument,
  Tools,
  Loading
} from '@element-plus/icons-vue';
import PageHeaderGradient from '@/components/PageHeaderGradient.vue';
import videoService from '@/services/video-service.js';

export default defineComponent({
  name: 'VideoGeneration',
  components: {
    VideoCamera,
    VideoPlay,
    Refresh,
    Download,
    CopyDocument,
    Tools,
    Loading,
    PageHeaderGradient
  },
  setup() {
    // 基本状态
    const prompt = ref('');
    const negativePrompt = ref('');
    const videoUrl = ref('');
    const isGenerating = ref(false);
    const progress = ref(0);
    const progressMessage = ref('准备中...');
    const currentTaskId = ref('');
    const videoHistory = ref([]);
    const videoWorks = ref(true);

    // 视频参数
    const videoParams = reactive({
      duration: 10,
      resolution: '720p',
      guidance: 7.5
    });
    
    // 计算属性
    const remainingTime = computed(() => {
      // 基于1.3B模型的估计时间计算
      const baseTime = 30; // 基础时间（秒）
      const durationFactor = videoParams.duration / 10;
      const resolutionFactor = videoParams.resolution === '1080p' ? 1.5 : 1;
      const estimatedTotal = baseTime * durationFactor * resolutionFactor;
      const remaining = Math.max(0, estimatedTotal * (1 - progress.value / 100));
      return Math.round(remaining);
    });

    const generationStatus = computed(() => {
      if (isGenerating.value) {
        return { text: '生成中', color: 'processing' };
      }
      if (videoUrl.value) {
        return { text: '已完成', color: 'success' };
      }
      return null;
    });
    
    // 轮询定时器
    let progressTimer = null;
    
    // 页面加载时获取历史记录
    onMounted(() => {
      loadVideoHistory();
      
      // 监听视频元素的加载完成事件
      document.addEventListener('loadedmetadata', checkVideoValidity, true);
    });

    // 页面卸载时清理定时器
    onUnmounted(() => {
      if (progressTimer) {
        clearInterval(progressTimer);
        progressTimer = null;
      }
    });

    // 生成视频
    const generateVideo = async () => {
      if (!prompt.value.trim()) {
        ElMessage.warning('请输入提示词');
        return;
      }

      try {
        // 清理之前的状态
        if (progressTimer) {
          clearInterval(progressTimer);
          progressTimer = null;
        }
        currentTaskId.value = '';

        // 设置生成状态
        isGenerating.value = true;
        progress.value = 0;
        progressMessage.value = '正在初始化模型...';
        videoUrl.value = '';

        // 映射分辨率格式
        const resolutionMap = {
          '480p': '768x512',
          '720p': '1280x720',
          '1080p': '1920x1080'
        };

        // 准备API参数 - 映射到Wanx 2.1 API 格式
        const params = {
          prompt: prompt.value,
          model: 't2v-1.3B',  // 使用 Wanx 2.1 的1.3B 模型
          duration: videoParams.duration,
          resolution: resolutionMap[videoParams.resolution] || '768x512',
          fps: 24,
          guidance_scale: videoParams.guidance,
          negative_prompt: negativePrompt.value || ''
        };

        // 显示提示消息
        ElMessage.info(`正在使用 Wanx 2.1 模型生成视频 (${params.resolution}, ${params.fps}fps, ${params.duration}秒)`);

        console.log('生成视频参数:', params);

        // 调用视频生成API
        const response = await videoService.generateVideo(params);

        if (!response || !response.task_id) {
          throw new Error('服务器返回无效响应');
        }

        // 保存任务ID并开始轮询进度
        currentTaskId.value = response.task_id;
        console.log('视频生成任务已创建', currentTaskId.value);

        // 只有在获取到有效任务ID时才开始轮询
        if (currentTaskId.value && !currentTaskId.value.includes('undefined')) {
          startProgressPolling(currentTaskId.value);
        } else {
          throw new Error('获取到无效的任务ID');
        }

      } catch (error) {
        console.error('生成视频失败:', error);
        handleGenerationFailed(error.message || '生成失败');
      }
    };

    // 开始进度轮询
    const startProgressPolling = async (taskId) => {
      try {
        console.log('开始任务轮询', taskId);
        progressMessage.value = '正在启动任务...';

        // 使用自动轮询模式
        progress.value = 5;
        progressMessage.value = '任务已创建，等待处理...';

        const finalStatus = await videoService.getVideoTaskProgress(taskId, true);
        console.log('自动轮询完成，最终状态', finalStatus);

        // 任务完成，处理结果
        if (finalStatus.status === 'completed' || finalStatus.status === 'COMPLETED' ||
            finalStatus.status === 'SUCCESS') {
          await handleGenerationComplete(taskId);
        } else {
          handleGenerationFailed(finalStatus.error || '生成失败');
        }
      } catch (error) {
        console.error('轮询失败', error);
        handleGenerationFailed(error.message || '轮询失败');
      }
    };

    // 处理生成完成
    const handleGenerationComplete = async (taskId) => {
      try {
        console.log('开始获取任务详情', taskId);
        const taskDetails = await videoService.getVideoTask(taskId);
        console.log('获取到任务详情', taskDetails);

        // 更新状态
        isGenerating.value = false;
        progress.value = 100;

        // 获取视频URL
        let finalVideoUrl = taskDetails.data?.video_url || videoService.getVideoUrl(taskId);

        // 确保URL是绝对路径
        if (!finalVideoUrl.startsWith('http')) {
          finalVideoUrl = `${window.location.origin}${finalVideoUrl}`;
        }

        videoUrl.value = finalVideoUrl;
        ElMessage.success('视频生成成功!');

        // 刷新历史记录
        loadVideoHistory();

      } catch (error) {
        console.error('获取视频详情失败:', error);
        handleGenerationFailed('获取视频失败');
      }
    };

    // 处理生成失败
    const handleGenerationFailed = (errorMessage) => {
      isGenerating.value = false;
      progress.value = 0;
      progressMessage.value = '生成失败';
      ElMessage.error(errorMessage || '视频生成失败');
    };

    // 加载视频历史记录
    const loadVideoHistory = async () => {
      try {
        console.log('加载视频历史记录');
        const response = await videoService.getVideoTasks({ limit: 20 });

        if (response && response.data) {
          videoHistory.value = response.data.map(task => ({
            id: task.id,
            prompt: task.prompt || '无提示词',
            duration: task.duration || 10,
            resolution: task.resolution || '720p',
            createdAt: new Date(task.created_at).toLocaleDateString(),
            thumbnailUrl: task.thumbnail_url || videoService.getThumbnailUrl(task.id),
            videoUrl: task.video_url || videoService.getVideoUrl(task.id)
          }));
        }
      } catch (error) {
        console.error('加载历史记录失败:', error);
        videoHistory.value = [];
      }
    };

    // 下载视频
    const downloadVideo = async () => {
      if (!currentTaskId.value) {
        ElMessage.warning('没有可下载的视频');
        return;
      }

      try {
        const filename = `video_${Date.now()}.mp4`;
        await videoService.downloadVideo(currentTaskId.value, filename);
        ElMessage.success('视频下载已开始');
      } catch (error) {
        console.error('下载视频失败:', error);
        ElMessage.error('下载失败');
      }
    };

    // 重新生成视频
    const regenerateVideo = () => {
      if (!prompt.value.trim()) {
        ElMessage.warning('请先输入提示词');
        return;
      }
      generateVideo();
    };

    // 复制提示词
    const copyPrompt = async () => {
      if (!prompt.value.trim()) {
        ElMessage.warning('没有可复制的提示词');
        return;
      }

      try {
        await navigator.clipboard.writeText(prompt.value);
        ElMessage.success('提示词已复制到剪贴板');
      } catch (error) {
        console.error('复制失败:', error);
        ElMessage.error('复制失败');
      }
    };

    // 加载历史记录项
    const loadHistoryItem = (item) => {
      prompt.value = item.prompt;
      videoUrl.value = item.videoUrl;
      ElMessage.info('已加载历史记录');
    };

    // 视频加载完成处理
    const handleVideoLoaded = (event) => {
      console.log('视频加载完成', event);
      videoWorks.value = true;
    };

    // 视频加载错误处理
    const handleVideoError = (event) => {
      console.error('视频加载错误', event);
      videoWorks.value = false;
      ElMessage.error('视频加载失败，可能文件不存在或格式不支持');
    };

    // 检查视频有效性
    const checkVideoValidity = async (event) => {
      if (event.target.tagName === 'VIDEO' && videoUrl.value) {
        const isAccessible = await videoService.checkVideoAccessibility(videoUrl.value);
        if (!isAccessible) {
          ElMessage.warning('视频文件可能不可访问，请稍后重试');
        }
      }
    };

    // 修复黑屏问题
    const fixBlackScreenIssue = async () => {
      try {
        ElMessage.info('正在尝试修复黑屏问题...');
        const response = await videoService.fixBlackScreenIssue();

        if (response && response.success) {
          ElMessage.success('黑屏问题修复完成');
          // 重新加载历史记录
          loadVideoHistory();
        } else {
          ElMessage.warning('修复可能未完全成功，请重试');
        }
      } catch (error) {
        console.error('修复黑屏问题失败:', error);
        ElMessage.error('修复失败，请联系技术支持');
      }
    };

    return {
      // 状态
      prompt,
      negativePrompt,
      videoUrl,
      isGenerating,
      progress,
      progressMessage,
      videoHistory,
      videoParams,
      videoWorks,
      
      // 计算属性
      remainingTime,
      generationStatus,
      
      // 方法
      generateVideo,
      loadVideoHistory,
      downloadVideo,
      regenerateVideo,
      copyPrompt,
      loadHistoryItem,
      handleVideoLoaded,
      handleVideoError,
      fixBlackScreenIssue,
      checkVideoValidity
    };
  }
});
</script>

<style scoped>
.video-generation-container {
  padding: 20px;
  max-width: 1400px;
  margin: 0 auto;
}

.main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.input-section,
.preview-section {
  min-height: 600px;
}

.card-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.generate-btn {
  width: 100%;
  height: 50px;
  font-size: 16px;
}

.empty-container,
.generating-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  color: #ddd;
  margin-bottom: 20px;
}

.loading-spinner {
  font-size: 48px;
  color: #409eff;
  margin-bottom: 20px;
}

.generation-info h3 {
  margin: 0 0 20px 0;
  color: #333;
}

.progress-text {
  margin: 10px 0 5px 0;
  color: #666;
}

.time-text {
  margin: 0;
  color: #999;
  font-size: 14px;
}

.video-container {
  text-align: center;
}

.preview-video {
  width: 100%;
  max-width: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.video-actions {
  margin-top: 20px;
}

.history-section {
  margin-top: 30px;
}

.history-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
}

.history-item {
  display: flex;
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
}

.history-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

.history-thumbnail {
  width: 80px;
  height: 60px;
  border-radius: 4px;
  overflow: hidden;
  margin-right: 12px;
  background: #f5f7fa;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-thumbnail {
  color: #c0c4cc;
  font-size: 24px;
}

.history-info {
  flex: 1;
  min-width: 0;
}

.history-prompt {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #333;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.history-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #999;
}

.empty-history {
  text-align: center;
  padding: 40px 0;
}

@media (max-width: 768px) {
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .history-grid {
    grid-template-columns: 1fr;
  }
}
</style>
