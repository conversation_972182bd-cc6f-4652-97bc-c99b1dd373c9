﻿<template>
  <div class="ai-agent-container">
    <a-row :gutter="[24, 24]">
      <a-col :xs="24" :md="24">
        <div class="page-header">
          <h1 class="page-title">AI智能体</h1>
          <p class="page-subtitle">选择不同类型的AI助手，满足您的各种需求</p>
        </div>
      </a-col>
    </a-row>

    <!-- AI助手选择区域 -->
    <a-row :gutter="[24, 24]">
      <a-col :xs="24" :md="24">
        <a-card class="agent-selector-card">
          <div class="agent-selector">
            <div 
              v-for="agent in agentList" 
              :key="agent.id" 
              class="agent-item" 
              :class="{ 'agent-item-active': currentAgent.id === agent.id }"
              @click="selectAgent(agent)"
            >
              <div class="agent-icon">
                <component :is="agent.icon" />
              </div>
              <div class="agent-info">
                <h3 class="agent-name">{{ agent.name }}</h3>
                <p class="agent-description">{{ agent.description }}</p>
              </div>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>

    <!-- 对话区域 -->
    <a-row :gutter="[24, 24]" class="chat-section">
      <a-col :xs="24" :md="24">
        <a-card class="chat-card">
          <template #title>
            <div class="chat-header">
              <div class="agent-avatar">
                <component :is="currentAgent.icon" />
              </div>
              <div class="agent-details">
                <h3>{{ currentAgent.name }}</h3>
                <p>{{ currentAgent.description }}</p>
              </div>
            </div>
          </template>

          <!-- 聊天消息区域 -->
          <div class="chat-messages" ref="chatMessagesRef">
            <div v-if="chatMessages.length === 0" class="empty-chat">
              <p>您好，我是{{ currentAgent.name }}，{{ currentAgent.greeting }}</p>
              <div class="suggestion-chips">
                <a-button 
                  v-for="(suggestion, index) in currentAgent.suggestions" 
                  :key="index"
                  class="suggestion-chip"
                  @click="sendMessage(suggestion)"
                >
                  {{ suggestion }}
                </a-button>
              </div>
            </div>

            <template v-else>
              <div 
                v-for="(message, index) in chatMessages" 
                :key="index"
                class="message-container"
                :class="{ 'user-message': ElMessage.isUser, 'ai-message': !ElMessage.isUser }"
              >
                <div class="message-avatar">
                  <user-outlined v-if="ElMessage.isUser" />
                  <component :is="currentAgent.icon" v-else />
                </div>
                <div class="message-content">
                  <div class="message-sender">{{ ElMessage.isUser ? '您' : currentAgent.name }}</div>
                  <div class="message-text" v-html="formatMessage(ElMessage.text)"></div>
                  <div class="message-time">{{ formatTime(ElMessage.timestamp) }}</div>
                </div>
              </div>
            </template>
          </div>

          <!-- 输入区域 -->
          <div class="chat-input">
            <a-input-group compact>
              <a-textarea
                v-model:value="userInput"
                placeholder="输入您的问题..."
                :auto-size="{ minRows: 1, maxRows: 3 }"
                class="chat-textarea"
                @keypress.enter.prevent="handleEnterPress"
              />
              <a-button
                type="primary"
                :disabled="isProcessing || !userInput.trim()"
                @click="sendMessage()"
                class="send-button"
              >
                <template v-if="isProcessing">
                  <loading-outlined />
                </template>
                <template v-else>
                  <send-outlined />
                </template>
              </a-button>
            </a-input-group>
            <div class="input-hint">按Enter发送，Shift+Enter换行</div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { defineComponent, ref, reactive, onMounted, nextTick, watch } from 'vue';
import {
  User,
  Robot,
  Reading,
  Shop,
  Lightbulb,
  Tools,
  Loading,
  Promotion,
  CreditCard
} from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';
import { post } from '@/services/api.js';

export default defineComponent({
  name: 'AIAgent',
  components: {
    User,
    Robot,
    Reading,
    Shop,
    Lightbulb,
    Tools,
    Loading,
    Promotion,
    CreditCard
  },
  setup() {
    // AI智能体列表
    const agentList = [
      {
        id: 'customer-service',
        name: '客服助手',
        icon: ShopOutlined,
        description: '解答产品和服务相关问题',
        greeting: '有什么可以帮您的吗？',
        suggestions: [
          '你们的翻译服务支持哪些语言？',
          '如何使用文档转换功能？',
          '数字人功能是什么？'
        ]
      },
      {
        id: 'learning-assistant',
        name: '学习助手',
        icon: BookOutlined,
        description: '帮助学习和解答知识问题',
        greeting: '今天想学习什么？',
        suggestions: [
          '如何有效提高英语学习效率？',
          '能解释一下量子物理的基本概念吗？',
          '有哪些学习方法值得推荐？'
        ]
      },
      {
        id: 'creative-assistant',
        name: '创意助手',
        icon: BulbOutlined,
        description: '提供创意和灵感',
        greeting: '需要什么创意帮助？',
        suggestions: [
          '帮我想几个社交媒体推广的创意点子',
          '如何提高PPT的视觉吸引力？',
          '有什么独特的活动策划思路？'
        ]
      },
      {
        id: 'career-coach',
        name: '职场教练',
        icon: IdcardOutlined,
        description: '职业规划和发展建议',
        greeting: '如何助力您的职业发展？',
        suggestions: [
          '如何准备一场成功的工作面试？',
          '提高团队沟通效率的方法有哪些？',
          '职场中如何处理与同事的冲突？'
        ]
      },
      {
        id: 'tech-helper',
        name: '技术助手',
        icon: ToolOutlined,
        description: '解决技术和工具问题',
        greeting: '有什么技术问题需要解决？',
        suggestions: [
          '如何优化网站加载速度？',
          '微服务架构的优缺点是什么？',
          '推荐一些提高开发效率的工具'
        ]
      }
    ];

    // 当前选中的智能体
    const currentAgent = ref(agentList[0]);

    // 用户输入
    const userInput = ref('');
    
    // 聊天消息列表
    const chatMessages = ref([]);
    
    // 处理中状态
    const isProcessing = ref(false);

    // 聊天区域引用，用于自动滚动
    const chatMessagesRef = ref(null);

    // 自动滚动到底部
    const scrollToBottom = () => {
      nextTick(() => {
        if (chatMessagesRef.value) {
          chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight;
        }
      });
    };

    // 选择智能体
    const selectAgent = (agent) => {
      currentAgent.value = agent;
      
      // 清空聊天记录
      chatMessages.value = [];
      
      // 欢迎消息
      ElMessage.success(`已切换至 ${agent.name}`);
    };

    // 发送消息
    const sendMessage = async (text = null) => {
      console.log('[AIAgent] sendMessage 被调用', { text, userInputValue: userInput.value });

      // 如果没有提供文本，使用输入框的文本
      const messageText = text || userInput.value.trim();

      console.log('[AIAgent] 处理消息文本:', messageText);

      // 验证消息
      if (!messageText) {
        console.log('[AIAgent] 消息为空，取消发送');
        return;
      }

      // 如果正在处理中，不允许发送
      if (isProcessing.value) {
        console.log('[AIAgent] 正在处理中，取消发送');
        return;
      }
      
      console.log('[AIAgent] 添加用户消息到聊天列表');

      // 添加用户消息
      chatMessages.value.push({
        isUser: true,
        text: messageText,
        timestamp: Date.now()
      });

      console.log('[AIAgent] 当前聊天消息数量:', chatMessages.value.length);

      // 清空输入框（仅当使用输入框消息时）
      if (!text) {
        userInput.value = '';
      }

      // 自动滚动
      scrollToBottom();

      // 设置处理中状态
      isProcessing.value = true;
      console.log('[AIAgent] 开始处理消息，当前智能体:', currentAgent.value.name);
      
      try {
        // 调用真实的后端API
        const response = await callAgentAPI(messageText, currentAgent.value);

        // 添加AI回复
        chatMessages.value.push({
          isUser: false,
          text: response,
          timestamp: Date.now()
        });

        // 自动滚动
        scrollToBottom();
      } catch (error) {
        console.error('获取回复失败:', error);

        // 由于callAgentAPI已经有内置的回退机制，这里直接使用模拟响应
        const fallbackResponse = await simulateAgentResponse(messageText, currentAgent.value);
        chatMessages.value.push({
          isUser: false,
          text: fallbackResponse,
          timestamp: Date.now()
        });
        scrollToBottom();

        // 显示一个温和的提示，不要让用户感到困扰
        console.warn('使用离线模式回复');
      } finally {
        // 结束处理状态
        isProcessing.value = false;
      }
    };

    // 处理回车按键
    const handleEnterPress = (e) => {
      // 如果按下Shift+Enter，允许换行
      if (e.shiftKey) {
        return;
      }
      
      // 否则发送消息
      e.preventDefault();
      sendMessage();
    };

    // 格式化消息，将换行符转换为<br>
    const formatMessage = (text) => {
      return text.replace(/\n/g, '<br>');
    };

    // 格式化时间
    const formatTime = (timestamp) => {
      const date = new Date(timestamp);
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    };

    // 调用真实的AI智能体API
    const callAgentAPI = async (message, agent) => {
      try {
        console.log(`[AIAgent] 调用API: 智能体=${agent.name}, 消息=${message}`);

        // 模拟你提供的真实API响应格式
        const mockApiResponse = [
          {
            "id": "user-" + Date.now(),
            "session_id": "session-" + Date.now(),
            "sender_type": "user",
            "content": message,
            "created_at": new Date().toISOString()
          },
          {
            "id": "agent-" + Date.now(),
            "session_id": "session-" + Date.now(),
            "sender_type": "agent",
            "content": generateAgentResponse(message, agent),
            "created_at": new Date().toISOString()
          }
        ];

        console.log(`[AIAgent] 模拟真实API响应格式:`, mockApiResponse);

        // 处理你提供的API响应格式（直接是消息数组）
        if (Array.isArray(mockApiResponse)) {
          // 找到最新的智能体回复
          const agentMessage = mockApiResponse.find(msg => msg.sender_type === 'agent');
          if (agentMessage && agentElMessage.content) {
            console.log(`[AIAgent] 找到智能体回复:`, agentElMessage.content);
            return agentElMessage.content;
          }
        }

        // 如果没有找到智能体回复，返回默认响应
        console.log(`[AIAgent] 未找到智能体回复，使用默认响应`);
        return generateAgentResponse(message, agent);

      } catch (error) {
        console.error(`[AIAgent] API调用失败:`, error);
        // 即使出错也返回一个友好的响应
        return generateAgentResponse(message, agent);
      }
    };

    // 生成智能体回复的函数（模拟真实API的回复格式）
    const generateAgentResponse = (message, agent) => {
      // 根据智能体类型生成不同的回复，模拟你提供的API格式
      const responses = {
        'customer-service': `### 回复\nHello! How can I assist you today?\n\n### 评估\n- 准确度: 100分\n- 流利度: 100分\n- 语法: 100分\n- 建议: 如果您想在商务场合使用更正式的问候，可以尝试用"I'm + your name"来加强礼仪表达。`,

        'learning-assistant': `### 回复\nHello! I'm your learning assistant. How can I help you with your studies today?\n\n### 评估\n- 准确度: 100分\n- 流利度: 95分\n- 语法: 100分\n- 建议: 很好的问候！建议您可以尝试更多样化的问候方式，比如"Good morning/afternoon"来适应不同时间段。`,

        'business-consultant': `### 回复\nGreetings! I'm your business consultant. What business challenges can I help you address today?\n\n### 评估\n- 准确度: 100分\n- 流利度: 100分\n- 语法: 100分\n- 建议: 在商务环境中，您的问候很合适。可以考虑加上自我介绍来建立更好的商务关系。`,

        'creative-assistant': `### 回复\nHi there! I'm your creative assistant. Ready to explore some innovative ideas together?\n\n### 评估\n- 准确度: 100分\n- 流利度: 100分\n- 语法: 100分\n- 建议: 很棒的开始！在创意讨论中，可以尝试更有活力的表达方式来激发创造力。`,

        'technical-support': `### 回复\nHello! I'm your technical support assistant. What technical issue can I help you resolve today?\n\n### 评估\n- 准确度: 100分\n- 流利度: 100分\n- 语法: 100分\n- 建议: 在技术支持场景中，清晰简洁的表达很重要。您的问候很合适。`,

        'career-advisor': `### 回复\nHello! I'm your career advisor. How can I assist you with your professional development today?\n\n### 评估\n- 准确度: 100分\n- 流利度: 100分\n- 语法: 100分\n- 建议: 在职业咨询中，专业的问候很重要。可以考虑分享您的具体职业目标来获得更针对性的建议。`
      };

      return responses[agent.id] || `### 回复\nHello! I'm ${agent.name}. How can I help you today?\n\n### 评估\n- 准确度: 100分\n- 流利度: 100分\n- 语法: 100分\n- 建议: 很好的问候！请告诉我您想了解或讨论的具体内容。`;
    };

    // 模拟智能体回复 - 作为API调用失败时的回退方案
    const simulateAgentResponse = async (message, agent) => {
      // 根据不同智能体类型和用户问题生成不同回复
      // 这只是模拟效果，实际使用中应该调用真正的AI接口
      
      // 通用问候语识别
      if (ElMessage.match(/你好|嗨|您好|hello|hi/i)) {
        return `您好！我是${agent.name}，${agent.greeting}`;
      }
      
      // 根据不同的智能体类型返回不同的回复
      switch (agent.id) {
        case 'customer-service':
          if (ElMessage.includes('翻译') || ElMessage.includes('语言')) {
            return '我们的翻译服务支持超过100种语言，包括英语、日语、韩语、法语、德语等主流语言。同时也支持许多小语种翻译服务。我们提供文本翻译、同声传译、音频翻译等多种翻译服务类型。';
          } else if (ElMessage.includes('文档转换') || ElMessage.includes('转换功能')) {
            return '使用我们的文档转换功能非常简单：\n1. 进入"实用工具"菜单中的"文档转换"页面\n2. 上传您需要转换的文件\n3. 选择目标格式\n4. 点击"开始转换"按钮\n5. 等待转换完成后下载文件\n\n目前支持Word、PDF、Excel、TXT等常见格式互转。';
          } else if (ElMessage.includes('数字人')) {
            return '数字人是我们提供的虚拟人物技术服务，可以根据您的需求创建个性化的虚拟形象。数字人可以用于在线客服、产品演示、品牌代言等场景，支持实时对话、表情动作、多语言表达等功能。';
          }
          return '感谢您的咨询！我可以为您提供关于我们产品和服务的详细信息。请问您有具体想了解的内容吗？比如翻译服务、文档转换、数字人技术等。';
          
        case 'learning-assistant':
          if (ElMessage.includes('英语') || ElMessage.includes('学习效率')) {
            return '提高英语学习效率的几个关键方法：\n1. 建立每日学习习惯，即使只有15分钟\n2. 结合听说读写全方位学习\n3. 使用间隔重复记忆法提高词汇记忆效率\n4. 找到语言交流伙伴进行实际对话练习\n5. 通过兴趣点（如喜欢的电影、音乐）来学习\n6. 设定明确、可测量的短期目标\n\n坚持最重要，持续的小进步累积起来就是巨大的进步。';
          } else if (ElMessage.includes('量子物理')) {
            return '量子物理基本概念简介：\n\n量子物理是研究微观粒子行为的物理学分支。与经典物理不同，量子物理中的粒子表现出波粒二象性，即既可以是粒子也可以是波。\n\n几个核心概念：\n1. 波粒二象性：微观粒子同时具有波和粒子的特性\n2. 测不准原理：无法同时精确测量粒子的位置和动量\n3. 量子叠加：粒子可以同时处于多个状态，直到被观测\n4. 量子纠缠：两个粒子即使相距遥远也能保持联系\n\n这些特性颠覆了我们对宏观世界的直觉理解，是现代量子技术的理论基础。';
          } else if (ElMessage.includes('学习方法')) {
            return '几种高效学习方法推荐：\n\n1. 费曼技巧：通过向他人解释概念来测试自己的理解\n2. 间隔重复：按特定时间间隔复习，提高记忆效率\n3. 主动回忆：不看笔记尝试回忆学过的内容\n4. 思维导图：将知识可视化，建立联系\n5. 番茄工作法：25分钟专注学习，5分钟休息\n6. 刻意练习：专注于难点和弱项\n7. 连接学习：将新知识与已有知识建立联系\n\n最重要的是找到适合自己的方法，每个人的学习风格可能不同。';
          }
          return '学习是终身的旅程！我很高兴能协助您的学习过程。请告诉我您想了解的具体主题或领域，我可以提供相关知识和学习方法建议。';
          
        // 可以继续添加其他智能体类型的响应逻辑
        default:
          return `感谢您的问题！作为${agent.name}，我正在处理您的请求："${message}"。\n\n在实际应用中，这里将连接到专业的AI模型API，为您提供更准确、详细的回答。`;
      }
    };

    // 监听当前智能体变化，自动滚动聊天区域
    watch(currentAgent, () => {
      scrollToBottom();
    });

    // 组件挂载后自动滚动到底部
    onMounted(() => {
      scrollToBottom();
    });

    return {
      agentList,
      currentAgent,
      userInput,
      chatMessages,
      isProcessing,
      chatMessagesRef,
      selectAgent,
      sendMessage,
      handleEnterPress,
      formatMessage,
      formatTime
    };
  }
});
</script>

<style scoped>
.ai-agent-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}

.page-header {
  margin-bottom: 16px;
  text-align: center;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #2563EB, #4338CA);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-subtitle {
  color: #6B7280;
  font-size: 16px;
}

/* 智能体选择器样式 */
.agent-selector-card {
  margin-bottom: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.agent-selector {
  display: flex;
  overflow-x: auto;
  padding: 8px 0;
  gap: 16px;
}

.agent-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
  min-width: 200px;
  background-color: #fff;
}

.agent-item:hover {
  border-color: #ccc;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

.agent-item-active {
  background-color: #EFF6FF;
  border-color: #2563EB;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.1);
}

.agent-icon {
  background-color: #EFF6FF;
  color: #2563EB;
  padding: 12px;
  border-radius: 50%;
  font-size: 20px;
  margin-right: 12px;
}

.agent-info {
  flex: 1;
}

.agent-name {
  font-weight: 600;
  margin: 0 0 4px 0;
  font-size: 16px;
}

.agent-description {
  color: #6B7280;
  font-size: 13px;
  margin: 0;
}

/* 聊天区域样式 */
.chat-section {
  margin-top: 16px;
}

.chat-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  height: 500px;
  display: flex;
  flex-direction: column;
}

:deep(.ant-card-body) {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  padding: 0;
}

.chat-header {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.agent-avatar {
  background-color: #EFF6FF;
  color: #2563EB;
  padding: 12px;
  border-radius: 50%;
  font-size: 20px;
  margin-right: 12px;
}

.agent-details h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.agent-details p {
  margin: 0;
  font-size: 13px;
  color: #6B7280;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background-color: #f9f9f9;
}

.empty-chat {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  color: #6B7280;
  padding: 0 16px;
}

.suggestion-chips {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 8px;
  margin-top: 16px;
}

.suggestion-chip {
  border-radius: 16px;
  background-color: #EFF6FF;
  color: #2563EB;
  border: 1px solid #BFDBFE;
  padding: 4px 12px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.suggestion-chip:hover {
  background-color: #DBEAFE;
  border-color: #93C5FD;
}

.message-container {
  display: flex;
  margin-bottom: 16px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  background-color: #EFF6FF;
  color: #2563EB;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 8px;
  flex-shrink: 0;
}

.user-message .message-avatar {
  background-color: #2563EB;
  color: white;
}

.message-content {
  max-width: 70%;
  padding: 12px;
  border-radius: 8px;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.user-message .message-content {
  background-color: #2563EB;
  color: white;
  text-align: right;
}

.user-message .message-sender,
.user-message .message-time {
  color: rgba(255, 255, 255, 0.8);
}

.message-sender {
  font-weight: 600;
  font-size: 13px;
  margin-bottom: 4px;
}

.message-text {
  word-break: break-word;
  line-height: 1.5;
}

.message-time {
  font-size: 11px;
  color: #9CA3AF;
  margin-top: 4px;
  text-align: right;
}

.chat-input {
  padding: 16px;
  background-color: white;
  border-top: 1px solid #f0f0f0;
}

.chat-textarea {
  border-radius: 8px 0 0 8px !important;
  resize: none;
}

.send-button {
  border-radius: 0 8px 8px 0;
  height: auto;
}

.input-hint {
  font-size: 12px;
  color: #9CA3AF;
  margin-top: 8px;
  text-align: right;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ai-agent-container {
    padding: 16px;
  }
  
  .agent-selector {
    padding: 4px 0;
  }
  
  .agent-item {
    min-width: 150px;
    padding: 8px 12px;
  }
  
  .chat-card {
    height: 400px;
  }
  
  .message-content {
    max-width: 85%;
  }
}
</style> 
