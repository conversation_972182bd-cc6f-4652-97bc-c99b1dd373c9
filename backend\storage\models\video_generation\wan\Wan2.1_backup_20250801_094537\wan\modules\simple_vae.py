#!/usr/bin/env python
# -*- coding: utf-8 -*-

import torch
import torch.nn as nn
import torch.nn.functional as F
import logging

class SimpleVAE(nn.Module):
    """
    简单的VAE实现，用于加载WAN 2.1模型的VAE权重
    """
    def __init__(self):
        super().__init__()
        self.latent_channels = 4
        
        # 创建简单的编码器和解码器，避免复杂结构导致递归问题
        self.encoder = nn.Sequential(
            nn.Conv2d(3, 64, kernel_size=3, stride=2, padding=1),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(64, 128, kernel_size=3, stride=2, padding=1),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(128, 256, kernel_size=3, stride=2, padding=1),
            nn.LeakyReLU(0.2, inplace=True),
            nn.Conv2d(256, self.latent_channels * 2, kernel_size=3, padding=1)
        )
        
        self.decoder = nn.Sequential(
            nn.Conv2d(self.latent_channels, 256, kernel_size=3, padding=1),
            nn.LeakyReLU(0.2, inplace=True),
            nn.ConvTranspose2d(256, 128, kernel_size=4, stride=2, padding=1),
            nn.LeakyReLU(0.2, inplace=True),
            nn.ConvTranspose2d(128, 64, kernel_size=4, stride=2, padding=1),
            nn.LeakyReLU(0.2, inplace=True),
            nn.ConvTranspose2d(64, 3, kernel_size=4, stride=2, padding=1),
            nn.Tanh()
        )
        
    def encode(self, x):
        """
        编码函数，返回均值和方差
        """
        try:
            # 处理输入
            if len(x.shape) == 5:  # [B, C, T, H, W] 格式
                # 将时间维度折叠到批次维度
                b, c, t, h, w = x.shape
                x = x.permute(0, 2, 1, 3, 4).reshape(b * t, c, h, w)
                
            result = self.encoder(x)
            # 分离均值和对数方差
            mean, logvar = torch.chunk(result, 2, dim=1)
            return mean, logvar
            
        except Exception as e:
            logging.error(f"Error in SimpleVAE encode: {str(e)}")
            # 返回随机张量作为备用
            batch_size = x.shape[0] if hasattr(x, 'shape') and len(x.shape) > 0 else 1
            h = x.shape[2] // 8 if hasattr(x, 'shape') and len(x.shape) > 2 else 32
            w = x.shape[3] // 8 if hasattr(x, 'shape') and len(x.shape) > 3 else 32
            z = torch.randn((batch_size, self.latent_channels, h, w), device=x.device) * 0.1
            logvar = torch.zeros_like(z)
            return z, logvar
    
    def decode(self, z):
        """
        解码函数
        """
        try:
            # 处理输入
            if isinstance(z, list) and len(z) > 0:
                z = z[0]
            
            # 记录输入形状
            logging.info(f"VAE decode input shape: {z.shape}")
            
            # 确保输入不是全零或全接近零
            if z.abs().max() < 1e-6:
                logging.warning("SimpleVAE 输入几乎为零，添加随机噪声")
                z = z + torch.randn_like(z) * 0.1
            
            # 检查通道数是否为4
            if z.shape[1] != self.latent_channels:
                logging.warning(f"输入通道数不匹配 (expected {self.latent_channels}, got {z.shape[1]}), 尝试调整")
                if z.shape[1] < self.latent_channels:
                    # 通道数不足，需要填充
                    padding = torch.zeros((z.shape[0], self.latent_channels - z.shape[1], *z.shape[2:]), device=z.device)
                    z = torch.cat([z, padding], dim=1)
                    logging.info(f"通道数不足，填充后形状: {z.shape}")
                else:
                    # 通道数过多，需要截断
                    z = z[:, :self.latent_channels]
                    logging.info(f"通道数过多，截断后形状: {z.shape}")
            
            # 执行解码
            decoded = self.decoder(z)
            logging.info(f"VAE decode output shape: {decoded.shape}")
            
            # 检查输出是否全零或接近全零
            if decoded.abs().max() < 1e-3:
                logging.warning("SimpleVAE 解码输出几乎为零，添加随机噪声")
                # 添加随机噪声并确保范围合适
                decoded = decoded + torch.randn_like(decoded) * 0.2
                # 重新映射到[-1, 1]范围
                decoded = torch.tanh(decoded)
                logging.info(f"Added noise to SimpleVAE output, new range: [{decoded.min().item():.4f}, {decoded.max().item():.4f}]")
            
            # 确保张量值在合理范围内
            if decoded.min() < -1.0 or decoded.max() > 1.0:
                logging.warning(f"SimpleVAE 输出范围异常: [{decoded.min().item():.4f}, {decoded.max().item():.4f}]，进行归一化")
                decoded = torch.tanh(decoded)
                logging.info(f"归一化后范围: [{decoded.min().item():.4f}, {decoded.max().item():.4f}]")
            
            return decoded
            
        except Exception as e:
            logging.error(f"Error in SimpleVAE decode: {str(e)}")
            # 提供详细的异常信息
            import traceback
            logging.error(traceback.format_exc())
            
            # 返回随机张量作为备用
            try:
                batch_size = z.shape[0] if hasattr(z, 'shape') and len(z.shape) > 0 else 1
                h = z.shape[2] * 8 if hasattr(z, 'shape') and len(z.shape) > 2 else 256
                w = z.shape[3] * 8 if hasattr(z, 'shape') and len(z.shape) > 3 else 256
            except (IndexError, AttributeError):
                batch_size = 1
                h = 256
                w = 256
                
            # 生成随机噪声作为替代输出，使用合理的初始化值范围
            random_output = torch.rand((batch_size, 3, h, w), device=z.device if hasattr(z, 'device') else 'cpu') * 0.6 - 0.3
            logging.warning(f"生成随机噪声作为VAE输出: 形状=({batch_size}, 3, {h}, {w})")
            return random_output
    
    def forward(self, x):
        """
        前向传播函数
        """
        mean, logvar = self.encode(x)
        # 采样隐变量
        eps = torch.randn_like(mean)
        z = mean + torch.exp(0.5 * logvar) * eps
        return self.decode(z) 